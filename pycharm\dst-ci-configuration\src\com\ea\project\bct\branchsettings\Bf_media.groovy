package com.ea.project.bct.branchsettings

class Bf_media {
    // Settings for jobs
    static Class project = com.ea.project.bct.Bct
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map code_settings = [:]
    static Map data_settings = [:]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                          : 'Marketing/GA_Preflight_bf-media',
        extra_code_path                : 'mainline/trunk-content-dev',
        extra_locations_bilbo_drone_job: [],
        extra_data_args                : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args              : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        import_avalanche_state         : false,
        move_location_parallel         : true,
        new_locations                  : [
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        poolbuild_data                 : true,
        server_asset                   : 'Marketing/GA_Preflight_bf-media',
        slack_channel_code             : [
            channels                  : ['#bf-media'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_data             : 5,
        skip_code_build_if_no_changes  : false,
        strip_symbols                  : false,
        statebuild_code                : false,
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        'win64',
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
