# pylint: disable=too-many-lines
"""
core.py

Core module of the ELIPY package.
Contains implementation of various low level functions that
interact with i.e. the operating system or for running external applications.
"""
from __future__ import absolute_import
import contextlib
import csv
import hashlib
import importlib
import json
import uuid
import os
import requests
import subprocess
import multiprocessing
import retry
import sys
import shutil
import stat
import time
import fnmatch
from collections.abc import Iterator
from json import JSONDecodeError
from pathlib import Path
from threading import Thread
from typing import List, Union
from elipy2.command_logger import CommandLogger
from elipy2.exceptions import (
    ConfigValueNotFoundException,
    CoreException,
    EapmException,
    ELIPYException,
)
from elipy2.telemetry import collect_metrics
from elipy2 import (
    frostbite_core,
    LOGGER,
    multiprocessing as elipy_mp,
    p4,
    run as elipy_run,
    SETTINGS,
)
from contextlib import contextmanager

PIPE = -1
STDOUT = -2


def clean_temp(allow_failure=True):
    """
    Cleans out %TEMP% on the machine.
    """
    try:
        delete_folder_with_robocopy(os.environ.get("TEMP"))
    except WindowsError:  # pylint: disable=undefined-variable
        if allow_failure:
            LOGGER.warning(
                "Unable to fully clean out %TEMP%,\
                this is expected since some files might be in use."
            )
        else:
            raise


def get_licensee_code_folder():
    """
    Returns the Licensee Code Folder.

    :returns: Relative Licensee Code Folder path as defined in the elipy project config
    :raises ConfigValueNotFoundException: licensee_code_folder_name is not set in elipy config.
    """
    try:
        return SETTINGS.get("licensee_code_folder_name")
    except ConfigValueNotFoundException:
        return "Code"


def import_module_from_file(module_name, file_path):
    "https://docs.python.org/3/library/importlib.html#importing-a-source-file-directly"
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module


def update_shell():
    """
    update shell with new vars after package installation
    """
    import_module_from_file(
        "updateshell",
        os.path.join(frostbite_core.get_tnt_root(), "bin", "fbcli", "core", "updateshell.py"),
    ).recalculate_shell_vars()


def use_bilbo():
    """
    Function to determine if bilbo should be used
    :returns: True if bilbo_url in settings config
    """
    try:
        return SETTINGS.get("bilbo_url") is not None
    except ConfigValueNotFoundException:
        return False


@contextmanager
def patch_environ(env_patch: dict):
    """
    Temporarily set environment variables inside the context manager and
    fully restore previous environment afterward
    """
    env_patch = env_patch if env_patch else {}
    env_patch = {key: value for key, value in env_patch.items() if value}
    original_env = {key: os.getenv(key) for key in env_patch}
    os.environ.update(env_patch)
    try:
        yield
    finally:
        for key, value in original_env.items():
            if value is None:
                del os.environ[key]
            else:
                os.environ[key] = value


@collect_metrics()
def run(
    cmd,
    working_dir=None,
    allow_non_zero_exit_code=False,
    print_std_out=False,
    capture_std_out=True,
    write_std_out=False,
    env_patch=None,
):
    """
    Expose run as a public method. Optionally "allow" programs to run without returning 0 (default
    is we raise exception) in case the user wants to parse exit codes (i.e. robocopy returns a
    bitmask). BFA can use "failed with error message:" to highlights stderr

    :param cmd: The command and arguments you want to execute
    :param working_dir: What is the working directory for you command
    :param allow_non_zero_exit_code: Allow non-zero exist codes
    :param print_std_out: Print the command output to std out
    :param capture_std_out: Save the command output into variables
    :param write_std_out: Save the command output into text file
    :param env_patch: Dictionary of key:values to set in the environment for you command
    :returns: exitcode, stdout and stderr in format string (if capture enabled)
    """
    cmd = [str(a) for a in cmd]
    cmd_str = " ".join(cmd)

    if print_std_out:
        LOGGER.start_group("Run external command: {0}".format(cmd_str))
        LOGGER.command("Running external command: {0}".format(cmd_str))
    elif cmd:
        LOGGER.start_group("Run external command: {0}".format(cmd[0]))
        LOGGER.command("Running external command: {0}".format(cmd[0]))

    base_log_path = _get_log_directory_path()
    successful_execution = True
    with patch_environ(env_patch):
        with CommandLogger(cmd_str, base_log_path) as cmd_logger:
            exit_code, stdout, stderr = _run(
                cmd,
                working_dir=working_dir,
                print_std_out=print_std_out,
                capture_std_out=capture_std_out,
                write_std_out=write_std_out,
            )

            successful_execution = exit_code == 0 or allow_non_zero_exit_code
            LOGGER.info("External command finished with exit code: {0}".format(exit_code))
            cmd_logger.finish_command_log(successful_execution, exit_code)

    if not successful_execution:
        executor = cmd[0].lower()
        if executor.endswith("eapm.exe"):
            LOGGER.end_group()
            raise EapmException(exit_code)
        else:
            single_fmt = "\n\t"
            double_fmt = single_fmt + "\t"

            LOGGER.end_group()
            raise CoreException(
                "`{0}` failed with{4}exit code: {1}{4}stdout: {5}{2}{4}stderr: {5}{3}".format(
                    " ".join(cmd),
                    exit_code,
                    double_fmt.join(stdout),
                    double_fmt.join(stderr),
                    single_fmt,
                    double_fmt,
                )
            )

    LOGGER.end_group()
    return exit_code, stdout, stderr


def decode(line):
    """
    Handles decoding of line from UTF-8 or UTF-16-LE
    (Output encoding seen from Sony Tools e.g. orbis-symupload.exe)
    """
    decoded_line = line.decode("utf-8", "ignore").replace("\x00", "").rstrip("\r")
    return decoded_line


def _get_ado_temp_folder_path() -> Union[str, None]:
    """
    Return a temp folder name that is unique to this job but is still "well known"
    https://learn.microsoft.com/en-us/azure/devops/pipelines/build/variables?view=azure-devops&tabs=yaml

    :returns: folder path or None
    """
    # ADO ID
    temp_dir = os.environ.get("AGENT_TEMPDIRECTORY")

    folder_path = None
    if temp_dir:
        # because ADO provides a temp directory, there isn't a need for a separate folder name
        folder_path = os.path.join(temp_dir, "ELIPY")

    return folder_path


def _get_jenkins_temp_folder_path() -> Union[str, None]:
    """
    Return a temp folder path that is unique to this job but is still "well known"

    :returns: folder path or None
    """
    job_name = os.environ.get("JOB_NAME")
    build_id = os.environ.get("BUILD_ID")
    temp_dir = os.environ["TEMP"]

    folder_path = None
    if job_name and build_id:
        folder_path = os.path.join(temp_dir, "ELIPY", "jenkins", job_name, build_id)

    return folder_path


def _get_default_temp_folder_path() -> str:
    """
    Return a temp folder path that is used for running local builds

    :returns: folder path or None
    """
    temp_dir = os.environ["TEMP"]
    folder_path = os.path.join(temp_dir, "ELIPY", "local")
    return folder_path


def _get_build_temp_folder_path() -> str:
    """
    Get the temp folder name for this build

    :returns: folder name
    """
    default_folder_path = _get_default_temp_folder_path()
    ado_folder_path = _get_ado_temp_folder_path()
    jenkins_folder_path = _get_jenkins_temp_folder_path()
    prioritized_path = [default_folder_path, jenkins_folder_path, ado_folder_path]
    temp_folder_path = [folder_path for folder_path in prioritized_path if folder_path][-1]

    return temp_folder_path


def _get_log_directory_path() -> str:
    """
    Get the core log path

    :returns: str
    """
    temp_folder_path = _get_build_temp_folder_path()
    if not os.path.exists(temp_folder_path):
        os.makedirs(temp_folder_path)
    return temp_folder_path


def _get_log_file_path() -> str:
    """
    Get a path to a random log file

    :returns: str
    """
    return os.path.join(_get_log_directory_path(), "{0}.txt".format(str(uuid.uuid4())))


def _write_output(cmd, out, out_collect, print_std_out, capture_std_out, write_std_out):
    """
    Handles writing output from Popen to out_collect, convert from byte to str, line by line
    """
    log_file = None
    if write_std_out:
        try:
            log_file = _get_log_file_path()
            LOGGER.info("Output command logs to: {0}".format(log_file))
            with open(log_file, "w+") as file:
                file.write(" ".join(cmd))
                file.flush()
        except (TypeError, IOError) as exc:
            LOGGER.info("%s", exc)
            write_std_out = False

    with open(log_file, "a") if write_std_out else contextlib.nullcontext() as file:
        for line in iter(out.readline, b""):
            line = line.rstrip()
            if line == b"":
                continue
            decoded_line = decode(line)
            if print_std_out:
                sys.stdout.write(decoded_line + "\n")
            if capture_std_out:
                out_collect.append(decoded_line)
            if write_std_out:
                file.write(decoded_line + "\n")
                file.flush()

        out.close()


def _write_stderr(err, err_collect, print_std_out, capture_std_out):
    """
    Handles collecting output that was written to err pipe, to err_collect,
    line by line as str format
    """
    for line in iter(err.readline, b""):
        line = line.strip()
        if line == b"":
            continue
        decoded_line = decode(line)
        if print_std_out:
            sys.stderr.write(decoded_line + "\n")
        if capture_std_out:
            err_collect.append(decoded_line)

    err.close()


@collect_metrics()
def _run(cmd, working_dir=None, print_std_out=False, capture_std_out=False, write_std_out=False):
    """
    Handles logic for running external programs, wrapping and piping output.
    :params cmd: A list where the first element is the external application
        (absolute path if not already on PATH) and the rest of the
        elements are arguments to the application.
        Example: ["helloworld.exe", "-f", "something", "-b", "else"]
    :returns: tuple containing three elements (if capture enabled):
            exit code of external application,
            list of lines from stdout,
            list of lines from stderr.
    :raises CoreException: _run expected list but got {0}.
    """
    if not isinstance(cmd, list):
        raise CoreException("_run expected list but got {0}".format(type(cmd)))

    expanding_cmd = [os.path.expandvars(cmd_value) for cmd_value in cmd]

    proc = subprocess.Popen(  # pylint: disable=consider-using-with
        expanding_cmd, stdout=PIPE, stderr=PIPE, cwd=working_dir
    )

    out_collect = []
    err_collect = []

    to_thread = Thread(
        target=_write_output,
        args=[cmd, proc.stdout, out_collect, print_std_out, capture_std_out, write_std_out],
    )
    to_thread.start()

    te_thread = Thread(
        target=_write_stderr,
        args=[proc.stderr, err_collect, print_std_out, capture_std_out],
    )
    te_thread.start()

    LOGGER.info("Waiting for process to finish...")
    while proc.poll() is None:
        time.sleep(1)

    LOGGER.info("Waiting for threads to finish...")
    te_thread.join()
    to_thread.join()

    LOGGER.info("Process and threads are done.")

    return proc.returncode, out_collect, err_collect


def run_async(
    cmd, working_dir=None, print_std_out=False, capture_std_out=True, write_std_out=False
):
    """
    Handles logic for starting an external programs, wrapping and piping output.

    :params cmd: A list where the first element is the external application
        (absolute path if not already on PATH) and the rest of the
        elements are arguments to the application.

        Example: ["helloworld.exe", "-f", "something", "-b", "else"]
    :returns: A joinable elipy2.run.AsyncProcess object with the process already started
    :raises CoreException: _run_async expected list but got {0}.
    """
    if not isinstance(cmd, list):
        raise CoreException("_run_async expected list but got {0}".format(type(cmd)))

    LOGGER.info("Running external command: {0}".format(" ".join(cmd)))
    proc = subprocess.Popen(  # pylint: disable=consider-using-with
        cmd, stdout=PIPE, stderr=PIPE, cwd=working_dir
    )

    out_collect = []
    to_thread = Thread(
        target=_write_output,
        args=[cmd, proc.stdout, out_collect, print_std_out, capture_std_out, write_std_out],
    )
    to_thread.start()

    err_collect = []
    te_thread = Thread(
        target=_write_stderr, args=[proc.stderr, err_collect, print_std_out, capture_std_out]
    )
    te_thread.start()

    return elipy_run.AsyncProcess(cmd[0], proc, to_thread, te_thread, out_collect, err_collect)


@collect_metrics()
def robocopy(
    source,
    dest,
    single_file=False,
    purge=False,
    extra_args=[],
    quiet=False,
    include_empty_dirs=True,
    lock_file=None,
):
    """
    set purge == True for '/MIR'\n
    Invoke external robocopy.exe
    """
    handle_existing_files_args = ["/E"] if include_empty_dirs else ["/S"]

    if single_file:
        handle_existing_files_args = []

    if purge:
        handle_existing_files_args.append("/PURGE")

    if quiet:
        extra_args = extra_args + ["/NFL", "/NDL", "/NC", "/NS"]

    # check if source exists:
    if not os.path.exists(source):
        LOGGER.error("Source does not exist, cannot run Robocopy: %s", source)
        raise IOError("Source does not exist, cannot run Robocopy: {}".format(source))

    # Some external tools use the lock_file.
    if lock_file:
        os.makedirs(os.path.dirname(lock_file), exist_ok=True)

    # Pylint just doesn't seem to handle conditional with statements.
    with (
        open(lock_file, "w")  # pylint: disable=consider-using-with
        if lock_file
        else contextlib.nullcontext()
    ):
        num_thread = multiprocessing.cpu_count() * 10
        cmd = (
            ["robocopy.exe", source, dest]
            + handle_existing_files_args
            + ["/MT:{}".format(num_thread), "/NP", "/W:3", "/R:3"]
            + extra_args
        )

        return_code, _, _ = run(cmd, allow_non_zero_exit_code=True, print_std_out=True)

    if lock_file and os.path.exists(lock_file):
        os.remove(lock_file)

    robo_errors = {
        0: "No files were copied. No failure was encountered. No files were mismatched.\
            The files already exist in the destination directory;\
            therefore, the copy operation was skipped.",
        1: "All files were copied successfully.",
        2: "There are some additional files in the destination directory that are\
            not present in the source directory. No files were copied.",
        3: "Some files were copied. Additional files were present. No failure was encountered.",
        4: "Mismatched files or directories found. Examine log.",
        5: "Some files were copied. Some files were mismatched. No failure was encountered.",
        6: "Additional files and mismatched files exist. No files were copied and no failures\
           were encountered. This means that the files already exist in the destination directory.",
        7: "Files were copied, a file mismatch was present and additional files were present.",
        8: "Several files did not copy.",
        9: "OKCOPY + FAIL",
        10: "FAIL + XTRA",
        11: "OKCOPY + FAIL + XTRA",
        12: "FAIL + MISMATCHES",
        13: "OKCOPY + FAIL + MISMATCHES",
        14: "FAIL + MISMATCHES + XTRA",
        15: "OKCOPY + FAIL + MISMATCHES + XTRA",
        16: "Copy failed catastrophically",
    }

    err = robo_errors.get(return_code, "Unknown error.")

    if return_code > 7:
        LOGGER.error(
            "Robocopy.exe failed with error {0} ({1}): '{2}'".format(
                return_code,
                "https://ss64.com/nt/robocopy-exit.html",
                err,
            ),
            exc_info=True,
        )
        raise CoreException
    else:
        LOGGER.debug("Robocopy exited with status {0}: '{1}'".format(return_code, err))


def md5_check(source, dest):
    """
    Runs md5 check on source and destination folders.
    """
    try:
        exf_path = SETTINGS.get("md5_exf_path")
    except ConfigValueNotFoundException:
        raise ELIPYException(
            "There is no md5 exe path set in Elipy config, can't run md5 validation."
        )

    if not os.path.isabs(exf_path):
        exf_path = os.path.join(frostbite_core.get_tnt_root(), exf_path)

    dest_manifest = os.path.join(dest, "manifest.md5")
    dest_manifest_log = os.path.join(dest, "manifest_log.txt")

    cmd = [
        "cmd",
        "/C",
        exf_path,
        "-r",
        "-omd5",
        "-md5",
        "-d",
        source,
        "*.*",
        ">",
        dest_manifest,
    ]
    LOGGER.info("Computing md5 hashes with command: {}".format(cmd))
    run(cmd)

    cmd = [
        "cmd",
        "/C",
        exf_path,
        "-cv",
        dest_manifest,
        "-d",
        dest,
        ">",
        dest_manifest_log,
    ]
    LOGGER.info("Verifying md5 hashes command: {}".format(cmd))
    run(cmd)

    with open(dest_manifest_log, "r") as log_file:
        log = log_file.read()

    if "MD5 FAIL" in log or "No files found." in log:
        raise ELIPYException(
            "md5 validation between {} and {} failed, see {}".format(
                source, dest, dest_manifest_log
            )
        )
    if not "No errors." in log:
        raise ELIPYException(
            "md5 validation between {} and {} failed, see {}".format(
                source, dest, dest_manifest_log
            )
        )

    LOGGER.info("md5 validation successful.")


def md5_hash_one_file(file_path: str, chunk_size: int = 65536) -> str:
    """
    Create an MD5 hash for the contents of a file.
    Read a small chunk of the file at a time, to avoid issues with large files.

    :param file_path: The file to create an MD5 hash for.
    :param chunk_size: Size of the chunk we read from the file before processing.
    :return: Returns the hash, formatted to a hex value.
    """
    with open(file_path, "rb") as hash_file:
        file_hash = hashlib.md5()
        chunk = hash_file.read(chunk_size)
        while chunk:
            file_hash.update(chunk)
            chunk = hash_file.read(chunk_size)
        return file_hash.hexdigest()


def md5_hash_one_file_parallel(
    folder_path: Path, file_path: Path, chunk_size: int = 65536
) -> (str, str):
    """
    Create an MD5 hash for the contents of a file.
    Read a small chunk of the file at a time, to avoid issues with large files.
    This version is adapted for running multiple times in parallel, using the function run_starmap
    in the elipy2 module multiprocessing. This requires the function to return the relative path.

    :param folder_path: The path to the top level folder, used to create relative paths.
    :param file_path: The file to create an MD5 hash for.
    :param chunk_size: Size of the chunk we read from the file before processing.
    :return: Returns the relative path to the file and the hash(formatted to a hex value).
    """
    relative_path = file_path.relative_to(folder_path)
    with open(file_path, "rb") as hash_file:
        file_hash = hashlib.md5()
        chunk = hash_file.read(chunk_size)
        while chunk:
            file_hash.update(chunk)
            chunk = hash_file.read(chunk_size)
        hash_result = file_hash.hexdigest()
    return str(relative_path), hash_result


@collect_metrics()
def md5_hash_folder(
    folder_path: str,
    skipped_files: List = [],
    output_file_name: str = "Elipy_MD5_hashes",
    output_file_type: str = "csv",
    chunk_size: int = 65536,
) -> None:
    """
    Create a file with a list of MD5 hashes for all files in a folder.
    Read a small chunk of each file at a time, to avoid issues with large files.

    :param folder_path: The folder to create the file hash list for.
    :param output_file_name: The name of the file with the hash list.
    :param output_file_type: The file type for the output file.
    :param chunk_size: Size of the chunk we read from each file before processing.
    :return: None (creates a file with the hash list in the same folder).
    """
    LOGGER.info("Create MD5 hashes for files in %s", folder_path)
    folder_path = Path(folder_path)
    files = [path_file for path_file in folder_path.rglob("*") if not path_file.is_dir()]
    not_skipped_files = []

    for file in files:
        if not any(fnmatch.fnmatch(file, skipped_files) for skip_file in skipped_files):
            not_skipped_files.append(file)

    input_tuple = [(folder_path, hash_file, chunk_size) for hash_file in not_skipped_files]
    result = elipy_mp.run_starmap(md5_hash_one_file_parallel, input_tuple)
    file_dict = dict(result)

    output_file = os.path.join(folder_path, f"{output_file_name}.{output_file_type}")
    LOGGER.info("Write the created MD5 hashes to %s", output_file)
    if output_file_type == "csv":
        with open(output_file, "w", newline="") as csv_file:
            writer = csv.writer(csv_file)
            for key, value in file_dict.items():
                writer.writerow([key, value])
    elif output_file_type == "txt":
        with open(output_file, "w") as txt_file:
            for key, value in file_dict.items():
                txt_file.write(f"{key}: {value}\n")
    else:
        raise CoreException(f"Unsupported output file type: {output_file_type}, aborting.")
    LOGGER.info("Done creating and writing MD5 hashes.")


@collect_metrics()
def check_for_handles(path):
    """
    Try to rename provided path, if unsuccessful, raise exception
    and assume open file handles; run method to recursively
    identify opened files, so the job will fail with a
    detailed list of opened files
    """
    LOGGER.info("attempting to rename {0}, checking for open handles".format(path))
    new_path = path + "_DELETE_ME"
    try:
        os.rename(path, new_path)
        os.rename(new_path, path)
    except Exception as exc:
        # FUTUREENGINEERS add recursive file-open
        # function to gather list of open file handles ENDTODO
        LOGGER.error("Possible open file handles in: {0}".format(path))
        LOGGER.error("Failed to rename folder: {0} with exception {1}".format(path, exc))
        raise ELIPYException("Failed to rename folder: {0}".format(path))


@collect_metrics()
def close_file_handles(path):
    """
    Checks for open file handles in path and tries to close all open handles
    Requires handle.exe to be avalible in path
    """
    LOGGER.info("Checking {0} for open handles".format(path))

    try:
        handle_exe = SETTINGS.get("handle_exe_path")
    except ConfigValueNotFoundException:
        # This is the default location we install handle everywhere
        handle_exe = "C:\\ProgramData\\chocolatey\\bin\\handle"

    try:
        subprocess.run(["cmd", "/C", "where", "handle"], check=True)
    except subprocess.CalledProcessError:
        LOGGER.info("Can't find installation of handle.exe, skipping closing of handles")
    else:
        process_args = ["cmd", "/C", handle_exe, path, "-nobanner", "-accepteula"]
        cmd = " ".join(process_args)
        LOGGER.info("Running external command: {}".format(cmd))
        try:
            raw_output = subprocess.check_output(process_args)
        except subprocess.CalledProcessError as exep:
            LOGGER.info("Failed to run close handles, check if handle.exe is installed")
            LOGGER.info(exep)
        else:
            # pylint: disable=redefined-variable-type
            # pylint: disable=bad-option-value
            output = None
            if not isinstance(raw_output, str):
                output = raw_output.decode()
            else:
                output = raw_output
            # pylint: enable=redefined-variable-type
            # pylint: enable=bad-option-value

            if "usage: handle" not in output:
                for line in output.split("\n")[0:-1]:
                    handle_info = line.split()
                    # Handle returns lines of this format:
                    # "explorer.exe  pid:  101484  type:  File  16E4:  \Local\Bin\"
                    if len(handle_info) >= 7:
                        LOGGER.info(
                            "Process {}, {}, is holding file in {}, closing handle".format(
                                handle_info[0], handle_info[2], handle_info[6]
                            )
                        )
                        hexidex = handle_info[5][:-1]
                        run(
                            [
                                "handle",
                                "-c",
                                hexidex,
                                "-p",
                                handle_info[2],
                                "-nobanner",
                                "-y",
                                "-accepteula",
                            ]
                        )
                    else:
                        LOGGER.info("Not closing: {}".format(line))
            else:
                LOGGER.info("Invalid call to handle.exe, skipping closing of handles")


def is_filer_api_available():
    """
    Determines whether or not filer API is available for the current project.
    Returns:
        True if filer_api_url is in settings config, False otherwise
    """
    try:
        return SETTINGS.get("filer_api_url") is not None
    except ConfigValueNotFoundException:
        return False


@retry.retry(JSONDecodeError, tries=3, delay=6, backoff=2, logger=LOGGER)
@collect_metrics()
def close_filer_file_handles(path):
    """
    Closes any open file handles in the given path.
    Returns:
        True if any file handles were removed, False otherwise.
    """
    if not path or "\\" not in path or not is_filer_api_available():
        return False
    cl_filter = path.rsplit("\\", 1)[-1]
    url = SETTINGS.get("filer_api_url")
    request_url = url + "/openfiles"
    LOGGER.info("Requesting open handles '{0}' with filter '{1}'".format(request_url, cl_filter))
    open_files_response = requests.get(request_url, params={"filter": cl_filter}, verify=False)

    if open_files_response.content.decode("utf-8") == '"Filter returned no data..."':
        LOGGER.info("No open file handles. Nothing to do.")
        return False

    try:
        decoded_json_response = json.loads(open_files_response.content)
    except JSONDecodeError:
        LOGGER.warning(
            "Failed to decode response from {0} : {1}".format(
                request_url, open_files_response.content
            ),
            exc_info=True,
        )
        raise

    session_has_been_closed = False
    for item in decoded_json_response:
        node = item["node"]
        session = item["session"]
        user = item["user"]
        formatted_file = item["file"].replace("\\\\", "\\")
        LOGGER.info(
            "Killing session for user {0} on file {1} (session ID: {2}; node ID: {3})".format(
                user, formatted_file, session, node
            )
        )
        requests.get(
            url + "/closesession",
            params={"node": node, "session": session},
            verify=False,
        )
        session_has_been_closed = True
        LOGGER.info(
            "Session {0} on node {1} has been killed for user {2}. File: {3}".format(
                session, node, user, formatted_file
            )
        )
    return session_has_been_closed


@collect_metrics()
def delete_filer_folder(path, retries=0):
    """
    Deletes the folder at $path by using robocopy to copy an empty folder over it.
    If the deletion fails it attempts to remove any open file handles and tries again.
    """
    try:
        delete_folder_with_robocopy(path)
    except Exception as exc:
        LOGGER.warning("Failed to delete folder using robocopy: {0}".format(exc))
        LOGGER.info("Checking for open file handles that might cause problems.")
        should_rerun = close_filer_file_handles(path)
        if should_rerun and retries < 4:
            retries += 1
            time.sleep(5 * retries)
            LOGGER.info("Attempting to delete {0} again".format(path))
            delete_filer_folder(path, retries)
        else:
            raise exc


@collect_metrics()
def delete_folder_with_robocopy(path):
    """
    Deletes the folder at $path by using robocopy to copy an empty folder over it.

    Benefit here is that this can deal with super deep directiories and filenames which otherwise
    fails.
    """
    LOGGER.info("Using robocopy deletion method")
    empty_folder = os.path.join(os.path.abspath(os.sep), "empty_folder")

    if not os.path.exists(empty_folder):
        try:
            os.mkdir(empty_folder)
        except FileExistsError:
            # directory already exists
            # https://stackoverflow.com/questions/3112546/os-path-exists-lies
            pass

    LOGGER.info("Stomping {0} with {1}".format(empty_folder, path))
    # Magical collection of arguments to stomp robocopy output - it can be *very* verbose if we
    # have a super deep folder path here.
    args = [
        "/PURGE",
        "/NFL",
        "/NDL",
        "/NJH",
        "/NJS",
        "/NS",
        "/NC",
        "/NP",
        "/LOG:NUL",
    ]
    robocopy(empty_folder, path, purge=True, extra_args=args)

    # An empty folder gets left behind at $path, so removing that also.
    try:
        os.rmdir(path)
    except FileNotFoundError:
        LOGGER.info("Expected folder at path {0} but does not exist.".format(path))
    except OSError as exc:
        # Check if this is a "directory not empty" error (WinError 145)
        if exc.errno == 145 or "directory is not empty" in str(exc).lower():
            LOGGER.warning(
                "Directory {0} not empty after robocopy purge, attempting fallback cleanup.".format(
                    path
                )
            )
            try:
                # Use shutil.rmtree as fallback with error callback for better handling
                _chmod_recursive(path, stat.S_IWRITE)
                shutil.rmtree(path, onerror=_delete_folder_callback)
                LOGGER.info(
                    "Successfully deleted directory {0} using fallback method.".format(path)
                )
            except Exception as fallback_exc:
                LOGGER.error(
                    "Fallback deletion also failed for {0}: {1}".format(path, fallback_exc)
                )
                # Only raise if directory still exists after all attempts
                if os.path.exists(path):
                    raise ELIPYException(
                        "Failed to delete folder after robocopy and fallback: {0}".format(path)
                    )
                else:
                    LOGGER.info(
                        "Directory {0} was successfully removed despite error.".format(path)
                    )
        else:
            # For other types of OSError, raise immediately
            LOGGER.warning("Expected folder at path {0} to be empty but it was not.".format(path))
            raise exc
    except Exception as exc:
        LOGGER.warning("Unexpected error removing folder at path {0}: {1}".format(path, exc))
        raise exc


@collect_metrics()
def delete_folder(path, close_handles=False):
    """
    Reliably delete the folder at $path and everything inside it
    Close handles functionallity require installation of handle.exe
    """
    # no path no problem
    if os.path.exists(path):
        LOGGER.info("attempting to delete [%s]", path)
        if close_handles:
            close_file_handles(path)
        else:
            check_for_handles(path)
        _chmod_recursive(path, stat.S_IWRITE)
        try:
            if os.path.isdir(path):
                shutil.rmtree(path, onerror=_delete_folder_callback)
            else:
                os.remove(path)
        # pylint: disable=C0103
        except Exception as exc:
            LOGGER.error("Failed to delete folder: [%s] with exception: [%s]", path, exc)
            raise ELIPYException("Failed to delete folder: [%s]", path)


@collect_metrics()
def _chmod_recursive(path, mode):
    """
    mode should be in octal form: 0o777
    """
    os.chmod(path, mode)
    for root, dirs, _ in os.walk(path):
        for directory in dirs:
            os.chmod(os.path.join(root, directory), mode)


# pylint: disable=W0613
def _delete_folder_callback(func, del_path, exc):
    if os.path.isfile(del_path):
        try:
            os.chmod(del_path, stat.S_IWRITE)
            os.remove(del_path)
        except Exception as _exc:
            raise _exc
    else:
        if func is os.listdir:
            LOGGER.error("[expire] could not listdir: %s", del_path)
        elif func is os.remove:
            LOGGER.error("[expire] could not remove: %s", del_path)
        elif func is os.rmdir:
            LOGGER.error("[expire] could not rmdir: %s", del_path)
        # in all cases, re-rase exception
        raise exc


def is_buildsystem_run():
    """
    :returns true: if FB_AUTOBUILD is set in the environment.
    This is the official environment variable that the Frostbite engine uses to detect if it's
    running in CI or not.
    """
    if not "ELIPY_TEST_RUN" in os.environ:
        return os.environ.get("FB_AUTOBUILD", False)
    else:
        return False


@collect_metrics()
def create_zip(
    source: str,
    destination: str,
    additional_args: List[str] = [],
    compression: int = 3,
    keep_dir_source: bool = False,
    skip_compression: List[str] = [],
):
    """
    Creates a .zip archive from $source (recursively) and outputs at $destination (incl. filename)
    Uses 7zip.exe for performance considerations.

    Passes the default parameters of "a -mx3 -tzip", and if no additional_args are passed it adds
    exclusion of file types that we generally do not want in distributed builds (i.e. pdb's).

    Supports adding specified files to the zip archive without compression.
    """
    if not source.endswith(("*.*", "*")) and not keep_dir_source:
        source += "\\*"

    if not ".zip" in destination:
        raise ELIPYException("Unable to output to {0}, missing .zip.".format(destination))

    destinationdir = os.path.dirname(destination)
    LOGGER.info("Creating directory {0}".format(destinationdir))
    if not os.path.exists(destinationdir):
        os.makedirs(destinationdir)

    cmd = [
        "a",
        "-mx{}".format(compression),
        "-tzip",
        "-y",
        destination,
        source,
        "{}\\config6".format(source.strip("*.*").rstrip("\\")),
    ]

    if not additional_args:
        additional_args = [
            "-r",
            "-x!*.pdb",
            "-x!*.cas",
            "-x!*.map",
            "-x!*.ilk",
            "-x!*.pyc",
        ]

    cmd += additional_args
    _run_7zip(cmd)

    if skip_compression:
        # Need to use relative path when adding a single file,
        # in order to retain folder structures inside the zip.
        old_dir = os.getcwd()
        os.chdir(source.strip("*.*").rstrip("\\"))
        for skip_file in skip_compression:
            cmd_skip = ["a", "-mx0", "-tzip", "-y", destination, skip_file]
            _run_7zip(cmd_skip)
        os.chdir(old_dir)


@collect_metrics()
def extract_zip(source: str, destination: str, additional_args: List[str] = []):
    """
    Extracts a .zip archive from $source (recursively) and outputs at $destination (incl. filename)
    Uses 7zip.exe for performance considerations.
    """

    if not ".zip" in source:
        raise ELIPYException(
            "Unable to extract from {0}, missing .zip. Not sure if source is a file.".format(source)
        )

    cmd = ["x", "-r", "-y", source, "-o" + destination]
    cmd += additional_args
    _run_7zip(cmd)


@collect_metrics()
def _run_7zip(command: List[str]):
    """
    Find the 7zip executable, run it with the provided command and handle the error code.
    """
    path_to_7zip = os.path.join(frostbite_core.get_tnt_root(), "Bin", "7z.exe")
    command = [path_to_7zip] + command
    error_code, _, _ = run(command, allow_non_zero_exit_code=True)

    LOGGER.debug("7zip.exe exited with status {0}".format(error_code))
    if error_code not in [0, 1]:
        LOGGER.error("7zip.exe failed with error {0}".format(error_code))
        raise CoreException("7zip.exe failed with error {0}".format(error_code))


def ensure_p4_config(root_dir=None, port=None, client=None, user=None, ignore=None):
    """
    Ensures that the .p4config in GAME_ROOT, or root arg, is populated with the correct
    P4 information as set in the environment.

    Should most likely only be used in conjuction with core.is_build_system_run(),
    and may have implicit dependancies on P4 Plugin naming conventions.

    However, since the pipeline requires a connection to Perforce for indexing purposes, we need
    a way of passing this information to the pipeline. Other Frostbite tooling ensures this file
    exists (Drone, I believe) - but we don't run that on build machines nor want to.

    This also has an implicit dependancy on order when using MultiSCM context, but we already
    have that implicitness with i.e. labelling.

    Long term we want to get rid of this, but for the short term we need this.
    """
    if port is None:
        port = os.environ.get("P4_PORT", None)
    if client is None:
        client = os.environ.get("P4_CLIENT", None)
    if user is None:
        user = os.environ.get("P4_USER", None)
    if ignore is None:
        ignore = os.environ.get("P4_IGNORE", None)

    if None in [port, client, user]:
        LOGGER.warning("Unable to ensure .p4config! Got: {0}".format([port, client, user]))
        return

    if root_dir is None:
        p4config = os.path.join(frostbite_core.get_game_root(), ".p4config")
    else:
        p4config = os.path.join(root_dir, ".p4config")
    config_string = "P4PORT={0}\nP4CLIENT={1}\nP4USER={2}\n".format(port, client, user)

    if ignore:
        config_string += "\nP4IGNORE={0}".format(ignore)
    config_string += "\nfilesys.windows.lfn=10\n"

    LOGGER.info("Generating {} with contents:\n{}".format(p4config, config_string))
    with open(p4config, "w") as _file:
        _file.write(config_string)

    with open(p4config, "r") as _file:
        LOGGER.info("{} contents:\n{}".format(p4config, _file.read()))

    LOGGER.info("Setting P4ENVIRO to {}".format(p4config))
    os.environ["P4ENVIRO"] = p4config


def get_counter_value(counter_name, p4port, location, username=None):
    """
    Gets the counter value that is environment dependant and returns it to the function
    We can override the username is we are on servers that require a DOMAIN prefix
    """
    username = os.environ["USERNAME"]
    licensee_name = os.environ["LICENSEE_ID"]
    environment = location.lower()

    # local or non prod
    if not is_buildsystem_run() or environment != "default":
        LOGGER.info("OVERRIDE: Setting counter to point to the test environment")
        full_counter_name = "{0}_{1}_{2}".format(licensee_name, counter_name, environment)
    else:  # prod
        full_counter_name = "{0}_{1}".format(licensee_name, counter_name)

    perforce = p4.P4Utils(port=p4port, user=username)

    code_changelist = perforce.getcounter(full_counter_name)
    LOGGER.info("The current code build CL: {0}".format(code_changelist))

    if code_changelist == "0":
        raise ELIPYException("Could not find counter: {0}".format(full_counter_name))

    return code_changelist


def run_age_store(path, days, dry_run=False):
    """
    Use agestore in windowssdk package to clean up all files older than days in path
    """
    LOGGER.info("Deleting files in {} older than {} days.".format(path, days))
    agestore_exe = os.path.join(
        "C:",
        os.sep,
        r"Program Files (x86)",
        r"Windows Kits",
        "10",
        "debuggers",
        "x64",
        "agestore.exe",
    )

    run_args = [agestore_exe, path, "-days={}".format(days), "-s", "-y"]
    if dry_run:
        run_args += ["-l"]
    run(run_args, print_std_out=True)


def list_chunk_generator(large_list: List, chunk_size: int) -> Iterator:
    """
    Create a generator, which will split a larger list into smaller chunks.

    :param large_list: The list we want to split.
    :param chunk_size: The size of each chunk.
    :return: A generator where each iteration will create a smaller chunk of the initial list.
    """

    for i in range(0, len(large_list), chunk_size):
        yield large_list[i : i + chunk_size]
