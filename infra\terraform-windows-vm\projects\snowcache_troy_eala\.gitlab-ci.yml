#**********************************************
#                  Snowcache State PIPE
#**********************************************
.default-snowcache-troy-eala-variables:
  extends: .secrets-snowcache_troy_eala
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "snowcache_troy_eala"
    WORKING_DIR: "projects/snowcache_troy_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-snowcache-eala.json"
    ansible_main_module: avalanche_snowcache
    SILVERBACK_CONFIG_JSON_FILE: "snowcache_troy_eala.json"
    TF_LOG: "TRACE"
    TF_LOG_PATH: $CI_PROJECT_DIR/debug.log

prepare-json-config-snowcache-troy-eala:
  extends: ['.default-snowcache-troy-eala-variables', '.prepare_config']

validate-snowcache-troy-eala:
  extends: ['.default-snowcache-troy-eala-variables', '.validation_steps']

plan-snowcache-troy-eala:
  needs:
    - job: validate-snowcache-troy-eala
    - job: prepare-json-config-snowcache-troy-eala
  extends: ['.default-snowcache-troy-eala-variables', '.plan_steps']

apply-snowcache-troy-eala:
  needs:
    - job: plan-snowcache-troy-eala
    - job: prepare-json-config-snowcache-troy-eala
  extends: ['.default-snowcache-troy-eala-variables', '.apply_steps']

ansible-snowcache-troy-eala:
  needs:
    - job: apply-snowcache-troy-eala
    - job: prepare-json-config-snowcache-troy-eala
  extends: ['.default-snowcache-troy-eala-variables', '.ansible_common_secrets', '.run_ansible_step']

