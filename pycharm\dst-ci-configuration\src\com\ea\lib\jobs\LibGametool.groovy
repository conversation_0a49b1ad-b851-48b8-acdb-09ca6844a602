package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.LibPerforce
import com.ea.lib.jobsettings.GametoolSettings

class LibGametool {

    static void start(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            environmentVariables {
                env('BRANCH_NAME', settings.branchName)
                env('CODE_BRANCH', settings.codeBranch)
                env('CODE_FOLDER', settings.codeFolder)
                env('PROJECT_NAME', settings.projectName)
                env('NON_VIRTUAL_CODE_BRANCH', settings.nonVirtualCodeBranch)
                env('NON_VIRTUAL_CODE_FOLDER', settings.nonVirtualCodeFolder)
            }
            disabled(false)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        pollSCM {
                            scmpoll_spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    static void icepickJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeIcepick(branchFile, masterFile, project, branchName, LibPerforce.GAMETOOL_ICEPICK)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    static void frostbiteDatabaseUpgraderJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeFrostbiteDatabaseUpgrader(branchFile, masterFile, project, branchName, LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    static void frostyisotoolJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeFrostyisotool(branchFile, masterFile, project, branchName, LibPerforce.GAMETOOL_FROSTYISOTOOL)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }

                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    static void droneJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeDrone(branchFile, masterFile, project, branchName, LibPerforce.GAMETOOL_DRONE)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }

                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    static void frameworkJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeFramework(branchFile, masterFile, project, branchName, LibPerforce.GAMETOOL_FRAMEWORK)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN', false, 'If true, TnT/Build/Framework/bin will be deleted at the beginning of the run.')
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }

                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    static void fbenvJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new GametoolSettings()
        settings.initializeFbenv(branchFile, masterFile, project, branchName, LibPerforce.GAMETOOL_FBENV)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('SUBMIT', true, 'Submit the result to perforce. Uncheck this if you want dry-run')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }

                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
