#**********************************************
#               bct_eala PIPE                 *
#**********************************************
.default-bct-eala-variables:
  extends: .secrets-bct_eala
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_eala"
    WORKING_DIR: "projects/bct_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-eala.json"
    ansible_main_module: bct_eala_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-eala:
  extends: ['.default-bct-eala-variables', '.prepare_config']

validate-bct-eala:
  extends: ['.default-bct-eala-variables', '.validation_steps']

plan-bct-eala:
  needs:
    - job: validate-bct-eala
    - job: prepare-json-config-bct-eala
  extends: ['.default-bct-eala-variables','.plan_steps']

apply-bct-eala:
  needs:
    - job: plan-bct-eala
    - job: prepare-json-config-bct-eala
  extends: ['.default-bct-eala-variables','.apply_steps']

attache-bct-eala:
  needs:
    - job: apply-bct-eala
    - job: prepare-json-config-bct-eala
  extends: ['.default-bct-eala-variables','.attache_vmdk_step']

sync-bct-eala:
  needs:
    - job: apply-bct-eala
    - job: attache-bct-eala
    - job: prepare-json-config-bct-eala
  extends: ['.default-bct-eala-variables','.sync_vmdk_step']

ansible-bct-eala:
  needs:
    - job: apply-bct-eala
    - job: sync-bct-eala
    - job: prepare-json-config-bct-eala
  extends: ['.default-bct-eala-variables', '.ansible_common_secrets', '.run_ansible_step']
