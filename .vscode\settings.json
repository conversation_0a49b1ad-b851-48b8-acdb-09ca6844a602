{"chat.tools.autoApprove": true, "python.defaultInterpreterPath": "python", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.pytestArgs": ["."], "python.testing.autoTestDiscoverOnSaveEnabled": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": false, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=100"], "python.sortImports.args": ["--profile", "black"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "python.terminal.activateEnvironment": true, "files.associations": {"*.py": "python"}}