# File Comparison Report

**Date:** July 3, 2025  
**Analysis:** Comparison between two log files containing UNC paths to build directories

## Files Compared

| File | Path | Total Paths |
|------|------|-------------|
| File A | `c:\Users\<USER>\vscode\txt\about_delete_builds.log` | 28 |
| File B | `c:\Users\<USER>\vscode\txt\should_be_delete_from_script.log` | 21 |

## Summary

- **Total unique paths across both files:** 29
- **Common paths in both files:** 20
- **Paths only in File A:** 8
- **Paths only in File B:** 1

## Detailed Results

### Paths in File A but NOT in File B (8 paths)

These are build paths that exist in `about_delete_builds.log` but are missing from `should_be_delete_from_script.log`:

1. **Build 24216947**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24216947\CH1-event\24216947
   ```

2. **Build 24221370**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24221370\CH1-event\24221370
   ```

3. **Build 24230552**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24230552\CH1-event\24230552
   ```

4. **Build 24237548**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24237548\CH1-event\24237548
   ```

5. **Build 24254078**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24254078\CH1-event\24254078
   ```

6. **Build 24283046**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24283046\CH1-event\24283046
   ```

7. **Build 24286233**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24286233\CH1-event\24286233
   ```

8. **Build 24288006**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24288006\CH1-event\24288006
   ```

### Paths in File B but NOT in File A (1 path)

This build path exists in `should_be_delete_from_script.log` but is missing from `about_delete_builds.log`:

1. **Build 24310479**
   ```
   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\24310479\CH1-event\24310479
   ```

## Build Number Quick Reference

### Only in File A:
`24216947`, `24221370`, `24230552`, `24237548`, `24254078`, `24283046`, `24286233`, `24288006`

### Only in File B:
`24310479`

## Analysis Notes

- All paths follow the same UNC path structure: `\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\[BUILD_NUMBER]\CH1-event\[BUILD_NUMBER]`
- File A contains more recent builds (higher build numbers) that are not present in File B
- File B contains one build (24310479) that is not present in File A
- The analysis was performed using Python set operations to ensure accuracy

## Methodology

The comparison was performed using a Python script that:
1. Read both files line by line
2. Stripped whitespace and ignored empty lines
3. Used set operations to find differences
4. Extracted build numbers for easier identification
5. Generated this comprehensive report

---
*Report generated automatically by file comparison script*
