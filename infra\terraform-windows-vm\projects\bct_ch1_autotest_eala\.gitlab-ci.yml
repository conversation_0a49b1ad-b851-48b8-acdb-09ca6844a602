#**********************************************
#               bct_ch1_autotest_eala PIPE                 *
#**********************************************
.default-bct_ch1_autotest_eala-variables:
  extends: .secrets-bct_ch1_autotest_eala
  variables:
    APPLY_PARALLELISM: "3" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ch1_autotest_eala"
    WORKING_DIR: "projects/bct_ch1_autotest_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-bct_ch1_autotest_eala.json"
    ansible_main_module: bct_ch1_eala_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ch1_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct_ch1_autotest_eala:
  extends: ['.default-bct_ch1_autotest_eala-variables', '.prepare_config']

validate-bct_ch1_autotest_eala:
  extends: ['.default-bct_ch1_autotest_eala-variables', '.validation_steps']

plan-bct_ch1_autotest_eala:
  needs:
    - job: validate-bct_ch1_autotest_eala
    - job: prepare-json-config-bct_ch1_autotest_eala
  extends: ['.default-bct_ch1_autotest_eala-variables','.plan_steps']

apply-bct_ch1_autotest_eala:
  needs:
    - job: plan-bct_ch1_autotest_eala
    - job: prepare-json-config-bct_ch1_autotest_eala
  extends: ['.default-bct_ch1_autotest_eala-variables','.apply_steps']

attache-bct_ch1_autotest_eala:
  needs:
    - job: apply-bct_ch1_autotest_eala
    - job: prepare-json-config-bct_ch1_autotest_eala
  extends: ['.default-bct_ch1_autotest_eala-variables','.attache_vmdk_step']

sync-bct_ch1_autotest_eala:
  needs:
    - job: apply-bct_ch1_autotest_eala
    - job: attache-bct_ch1_autotest_eala
    - job: prepare-json-config-bct_ch1_autotest_eala
  extends: ['.default-bct_ch1_autotest_eala-variables','.sync_vmdk_step']

ansible-bct_ch1_autotest_eala:
  needs:
    - job: apply-bct_ch1_autotest_eala
    - job: sync-bct_ch1_autotest_eala
    - job: prepare-json-config-bct_ch1_autotest_eala
  extends: ['.default-bct_ch1_autotest_eala-variables', '.ansible_common_secrets', '.run_ansible_step']
