#**********************************************
#                FB1_PS PIPE
#**********************************************
.default-fb1-ps-variables:
  extends: .secrets-fb1_ps
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "fb1_ps"
    WORKING_DIR: "projects/fb1_ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-fb1.json"
    ansible_main_module: fb_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1_ps.json"

prepare-json-config-fb1-ps:
  extends: ['.default-fb1-ps-variables', '.prepare_config']

validate-fb1-ps:
  extends: ['.default-fb1-ps-variables', '.validation_steps']

plan-fb1-ps:
  needs:
    - job: validate-fb1-ps
    - job: prepare-json-config-fb1-ps
  extends: ['.default-fb1-ps-variables', '.plan_steps']

apply-fb1-ps:
  needs:
    - job: plan-fb1-ps
    - job: prepare-json-config-fb1-ps
  extends: ['.default-fb1-ps-variables', '.apply_steps']

attache-fb1-ps:
  needs:
    - job: apply-fb1-ps
    - job: prepare-json-config-fb1-ps
  extends: ['.default-fb1-ps-variables', '.attache_vmdk_step']

sync-fb1-ps:
  needs:
    - job: apply-fb1-ps
    - job: attache-fb1-ps
    - job: prepare-json-config-fb1-ps
  extends: ['.default-fb1-ps-variables', '.sync_vmdk_step']

ansible-fb1-ps:
  needs:
    - job: apply-fb1-ps
    - job: sync-fb1-ps
    - job: prepare-json-config-fb1-ps
  extends: ['.default-fb1-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
