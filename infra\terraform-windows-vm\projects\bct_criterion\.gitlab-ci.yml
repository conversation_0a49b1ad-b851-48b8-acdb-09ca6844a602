#**********************************************
#                  BCT CRITERION
#**********************************************
.default-bct-criterion-variables:
  extends: .secrets-bct_criterion
  variables:
    APPLY_PARALLELISM: "5"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_criterion"
    WORKING_DIR: "projects/bct_criterion"
    VC_HOST: oh-vcenter1.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-criterion.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_criterion.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-criterion:
  extends: ['.default-bct-criterion-variables', '.prepare_config']

validate-bct-criterion:
  extends: ['.default-bct-criterion-variables', '.validation_steps']

plan-bct-criterion:
  needs:
    - job: validate-bct-criterion
    - job: prepare-json-config-bct-criterion
  extends: ['.default-bct-criterion-variables', '.plan_steps']

apply-bct-criterion:
  needs:
    - job: plan-bct-criterion
    - job: prepare-json-config-bct-criterion
  extends: ['.default-bct-criterion-variables', '.apply_steps']

attache-bct-criterion:
  needs:
    - job: apply-bct-criterion
    - job: prepare-json-config-bct-criterion
  extends: ['.default-bct-criterion-variables', '.attache_vmdk_step']

sync-bct-criterion:
  needs:
    - job: attache-bct-criterion
    - job: prepare-json-config-bct-criterion
  extends: ['.default-bct-criterion-variables', '.sync_vmdk_step']

ansible-bct-criterion:
  needs:
    - job: sync-bct-criterion
    - job: prepare-json-config-bct-criterion
  extends: ['.default-bct-criterion-variables', '.ansible_common_secrets', '.run_ansible_step']
