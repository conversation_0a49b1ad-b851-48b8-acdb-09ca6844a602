#**********************************************
#               fb1_eala PIPE                 *
#**********************************************
.default-fb1-eala-variables:
  extends: .secrets-fb1_eala
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "fb1_eala"
    WORKING_DIR: "projects/fb1_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-fb1_eala.json"
    ansible_main_module: fb_eala_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-fb1-eala:
  extends: ['.default-fb1-eala-variables', '.prepare_config']

validate-fb1-eala:
  extends: ['.default-fb1-eala-variables', '.validation_steps']

plan-fb1-eala:
  needs:
    - job: validate-fb1-eala
    - job: prepare-json-config-fb1-eala
  extends: ['.default-fb1-eala-variables','.plan_steps']

apply-fb1-eala:
  needs:
    - job: plan-fb1-eala
    - job: prepare-json-config-fb1-eala
  extends: ['.default-fb1-eala-variables','.apply_steps']

attache-fb1-eala:
  needs:
    - job: apply-fb1-eala
    - job: prepare-json-config-fb1-eala
  extends: ['.default-fb1-eala-variables','.attache_vmdk_step']

sync-fb1-eala:
  needs:
    - job: apply-fb1-eala
    - job: attache-fb1-eala
    - job: prepare-json-config-fb1-eala
  extends: ['.default-fb1-eala-variables','.sync_vmdk_step']

ansible-fb1-eala:
  needs:
    - job: apply-fb1-eala
    - job: sync-fb1-eala
    - job: prepare-json-config-fb1-eala
  extends: ['.default-fb1-eala-variables', '.ansible_common_secrets', '.run_ansible_step']
