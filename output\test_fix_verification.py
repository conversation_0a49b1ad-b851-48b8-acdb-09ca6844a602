#!/usr/bin/env python3
"""
Simple script to test the expire unittest fixes
"""

import subprocess
import sys
import os

# Change to the correct directory
os.chdir("c:/Users/<USER>/vscode/pycharm/elipy2")

# Test names to run
tests = [
    "elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception",
    "elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception"
]

print("Testing fixed expire unittest errors...")
print("=" * 60)

all_passed = True

for test in tests:
    print(f"\nRunning: {test.split('::')[-1]}")
    print("-" * 40)
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", test, "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✓ PASSED")
    else:
        print("✗ FAILED")
        print("STDOUT:")
        print(result.stdout)
        print("STDERR:")
        print(result.stderr)
        all_passed = False

print("\n" + "=" * 60)
if all_passed:
    print("🎉 ALL TESTS PASSED!")
else:
    print("❌ Some tests failed.")

print(f"\nScript completed.")
