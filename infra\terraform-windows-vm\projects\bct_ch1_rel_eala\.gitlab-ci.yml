#**********************************************
#               bct_ch1_rel_eala PIPE                 *
#**********************************************
.default-bct-ch1-rel-eala-variables:
  extends: .secrets-bct_ch1_rel_eala
  variables:
    APPLY_PARALLELISM: "3" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ch1_rel_eala"
    WORKING_DIR: "projects/bct_ch1_rel_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ch1-rel-eala.json"
    ansible_main_module: bct_ch1_eala_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ch1_rel_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-ch1-rel-eala:
  extends: ['.default-bct-ch1-rel-eala-variables', '.prepare_config']

validate-bct-ch1-rel-eala:
  extends: ['.default-bct-ch1-rel-eala-variables', '.validation_steps']

plan-bct-ch1-rel-eala:
  needs:
    - job: validate-bct-ch1-rel-eala
    - job: prepare-json-config-bct-ch1-rel-eala
  extends: ['.default-bct-ch1-rel-eala-variables','.plan_steps']

apply-bct-ch1-rel-eala:
  needs:
    - job: plan-bct-ch1-rel-eala
    - job: prepare-json-config-bct-ch1-rel-eala
  extends: ['.default-bct-ch1-rel-eala-variables','.apply_steps']

attache-bct-ch1-rel-eala:
  needs:
    - job: apply-bct-ch1-rel-eala
    - job: prepare-json-config-bct-ch1-rel-eala
  extends: ['.default-bct-ch1-rel-eala-variables','.attache_vmdk_step']

sync-bct-ch1-rel-eala:
  needs:
    - job: apply-bct-ch1-rel-eala
    - job: attache-bct-ch1-rel-eala
    - job: prepare-json-config-bct-ch1-rel-eala
  extends: ['.default-bct-ch1-rel-eala-variables','.sync_vmdk_step']

ansible-bct-ch1-rel-eala:
  needs:
    - job: apply-bct-ch1-rel-eala
    - job: sync-bct-ch1-rel-eala
    - job: prepare-json-config-bct-ch1-rel-eala
  extends: ['.default-bct-ch1-rel-eala-variables', '.ansible_common_secrets', '.run_ansible_step']
