#**********************************************
#               bct_coverity PIPE             *
#**********************************************
.default-bct-coverity-variables:
  extends: .secrets-bct_coverity
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_coverity"
    WORKING_DIR: "projects/bct_coverity"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-coverity.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_coverity.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-coverity:
  extends: ['.default-bct-coverity-variables', '.prepare_config']

validate-bct-coverity:
  extends: ['.default-bct-coverity-variables', '.validation_steps']

plan-bct-coverity:
  needs:
    - job: validate-bct-coverity
    - job: prepare-json-config-bct-coverity
  extends: ['.default-bct-coverity-variables','.plan_steps']

apply-bct-coverity:
  needs:
    - job: plan-bct-coverity
    - job: prepare-json-config-bct-coverity
  extends: ['.default-bct-coverity-variables','.apply_steps']

attache-bct-coverity:
  needs:
    - job: apply-bct-coverity
    - job: prepare-json-config-bct-coverity
  extends: ['.default-bct-coverity-variables','.attache_vmdk_step']

sync-bct-coverity:
  needs:
    - job: apply-bct-coverity
    - job: attache-bct-coverity
    - job: prepare-json-config-bct-coverity
  extends: ['.default-bct-coverity-variables','.sync_vmdk_step']

ansible-bct-coverity:
  needs:
    - job: apply-bct-coverity
    - job: sync-bct-coverity
    - job: prepare-json-config-bct-coverity
  extends: ['.default-bct-coverity-variables', '.ansible_common_secrets', '.run_ansible_step']
