#!/usr/bin/env python3
"""
Debug script to understand why CH1-event branch is not being processed by deleter.py
"""

import os
from collections import ChainMap

def simulate_deleter_logic():
    """Simulate the deleter logic to understand the issue"""
    
    # Simulate the retention categories from Guildford config
    retention_categories = {
        'frosty\\BattlefieldGame': [
            {'default': 8},
            {'CH1-event-release': 12},
            {'CH1-release': 35},
            {'CH1-SP-release': 10},
            {'CH1-bflabs-release': 10},
            {'CH1-qol': 10},
            {'CH1-event': 10},
            {'CH1-bflabs-qol': 10}
        ]
    }
    
    # Simulate the build_share path
    build_share = "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Autobuilds"
    
    print("=" * 80)
    print("Simulating deleter logic")
    print("=" * 80)
    
    for sub_path, branch_list in retention_categories.items():
        print(f"\nProcessing sub_path: {sub_path}")
        print(f"Branch list: {branch_list}")
        
        # Create branch settings (same as deleter.py line 382)
        branch_settings = dict(ChainMap(*branch_list))
        print(f"Branch settings: {branch_settings}")
        
        # Create case-insensitive branch settings (same as deleter.py lines 383-385)
        branch_settings_normalized = {k.lower(): v for k, v in branch_settings.items()}
        print(f"Branch settings normalized: {branch_settings_normalized}")
        
        # Construct path (same as deleter.py line 386)
        path = build_share + "/" + sub_path
        print(f"Constructed path: {path}")
        
        # Simulate branch discovery (from our previous test)
        discovered_branches = ['CH1-SP-release', 'CH1-bflabs-qol', 'CH1-bflabs-release', 
                             'CH1-event', 'CH1-event-release', 'CH1-qol', 'CH1-release']
        print(f"Discovered branches: {discovered_branches}")
        
        print("\nProcessing each discovered branch:")
        for branch in discovered_branches:
            # Get retention amount (same as deleter.py lines 391-393)
            maxamount = branch_settings_normalized.get(
                branch.lower(), branch_settings["default"]
            )
            
            # Construct branch path (same as deleter.py line 394)
            branch_path = path + "/" + branch
            
            print(f"  Branch: {branch}")
            print(f"    Branch lowercase: {branch.lower()}")
            print(f"    Maxamount: {maxamount}")
            print(f"    Branch path: {branch_path}")
            print(f"    Would process: {'YES' if maxamount > 0 else 'NO'}")
            print()

def check_path_normalization():
    """Check if there are any path normalization issues"""
    print("=" * 80)
    print("Checking path normalization")
    print("=" * 80)
    
    # Test different path separators
    sub_path_backslash = "frosty\\BattlefieldGame"
    sub_path_forward = "frosty/BattlefieldGame"
    build_share = "\\\\eauk-file.eu.ad.ea.com\\Glacier\\Autobuilds"
    
    path1 = build_share + "/" + sub_path_backslash
    path2 = build_share + "/" + sub_path_forward
    
    print(f"Sub-path with backslash: {sub_path_backslash}")
    print(f"Sub-path with forward slash: {sub_path_forward}")
    print(f"Build share: {build_share}")
    print(f"Path 1 (backslash): {path1}")
    print(f"Path 2 (forward): {path2}")
    print(f"Normalized path 1: {os.path.normpath(path1)}")
    print(f"Normalized path 2: {os.path.normpath(path2)}")

if __name__ == "__main__":
    simulate_deleter_logic()
    print()
    check_path_normalization()
