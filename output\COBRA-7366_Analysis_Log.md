# COBRA-7366 Combined Bundles Analysis and Implementation Log

**Project Start Time:** 2025-06-25 10:22:00 AM

## Task 1.1 - Analysis of Existing Bundle Creation Logic

### Current System Analysis

**Current frosty and patchfrosty workflow:**

1. **Frosty Script (`frosty.py`):**
   - Main script that creates complete game builds with binaries and data
   - Uses FrostyIsoTool to package code and data together
   - Handles multiple platforms (win64, ps4, xb1, etc.)
   - Supports both deployed bundles and local data building
   - Current combined bundles logic is embedded within the main frosty workflow

2. **PatchFrosty Script (`patch_frosty.py`):**
   - Creates patch packages with only differences from baseline
   - Combines code with delta bundles created by patch_databuild
   - Also handles combined bundles for patch scenarios
   - Works with delta bundles for incremental updates

3. **Jenkins Workflow (`frosty_start.groovy` and `patchfrosty_start.groovy`):**
   - Both scripts check for `variant.format.contains('combine')` to identify combined bundle jobs
   - Use combine_code_changelist and combine_data_changelist parameters
   - Current system creates multiple bundle sets but only copies one to network share

4. **Bundle Management (`move_location_bundles.py`):**
   - Supports both "bundles" and "combine_bundles" bundle types
   - Can copy bundles between different filer locations
   - Uses filer_paths.get_bundles_path() for bundle location management

### Key Findings

**Problem Identified:**
- Multiple sets of combined bundles are created for the same platform
- Only one set gets copied to the network share
- The system lacks a dedicated job for combined bundle creation
- Logic is duplicated between frosty and patchfrosty scripts

**Reusable Components:**
- Bundle creation logic in frosty.py and patch_frosty.py
- Network share copying mechanisms in move_location_bundles.py
- Platform-specific handling in frosty_build_utils.py
- Jenkins job configuration patterns in groovy files

**Current Feature Flag Implementation:**
- Jenkins groovy files already have conditional logic for combine bundles
- Uses `variant.format.contains('combine')` for detection
- Separate changelist tracking for combined bundles

### Next Steps
1. Design new separate script architecture (Task 1.3)
2. Plan Jenkins job structure for dedicated combined bundle creation (Task 1.2)
3. Design VM infrastructure requirements (Task 1.4)

**Analysis Complete:** 2025-06-25 10:45:00 AM
