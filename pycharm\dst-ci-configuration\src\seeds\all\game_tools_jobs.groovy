package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibPerforce
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibGametool
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.branches

    branches.each { String currentBranch, info ->
        out.println("   Processing branch: $currentBranch")
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = info.project
        }
        def branchFile = GetBranchFile.get_branchfile(project.name, currentBranch)
        def generalSettings = branchFile.general_settings
        def standardJobsSettings = branchFile.standard_jobs_settings
        def branchInfo = info + generalSettings + standardJobsSettings + [branch_name: currentBranch]
        def freestyle_jobs = []

        // Start and check jobs
        if (branchInfo.gametool_settings) {
            out.println('       Processing gametool start job')
            def gametoolStart = pipelineJob("${currentBranch}.gametool.start") {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/gametool/GametoolScheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibGametool.start(gametoolStart, project, branchFile, masterSettings, currentBranch)

            if (branchInfo.gametool_settings.gametools?.containsKey(LibPerforce.GAMETOOL_ICEPICK)) {
                out.println("       Processing ${LibPerforce.GAMETOOL_ICEPICK}")
                def buildIcepick = job("${currentBranch}.gametool.${LibPerforce.GAMETOOL_ICEPICK}") {}
                freestyle_jobs.add(buildIcepick)
                LibGametool.icepickJob(buildIcepick, project, branchFile, masterSettings, currentBranch)
                LibScm.sync_code(buildIcepick, project, branchInfo, '${CODE_CHANGELIST}')
                LibJobDsl.kill_processes(buildIcepick, branchInfo)
                LibJobDsl.initialP4revert(buildIcepick, project, branchInfo, true, false)
                LibJobDsl.archive_non_build_logs(buildIcepick, branchInfo)
                LibJobDsl.postclean_silverback(buildIcepick, project, branchInfo)
            }
            if (branchInfo.gametool_settings.gametools?.containsKey(LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER)) {
                out.println("       Processing ${LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER}")
                def buildFrostbiteDatabaseUpgrader = job("${currentBranch}.gametool.${LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER}") {}
                freestyle_jobs.add(buildFrostbiteDatabaseUpgrader)
                LibGametool.frostbiteDatabaseUpgraderJob(buildFrostbiteDatabaseUpgrader, project, branchFile, masterSettings, currentBranch)
                LibScm.sync_code(buildFrostbiteDatabaseUpgrader, project, branchInfo, '${CODE_CHANGELIST}')
                LibJobDsl.kill_processes(buildFrostbiteDatabaseUpgrader, branchInfo)
                LibJobDsl.initialP4revert(buildFrostbiteDatabaseUpgrader, project, branchInfo, true, false)
                LibJobDsl.archive_non_build_logs(buildFrostbiteDatabaseUpgrader, branchInfo)
                LibJobDsl.postclean_silverback(buildFrostbiteDatabaseUpgrader, project, branchInfo)
            }
            if (branchInfo.gametool_settings.gametools?.containsKey(LibPerforce.GAMETOOL_FROSTYISOTOOL)) {
                out.println("       Processing ${LibPerforce.GAMETOOL_FROSTYISOTOOL}")
                def buildFrostyisotool = job("${currentBranch}.gametool.${LibPerforce.GAMETOOL_FROSTYISOTOOL}") {}
                freestyle_jobs.add(buildFrostyisotool)
                LibGametool.frostyisotoolJob(buildFrostyisotool, project, branchFile, masterSettings, currentBranch)
                LibScm.sync_code(buildFrostyisotool, project, branchInfo, '${CODE_CHANGELIST}')
                LibJobDsl.kill_processes(buildFrostyisotool, branchInfo)
                LibJobDsl.initialP4revert(buildFrostyisotool, project, branchInfo, true, false)
                LibJobDsl.archive_non_build_logs(buildFrostyisotool, branchInfo)
                LibJobDsl.postclean_silverback(buildFrostyisotool, project, branchInfo)
            }
            if (branchInfo.gametool_settings.gametools?.containsKey(LibPerforce.GAMETOOL_DRONE)) {
                out.println("       Processing ${LibPerforce.GAMETOOL_DRONE}")
                def buildDrone = job("${currentBranch}.gametool.${LibPerforce.GAMETOOL_DRONE}") {}
                freestyle_jobs.add(buildDrone)
                LibGametool.droneJob(buildDrone, project, branchFile, masterSettings, currentBranch)
                LibScm.sync_code(buildDrone, project, branchInfo, '${CODE_CHANGELIST}')
                LibJobDsl.kill_processes(buildDrone, branchInfo)
                LibJobDsl.initialP4revert(buildDrone, project, branchInfo, true, false)
                LibJobDsl.archive_non_build_logs(buildDrone, branchInfo)
                LibJobDsl.postclean_silverback(buildDrone, project, branchInfo)
            }
            if (branchInfo.gametool_settings.gametools?.containsKey(LibPerforce.GAMETOOL_FRAMEWORK)) {
                out.println("       Processing ${LibPerforce.GAMETOOL_FRAMEWORK}")
                def buildFramework = job("${currentBranch}.gametool.${LibPerforce.GAMETOOL_FRAMEWORK}") {}
                freestyle_jobs.add(buildFramework)
                LibGametool.frameworkJob(buildFramework, project, branchFile, masterSettings, currentBranch)
                LibScm.sync_code(buildFramework, project, branchInfo, '${CODE_CHANGELIST}')
                LibJobDsl.kill_processes(buildFramework, branchInfo)
                LibJobDsl.initialP4revert(buildFramework, project, branchInfo, true, false)
                LibJobDsl.archive_non_build_logs(buildFramework, branchInfo)
                LibJobDsl.postclean_silverback(buildFramework, project, branchInfo)
            }
            if (branchInfo.gametool_settings.gametools?.containsKey(LibPerforce.GAMETOOL_FBENV)) {
                out.println("       Processing ${LibPerforce.GAMETOOL_FBENV}")
                def buildFbenv = job("${currentBranch}.gametool.${LibPerforce.GAMETOOL_FBENV}") {}
                freestyle_jobs.add(buildFbenv)
                LibGametool.fbenvJob(buildFbenv, project, branchFile, masterSettings, currentBranch)
                LibScm.sync_code(buildFbenv, project, branchInfo, '${CODE_CHANGELIST}')
                LibJobDsl.kill_processes(buildFbenv, branchInfo)
                LibJobDsl.initialP4revert(buildFbenv, project, branchInfo, true, false)
                LibJobDsl.archive_non_build_logs(buildFbenv, branchInfo)
                LibJobDsl.postclean_silverback(buildFbenv, project, branchInfo)
            }
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, currentBranch, branchFile.freestyle_job_trigger_matrix)
    }
}
