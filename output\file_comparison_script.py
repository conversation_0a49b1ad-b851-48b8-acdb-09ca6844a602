#!/usr/bin/env python3
"""
File Comparison Script
Compares two log files and identifies paths that exist in one but not the other.
"""

def read_paths_from_file(filepath):
    """Read paths from a file and return as a set."""
    paths = set()
    try:
        with open(filepath, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line:  # Skip empty lines
                    paths.add(line)
    except FileNotFoundError:
        print('File not found: {}'.format(filepath))
        return set()
    except Exception as e:
        print('Error reading file {}: {}'.format(filepath, str(e)))
        return set()
    
    return paths

def extract_build_number(path):
    """Extract build number from the UNC path for easier identification."""
    # Build number appears to be the number in the path before the final duplicate
    # Example: ...CH1-event\24198366\CH1-event\24198366
    parts = path.split('\\')
    if len(parts) >= 2:
        # Find the build number (numeric part before the final CH1-event)
        for i in range(len(parts) - 1, -1, -1):
            if parts[i].isdigit():
                return parts[i]
    return None

def compare_files(file_a_path, file_b_path):
    """Compare two files and return the differences."""
    print('Reading File A: {}'.format(file_a_path))
    paths_a = read_paths_from_file(file_a_path)
    print('Found {} paths in File A'.format(len(paths_a)))
    
    print('Reading File B: {}'.format(file_b_path))
    paths_b = read_paths_from_file(file_b_path)
    print('Found {} paths in File B'.format(len(paths_b)))
    
    # Find differences
    only_in_a = paths_a - paths_b
    only_in_b = paths_b - paths_a
    
    return only_in_a, only_in_b, paths_a, paths_b

def main():
    """Main function to run the comparison."""
    file_a = r'c:\Users\<USER>\vscode\txt\about_delete_builds.log'
    file_b = r'c:\Users\<USER>\vscode\txt\should_be_delete_from_script.log'
    
    print('=== File Comparison Analysis ===')
    print('File A: {}'.format(file_a))
    print('File B: {}'.format(file_b))
    print('')
    
    only_in_a, only_in_b, all_paths_a, all_paths_b = compare_files(file_a, file_b)
    
    print('\n=== RESULTS ===')
    print('Total paths in File A: {}'.format(len(all_paths_a)))
    print('Total paths in File B: {}'.format(len(all_paths_b)))
    print('Paths only in File A: {}'.format(len(only_in_a)))
    print('Paths only in File B: {}'.format(len(only_in_b)))
    print('')
    
    # Display paths only in File A
    if only_in_a:
        print('=== PATHS IN FILE A BUT NOT IN FILE B ===')
        for i, path in enumerate(sorted(only_in_a), 1):
            build_num = extract_build_number(path)
            print('{}. Build {}: {}'.format(i, build_num, path))
        print('')
    else:
        print('=== NO PATHS FOUND ONLY IN FILE A ===')
        print('')
    
    # Display paths only in File B
    if only_in_b:
        print('=== PATHS IN FILE B BUT NOT IN FILE A ===')
        for i, path in enumerate(sorted(only_in_b), 1):
            build_num = extract_build_number(path)
            print('{}. Build {}: {}'.format(i, build_num, path))
        print('')
    else:
        print('=== NO PATHS FOUND ONLY IN FILE B ===')
        print('')
    
    # Summary of build numbers for easier reference
    print('=== BUILD NUMBER SUMMARY ===')
    builds_a = set()
    builds_b = set()
    
    for path in all_paths_a:
        build_num = extract_build_number(path)
        if build_num:
            builds_a.add(build_num)
    
    for path in all_paths_b:
        build_num = extract_build_number(path)
        if build_num:
            builds_b.add(build_num)
    
    only_builds_a = builds_a - builds_b
    only_builds_b = builds_b - builds_a
    
    if only_builds_a:
        print('Build numbers only in File A: {}'.format(sorted(only_builds_a)))
    else:
        print('No unique build numbers in File A')
        
    if only_builds_b:
        print('Build numbers only in File B: {}'.format(sorted(only_builds_b)))
    else:
        print('No unique build numbers in File B')

if __name__ == '__main__':
    main()
