"""
test_combined_bundle_creator.py

Unit tests for the combined_bundle_creator.py script.
Tests cover both the new separate workflow and legacy workflow compatibility.
"""
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock, call
import click.testing

from dice_elipy_scripts.combined_bundle_creator import cli, _process_bundles_for_combination, _create_head_bundles


class TestCombinedBundleCreator(unittest.TestCase):
    """Test cases for the combined bundle creator script."""

    def setUp(self):
        """Set up test fixtures."""
        self.runner = click.testing.CliRunner()
        self.test_temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        if os.path.exists(self.test_temp_dir):
            shutil.rmtree(self.test_temp_dir)

    def test_cli_basic_parameters_validation(self):
        """Test that CLI validates basic required parameters."""
        # Test missing required parameters
        result = self.runner.invoke(cli, [])
        self.assertNotEqual(result.exit_code, 0)
        
        # Test with minimal required parameters
        result = self.runner.invoke(cli, [
            'win64', 'head',
            '--data-branch', 'test-branch',
            '--data-changelist', '12345',
            '--code-branch', 'test-code-branch', 
            '--code-changelist', '67890'
        ])
        # Should not fail on parameter validation
        self.assertIn('test-branch', result.output or '')

    def test_feature_flag_disabled_uses_legacy(self):
        """Test that feature flag disabled properly delegates to legacy workflow."""
        with patch('dice_elipy_scripts.combined_bundle_creator._run_legacy_workflow') as mock_legacy, \
             patch('dice_elipy_scripts.combined_bundle_creator.add_sentry_tags'):
            
            result = self.runner.invoke(cli, [
                'win64', 'head',
                '--data-branch', 'test-branch',
                '--data-changelist', '12345',
                '--code-branch', 'test-code-branch',
                '--code-changelist', '67890',
                '--feature-flag-disabled',
                '--use-separate-workflow'
            ])
            
            # Should call legacy workflow
            mock_legacy.assert_called_once()
            
    def test_feature_flag_enabled_uses_separate_workflow(self):
        """Test that feature flag enabled uses the new separate workflow."""
        with patch('dice_elipy_scripts.combined_bundle_creator._create_head_bundles') as mock_head, \
             patch('dice_elipy_scripts.combined_bundle_creator.filer.FilerUtils'), \
             patch('dice_elipy_scripts.combined_bundle_creator.add_sentry_tags'):
            
            result = self.runner.invoke(cli, [
                'win64', 'head',
                '--data-branch', 'test-branch',
                '--data-changelist', '12345',
                '--code-branch', 'test-code-branch',
                '--code-changelist', '67890',
                '--feature-flag-enabled',
                '--use-separate-workflow'
            ])
            
            # Should call new workflow
            mock_head.assert_called_once()

    def test_delta_bundle_parameters_validation(self):
        """Test that delta bundle creation validates baseline parameters."""
        result = self.runner.invoke(cli, [
            'win64', 'delta',
            '--data-branch', 'test-branch',
            '--data-changelist', '12345',
            '--code-branch', 'test-code-branch',
            '--code-changelist', '67890',
            '--feature-flag-enabled'
            # Missing baseline parameters
        ])
        
        # Should fail due to missing baseline parameters
        self.assertNotEqual(result.exit_code, 0)
        self.assertIn('baseline', result.output.lower())

    def test_delta_bundle_with_baseline_parameters(self):
        """Test delta bundle creation with proper baseline parameters."""
        with patch('dice_elipy_scripts.combined_bundle_creator._create_delta_bundles') as mock_delta, \
             patch('dice_elipy_scripts.combined_bundle_creator.filer.FilerUtils'), \
             patch('dice_elipy_scripts.combined_bundle_creator.add_sentry_tags'):
            
            result = self.runner.invoke(cli, [
                'win64', 'delta',
                '--data-branch', 'test-branch',
                '--data-changelist', '12345',
                '--code-branch', 'test-code-branch',
                '--code-changelist', '67890',
                '--feature-flag-enabled',
                '--baseline-code-branch', 'baseline-code',
                '--baseline-code-changelist', '11111',
                '--baseline-data-branch', 'baseline-data',
                '--baseline-data-changelist', '22222'
            ])
            
            # Should call delta workflow
            mock_delta.assert_called_once()

    @patch('dice_elipy_scripts.combined_bundle_creator.filer_paths.get_bundles_path')
    @patch('dice_elipy_scripts.combined_bundle_creator.filer_paths.get_frosty_base_build_path')
    @patch('dice_elipy_scripts.combined_bundle_creator.os.path.exists')
    @patch('dice_elipy_scripts.combined_bundle_creator.os.makedirs')
    def test_create_head_bundles_path_handling(self, mock_makedirs, mock_exists, mock_base_path, mock_bundles_path):
        """Test that head bundle creation handles paths correctly."""
        # Setup mocks
        mock_bundles_path.return_value = '/source/bundles'
        mock_base_path.return_value = '/output/base'
        mock_exists.return_value = True
        
        filer_utils_mock = MagicMock()
        
        # Call function
        _create_head_bundles(
            'win64', 'test-data-branch', '12345', 'test-code-branch', '67890',
            'ww', 'final', None, filer_utils_mock
        )
        
        # Verify path construction
        mock_bundles_path.assert_called_once_with(
            data_branch='test-data-branch',
            data_changelist='12345',
            code_branch='test-code-branch',
            code_changelist='67890',
            platform='win64',
            bundles_dir_name='bundles'
        )
        
        mock_base_path.assert_called_once_with(
            data_branch='test-data-branch',
            data_changelist='12345',
            code_branch='test-code-branch',
            code_changelist='67890',
            platform='win64'
        )

    @patch('dice_elipy_scripts.combined_bundle_creator.core.robocopy')
    @patch('dice_elipy_scripts.combined_bundle_creator.core.rmtree')
    @patch('dice_elipy_scripts.combined_bundle_creator.os.path.exists')
    def test_process_bundles_for_combination(self, mock_exists, mock_rmtree, mock_robocopy):
        """Test bundle processing logic."""
        source_path = '/source/bundles'
        output_path = '/output/combine_bundles'
        
        # Test with existing output path
        mock_exists.return_value = True
        
        _process_bundles_for_combination(source_path, output_path, 'win64', 'ww', 'final')
        
        # Should remove existing output and copy new bundles
        mock_rmtree.assert_called_once_with(output_path)
        mock_robocopy.assert_called_once_with(source_path, output_path)

    def test_platform_specific_processing_coverage(self):
        """Test that platform-specific processing handles different platforms."""
        with patch('dice_elipy_scripts.combined_bundle_creator.LOGGER') as mock_logger:
            from dice_elipy_scripts.combined_bundle_creator import _apply_platform_specific_processing
            
            # Test Windows platform
            _apply_platform_specific_processing('/output', 'win64', 'ww', 'final')
            mock_logger.info.assert_any_call("Applying Windows-specific bundle processing")
            
            # Test PlayStation platform
            _apply_platform_specific_processing('/output', 'ps5', 'ww', 'final')
            mock_logger.info.assert_any_call("Applying PlayStation-specific bundle processing")
            
            # Test Xbox platform
            _apply_platform_specific_processing('/output', 'xbsx', 'ww', 'final')
            mock_logger.info.assert_any_call("Applying Xbox-specific bundle processing")

    def test_legacy_workflow_delegation(self):
        """Test that legacy workflow properly delegates to existing scripts."""
        with patch('dice_elipy_scripts.combined_bundle_creator.LOGGER') as mock_logger:
            from dice_elipy_scripts.combined_bundle_creator import _run_legacy_workflow
            
            # Test head bundle delegation
            _run_legacy_workflow('win64', 'head', 'branch', '123', 'code-branch', '456', 'ww', 'final')
            mock_logger.info.assert_any_call("Would call frosty.py with combine bundle parameters")
            
            # Test delta bundle delegation
            _run_legacy_workflow('win64', 'delta', 'branch', '123', 'code-branch', '456', 'ww', 'final')
            mock_logger.info.assert_any_call("Would call patch_frosty.py with combine bundle parameters")

    def test_error_handling_missing_source_bundles(self):
        """Test error handling when source bundles don't exist."""
        with patch('dice_elipy_scripts.combined_bundle_creator.filer_paths.get_bundles_path') as mock_path, \
             patch('dice_elipy_scripts.combined_bundle_creator.os.path.exists') as mock_exists, \
             patch('dice_elipy_scripts.combined_bundle_creator.add_sentry_tags'):
            
            mock_path.return_value = '/nonexistent/bundles'
            mock_exists.return_value = False
            
            result = self.runner.invoke(cli, [
                'win64', 'head',
                '--data-branch', 'test-branch',
                '--data-changelist', '12345',
                '--code-branch', 'test-code-branch',
                '--code-changelist', '67890',
                '--feature-flag-enabled'
            ])
            
            # Should exit with error
            self.assertNotEqual(result.exit_code, 0)

    def test_custom_network_share_path(self):
        """Test using custom network share path for output."""
        with patch('dice_elipy_scripts.combined_bundle_creator._create_head_bundles') as mock_head, \
             patch('dice_elipy_scripts.combined_bundle_creator.filer.FilerUtils'), \
             patch('dice_elipy_scripts.combined_bundle_creator.add_sentry_tags'):
            
            custom_path = '/custom/network/share'
            result = self.runner.invoke(cli, [
                'win64', 'head',
                '--data-branch', 'test-branch',
                '--data-changelist', '12345',
                '--code-branch', 'test-code-branch',
                '--code-changelist', '67890',
                '--feature-flag-enabled',
                '--network-share-path', custom_path
            ])
            
            # Should pass custom path to function
            args, kwargs = mock_head.call_args
            self.assertEqual(args[6], custom_path)


if __name__ == '__main__':
    unittest.main()
