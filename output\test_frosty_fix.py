#!/usr/bin/env python3
"""
Test script to verify the frosty build scanning fix
"""

import os
import sys

# Add the elipy2 module to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'pycharm', 'elipy2'))

from elipy2.expire import Expire

def test_frosty_scanning_fix():
    """Test the fixed frosty scanning logic"""
    
    print("=" * 80)
    print("Testing Frosty Build Scanning Fix")
    print("=" * 80)
    
    # Create an Expire instance
    expire = Expire()
    
    # Test paths
    parent_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    ch1_event_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event"
    
    print(f"Parent path: {parent_path}")
    print(f"CH1-event path: {ch1_event_path}")
    print()
    
    # Test 1: Scan parent path (should find all branches)
    print("Test 1: Scanning parent path (all branches)")
    try:
        parent_builds = expire._scan_frosty_builds_fixed_depth(parent_path)
        print(f"  Builds found: {len(parent_builds)}")
        
        # Group by branch
        branch_counts = {}
        for build in parent_builds:
            # Extract branch name from path
            parts = build.replace('\\', '/').split('/')
            if len(parts) >= 6:
                branch = parts[-5]  # 5 levels from end: .../BattlefieldGame/branch/CL1/branch/CL2
                branch_counts[branch] = branch_counts.get(branch, 0) + 1
        
        print("  Builds by branch:")
        for branch, count in sorted(branch_counts.items()):
            print(f"    {branch}: {count} builds")
            
        ch1_event_parent_count = branch_counts.get('CH1-event', 0)
        print(f"  CH1-event builds from parent scan: {ch1_event_parent_count}")
        
    except Exception as exc:
        print(f"  Error: {exc}")
        parent_builds = []
        ch1_event_parent_count = 0
    
    print()
    
    # Test 2: Scan specific CH1-event path
    print("Test 2: Scanning specific CH1-event path")
    try:
        ch1_event_builds = expire._scan_frosty_builds_fixed_depth(ch1_event_path)
        print(f"  Builds found: {len(ch1_event_builds)}")
        
        if ch1_event_builds:
            print("  Sample builds:")
            for build in ch1_event_builds[:3]:
                print(f"    {build}")
                
    except Exception as exc:
        print(f"  Error: {exc}")
        ch1_event_builds = []
    
    print()
    
    # Test 3: Compare results
    print("Test 3: Comparing results")
    print(f"  CH1-event builds from parent scan: {ch1_event_parent_count}")
    print(f"  CH1-event builds from direct scan: {len(ch1_event_builds)}")
    
    if ch1_event_parent_count == len(ch1_event_builds) and len(ch1_event_builds) > 0:
        print("  ✅ SUCCESS: Both methods found the same number of builds")
    elif len(ch1_event_builds) > 0:
        print("  ✅ SUCCESS: Direct scan found builds (this is the main fix)")
    else:
        print("  ❌ FAILURE: No builds found by either method")
    
    print()
    
    # Test 4: Test the branch detection logic
    print("Test 4: Testing branch detection logic")
    
    # Test if the function correctly identifies specific branch paths
    test_paths = [
        (parent_path, "Parent path", False),
        (ch1_event_path, "CH1-event path", True),
        (r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-qol", "CH1-qol path", True),
    ]
    
    for test_path, description, expected_is_branch in test_paths:
        print(f"  Testing {description}: {test_path}")
        
        if not os.path.exists(test_path):
            print(f"    Path does not exist")
            continue
            
        # Check if path contains CL directories (branch detection logic)
        try:
            entries = os.listdir(test_path)
            has_cl_dirs = any(
                entry.isdigit() and len(entry) >= 7 and os.path.isdir(os.path.join(test_path, entry))
                for entry in entries
            )
            
            print(f"    Has CL directories: {has_cl_dirs}")
            print(f"    Expected to be branch: {expected_is_branch}")
            
            if has_cl_dirs == expected_is_branch:
                print(f"    ✅ Correct detection")
            else:
                print(f"    ❌ Incorrect detection")
                
        except (OSError, PermissionError) as exc:
            print(f"    Error reading directory: {exc}")
    
    print()
    print("=" * 80)
    print("Test Summary")
    print("=" * 80)
    
    if len(ch1_event_builds) > 0:
        print("✅ OVERALL SUCCESS: The fix allows CH1-event builds to be found")
        print(f"   CH1-event builds found: {len(ch1_event_builds)}")
        print("   This should resolve the Jenkins issue where 0 builds were found")
    else:
        print("❌ OVERALL FAILURE: CH1-event builds still not found")
        print("   Additional investigation needed")

if __name__ == "__main__":
    test_frosty_scanning_fix()
