#**********************************************
#               fb1_dev_na_ps PIPE                 *
#**********************************************
.default-fb1-dev-na-ps-variables:
  extends: .secrets-fb1_dev_na_ps
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "fb1_dev_na_ps"
    WORKING_DIR: "projects/fb1_dev_na_ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-fb1_dev_na_ps.json"
    ansible_main_module: fb_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1_dev_na_ps.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-fb1-dev-na-ps:
  extends: ['.default-fb1-dev-na-ps-variables', '.prepare_config']

validate-fb1-dev-na-ps:
  extends: ['.default-fb1-dev-na-ps-variables', '.validation_steps']

plan-fb1-dev-na-ps:
  needs:
    - job: validate-fb1-dev-na-ps
    - job: prepare-json-config-fb1-dev-na-ps
  extends: ['.default-fb1-dev-na-ps-variables','.plan_steps']

apply-fb1-dev-na-ps:
  needs:
    - job: plan-fb1-dev-na-ps
    - job: prepare-json-config-fb1-dev-na-ps
  extends: ['.default-fb1-dev-na-ps-variables','.apply_steps']

attache-fb1-dev-na-ps:
  needs:
    - job: apply-fb1-dev-na-ps
    - job: prepare-json-config-fb1-dev-na-ps
  extends: ['.default-fb1-dev-na-ps-variables','.attache_vmdk_step']

sync-fb1-dev-na-ps:
  needs:
    - job: apply-fb1-dev-na-ps
    - job: attache-fb1-dev-na-ps
    - job: prepare-json-config-fb1-dev-na-ps
  extends: ['.default-fb1-dev-na-ps-variables','.sync_vmdk_step']

ansible-fb1-dev-na-ps:
  needs:
    - job: apply-fb1-dev-na-ps
    - job: sync-fb1-dev-na-ps
    - job: prepare-json-config-fb1-dev-na-ps
  extends: ['.default-fb1-dev-na-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
