#**********************************************
#               VMDK RUNNERS PIPE
#**********************************************
.default-eala-vmdk-runners-variables:
  extends: .secrets-eala_vmdk_runners
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "eala_vmdk_runners"
    WORKING_DIR: "projects/gitlab_runners/eala_vmdk_runners"
    NODE_INFO_FILE: "node-info-eala-vmdk-runners.json"
    VC_HOST: eala-vcenter.la.ad.ea.com
    VAULT_RATE_LIMIT: 50
    VAULT_ADDR: "https://ess.ea.com"
    VAULT_NAMESPACE: "cds-dre-prod"
    VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
    VAULT_AUTH_LOGIN_PATH: "auth/jwt/gitlab/login"
    SECRET_TOP: "secrets/kv/cobra/automation/gitlab"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log
    ansible_main_module: vmdk_runners_silverback
    SILVERBACK_CONFIG_JSON_FILE: "eala_vmdk_runners.json"

prepare-json-config-eala-vmdk-runners:
  extends: ['.default-eala-vmdk-runners-variables', '.prepare_config']

validate-eala-vmdk-runners:
  extends: ['.default-eala-vmdk-runners-variables', '.validation_steps']

plan-eala-vmdk-runners:
  extends: ['.default-eala-vmdk-runners-variables', '.plan_steps_runner']

apply-eala-vmdk-runners:
  needs:
    - job: plan-eala-vmdk-runners
  extends: ['.default-eala-vmdk-runners-variables', '.apply_steps']

ansible-eala-vmdk-runners:
  needs:
    - job: apply-eala-vmdk-runners
    - job: prepare-json-config-eala-vmdk-runners
  extends: ['.default-eala-vmdk-runners-variables', '.ansible_common_secrets', '.run_ansible_step']
