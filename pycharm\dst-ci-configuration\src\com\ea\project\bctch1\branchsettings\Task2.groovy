package com.ea.project.bctch1.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.bctch1.BctCh1

class Task2 {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                                : project.dataset,
        elipy_call                             : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call                     : project.elipy_install_call,
        frostbite_licensee                     : project.frostbite_licensee,
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : project.azure_elipy_call,
        azure_elipy_install_call               : project.azure_elipy_install_call,
        azure_workspace_root                   : project.azure_workspace_root,
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [
                    config: 'release',
                ],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
                (LibPerforce.GAMETOOL_FROSTYISOTOOL)              : [],
                (LibPerforce.GAMETOOL_DRONE)                      : [],
                (LibPerforce.GAMETOOL_FRAMEWORK)                  : [],
                (LibPerforce.GAMETOOL_FBENV)                      : [],
            ],
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: '@daily',
            referenceJob: '.data.start',
        ),
    ]
    static Map code_settings = [
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        poolbuild_data    : true,
        slack_channel_data: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map frosty_settings = [
        poolbuild_frosty    : true,
        timeout_hours_frosty: 4,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                         : 'Task2Levels',
        enable_lkg_p4_counters        : true,
        custom_tests                  : [],
        data_reference_job            : 'task2.code.start',
        extra_data_args               : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args             : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        frosty_reference_job          : 'task2.data.start',
        server_asset                  : 'Task2Levels',
        skip_icepick_settings_file    : true,
        strip_symbols                 : false,
        move_location_parallel        : true,
        new_locations                 : [
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = [
        [name: 'ps5', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
        [name: 'linux64'],
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64'],
        [platform: 'ps5'],
    ]
}
