# Mandatory
default:
  script_path:
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "fb1"
  studio_location: "DiceStockholm"
  avalanche_symbol_server: "dice-wal-sym.dice.ad.ea.com"
  symbol_stores_suffix: "SymStore"
  vault_destination: "\\\\filer.dice.ad.ea.com\\builds\\Vault\\fb1"
  ant_local_dir: "Raw\\Animations\\WhitesharkAnimations"

  bilbo_api_version: 2
  bilbo_url: "https://fb1-bilbo-eck.cobra.dre.ea.com"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\DiceUpgradeNext\\fb1"
  alternate_build_shares:
    fb1: "\\\\***********\\builds"

  filer_api_url: "https://it-sweden-api.dice.se/api/v1/smb"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://dice-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  recompression_cache:
    win64: "kraken-123eba.dice.ad.ea.com"
    server: "kraken-123eba.dice.ad.ea.com"
    xb1: "kraken-8a5a05.dice.ad.ea.com"
    xbsx: "kraken-af9d92.dice.ad.ea.com"
    ps4: "kraken-60a94a.dice.ad.ea.com"
    ps5: "kraken-1a2821.dice.ad.ea.com"
    linuxserver: "kraken-4379f0.dice.ad.ea.com"
    linux64: "kraken-4379f0.dice.ad.ea.com"

  denuvo:
    project:
      retail: ''
      trial: ''
    servers: ''
    server_url: 'https://oetk2-prod.codefusion.technology'
  game_binaries: ['ii_trial.exe', 'ii.exe']

  ps4_disk_code_branch: ShippingPS4
  ps4_disk_code_changelist: 970080
  ps4_disk_data_branch: ShippingPS4
  ps4_disk_data_changelist: 970080

  use_onefs_api: "false"
  elsa_patch: "false"
  skip_frosty_game_config_flags: "false"

  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
    # Get Glacier server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://int.ess.ea.com'
      namespace: 'eadp-np'
      role_id: '5lkt'
      secret_id_envvar: 'dice-online-gla-prod-secret-id' # Matches a Jenkins Credential
      mount_point: 'approle-identity'
      files:
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslKey'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.key'
        - path: 'secrets/certs/data/identity/5lkt/other/GLACIER_BK_OL_SERVER_20240130_080623'
          key: 'sslCert'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\GLACIER_BK_OL_SERVER_20240130_080623.client.int.eadp.ea.com.crt'
    - where:
        project_account: true
      url: 'https://ess.ea.com'
      namespace:  'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/fb1/accounts/main_account'
    - where:
        project_secrets: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/fb1/secrets'
    - where:
        build_type: drone
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      values:
        - '/cobra/automation/shift/automation_credentials'
    # Credentials for pushing to Azure fileshare
    - where:
        fb1_azure_fileshare: true
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9'
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
      values:
        - '/cobra/automation/projects/fb1/secrets'

  # The numbers here for Code are 1 per CL
  # The numbers for frosty/symbols are 1 per CL
  # num variants (per platform/config/region) change per branch between 5-12
  retention_categories:
      code:
        - 'default' :              50
      code_nomaster:
        - 'default' :              10
      frosty\walrus:
        - 'default' :              30
      frosty\Frostbite:
        - 'default' :              6
        - 'dev-na-dice-next-build-data': 20
      symbols:
        - 'default' :              0
      avalanchestate:
        - 'default' :              0
      ant_cache:
        - 'default' :              0
      webexport:
        - 'default' :              100
      tnt_local:
        - 'default' :              0
      expressiondebugdata\Frostbite:
        - 'default' :               50

  azure_path_retention:
  - secret_context: "fb1_azure_fileshare"
    fileshares:
      - fileshare_name: "builds"
        paths:
          - Code/dev-na-dice-next-build: 15
  spin_retention:
    - 'default': 5

  smoke_retention:
    - 'default': 5

  shift_retention: 100
  release_candidate_retention: 2
  shift_submission_path: "\\\\filer.dice.ad.ea.com\\builds\\Shift\\auto_submissions"
  shift_config_file: "shift_config_fb1.yml"
  shift_tool_url: "https://artifacts.at.ea.com/artifactory/list/dreeu-generic-local/shiftsubmission/5.1.0"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe' # should be killed before cl.exe
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'cl.exe'
    - 'Casablanca.Main_Win64.exe'
    - 'orbis-pub-cmd.exe'
    - 'orbis-ctrl.exe'
    - 'orbis-symupload.exe'
    - 'prospero-symupload.exe'
    - 'orbis-clang.exe'
    - 'nant.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'animationapp.exe'
    - 'Icepick.exe'
    - 'fbenvcore.exe'
    - 'eapm.exe'

  path_retention:
    - \\filer.dice.ad.ea.com\builds\DiceUpgradeNext\fb1\crashdumps\pipeline_crashdumps: 100
    - \\filer.dice.ad.ea.com\builds\DiceUpgradeNext\fb1\baselines\Frostbite\dev-na-dice-next-build-data: 3
    - \\filer.dice.ad.ea.com\Builds\DiceUpgradeNext\fb1\\offsite\dev-na-dice-next-build: 100

  snowcache_host:
    win64game: 'sc2-95ccdb.dice.ad.ea.com'
    win64trial: 'sc2-95ccdb.dice.ad.ea.com'
    win64server: 'sc2-60a76f.dice.ad.ea.com'
    linux64server: 'sc2-60a76f.dice.ad.ea.com'
    linux64: 'sc2-6485f7.dice.ad.ea.com'
    ps4: 'sc2-3ca102.dice.ad.ea.com'
    ps5: 'sc2-763f6a.dice.ad.ea.com'
    xb1: 'sc2-cd3e4d.dice.ad.ea.com'
    xb1gdk: 'sc2-cd3e4d.dice.ad.ea.com'
    xbsx: 'sc2-fd8d6f.dice.ad.ea.com'
    tool: 'sc2-95ccdb.dice.ad.ea.com'

  avalanche_state_lifetime: # Used by avalanche.remote_clone_db(), but falls back to default value if missing
    default: 2 # Optional because of the fallback

  metadata_manager:
    primary:
      name: "bilbo_v2"
      url: "https://fb1-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "https://fb1-bilbo-eck.cobra.dre.ea.com"
        attributes_filename: "bilbo_v1.json"
  # enable_executed_command_log: true

DiceStockholm:
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\DiceUpgradeNext\\fb1"
  licensee_code_folder_name: "Code\\DICE"
earo:
  build_share: "\\\\ro-nas-dice.eamobile.ad.ea.com\\sync"
  bilbo_url: "https://fb1-bilbo-eck.cobra.dre.ea.com"
  studio_location: "EARO"
  metadata_manager:
    primary:
      name: "bilbo"
      index: "earo_bilbo"
      url: "https://fb1-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "earo_build.json"

  retention_categories:
    code:
      - 'default': 20
      - 'dev-na-dice-next-build': 10
RippleEffect:
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\Frostbite"
  bilbo_url: "https://gnt-bilbo-eck.cobra.dre.ea.com"
  licensee_code_folder_name: "Code\\DICE"
  metadata_manager:
    primary:
      name: "bilbo_v2"
      index: "bilbo_v2"
      url: "https://gnt-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "eala_build.json"

  recompression_cache:
    win64: "kreala-5cef3b.la.ad.ea.com"
    server: "kreala-743e07.la.ad.ea.com"
    xb1: "kreala-457904.la.ad.ea.com"
    xbsx: "kreala-e5bb10.la.ad.ea.com"
    ps4: "kreala-93e74d.la.ad.ea.com"
    ps5: "kreala-9a3792.la.ad.ea.com"
    linux64: "kreala-d6d34d.la.ad.ea.com"
    linuxserver: "kreala-50317c.la.ad.ea.com"

  retention_categories:
    frosty\Frostbite:
      - 'default':                    20
      - '2024_1_dev-bf':              8
    WebExport:
      - 'default':                    100
      - '2024_1_dev-bf_bfdata-only':  100
    code:
      - 'default':                    100

  path_retention:
    - \\dice-la.la.ad.ea.com\Builds\Frostbite\offsite\2024_1_dev-bf_code-only: 100

  spin_retention:
    - 'default': 5
  smoke_retention:
    - 'default': 5
    - 'dev-na': 10
test:
  bilbo_url: "https://bilbo-dretest.thor.dice.se/"
  build_share: "\\\\filer.dice.ad.ea.com\\builds\\DiceUpgradeNext\\fb1\\test"
