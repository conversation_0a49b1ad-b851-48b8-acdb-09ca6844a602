#!/usr/bin/env python3
"""
Debug script to test branch discovery logic for ch1-event issue
"""

import os
import sys


def test_disk_branch_discovery():
    """Test the disk-based branch discovery for ch1-event path"""
    
    path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    print(f"Testing branch discovery for path: {path}")
    
    if not os.path.exists(path):
        print(f"ERROR: Path does not exist: {path}")
        return False
    
    try:
        # Get all directories under the path
        directories = [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
        print(f"Found {len(directories)} directories: {sorted(directories)}")
        
        # Check specifically for ch1-event
        if "ch1-event" in directories:
            print("✓ SUCCESS: ch1-event found in branch directories")
            
            # Check structure inside ch1-event
            ch1_event_path = os.path.join(path, "ch1-event")
            try:
                cl_dirs = [d for d in os.listdir(ch1_event_path) 
                          if os.path.isdir(os.path.join(ch1_event_path, d)) and d.isdigit()]
                print(f"  Found {len(cl_dirs)} CL directories under ch1-event")
                
                if cl_dirs:
                    sample_cl = cl_dirs[0]
                    sample_cl_path = os.path.join(ch1_event_path, sample_cl)
                    try:
                        inner_dirs = os.listdir(sample_cl_path)
                        print(f"  Sample CL {sample_cl} contains: {inner_dirs}")
                    except Exception as e:
                        print(f"  Cannot read sample CL {sample_cl}: {e}")
                        
                return True
            except Exception as e:
                print(f"  ERROR reading ch1-event directory: {e}")
                return False
        else:
            print("✗ FAILURE: ch1-event NOT found in branch directories")
            return False
            
    except Exception as e:
        print(f"ERROR scanning path: {e}")
        return False


def test_existing_logic_simulation():
    """Simulate the existing bilbo-first logic"""
    print("\n=== Testing existing logic simulation ===")
    
    # This would normally query bilbo first, but since we don't have bilbo access here,
    # we'll simulate what happens when bilbo returns incomplete data
    
    path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    # Simulate bilbo returning no branches (incomplete data)
    bilbo_branches = set()  # Empty set simulating incomplete bilbo data
    
    if os.path.exists(path):
        disk_branches = {d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))}
        print(f"Bilbo branches (simulated): {len(bilbo_branches)}")
        print(f"Disk branches: {len(disk_branches)} - {sorted(disk_branches)}")
        
        # The existing logic only falls back to disk if disk has MORE branches than bilbo
        if len(disk_branches) > len(bilbo_branches):
            print("✓ Existing logic WOULD use disk data (fallback triggered)")
            final_branches = bilbo_branches.union(disk_branches)
        else:
            print("✗ Existing logic would NOT use disk data (fallback not triggered)")
            final_branches = bilbo_branches
            
        print(f"Final branches with existing logic: {sorted(final_branches)}")
        return "ch1-event" in final_branches
    
    return False


def test_new_logic_simulation():
    """Simulate the new disk-first logic"""
    print("\n=== Testing new disk-first logic ===")
    
    path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    # New logic: disk first
    disk_branches = set()
    
    if os.path.exists(path):
        path_lower = path.lower()
        
        if "frosty" in path_lower:
            print("Detected frosty path - using frosty-specific logic")
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    try:
                        subitems = os.listdir(item_path)
                        # Look for numeric directories (CLs)
                        has_cl_dirs = any(subitem.isdigit() and len(subitem) >= 7 
                                        for subitem in subitems 
                                        if os.path.isdir(os.path.join(item_path, subitem)))
                        if has_cl_dirs:
                            disk_branches.add(item)
                            print(f"  Added branch: {item} (has CL directories)")
                    except (OSError, PermissionError):
                        disk_branches.add(item)
                        print(f"  Added branch: {item} (unable to scan, assuming branch)")
        
        print(f"New logic found branches: {sorted(disk_branches)}")
        return "ch1-event" in disk_branches
    
    return False


if __name__ == "__main__":
    print("=== CH1-Event Branch Discovery Debug ===")
    
    # Test basic disk discovery
    basic_success = test_disk_branch_discovery()
    
    # Test existing logic
    existing_success = test_existing_logic_simulation()
    
    # Test new logic
    new_success = test_new_logic_simulation()
    
    print("\n=== SUMMARY ===")
    print(f"Basic disk scan finds ch1-event: {'✓' if basic_success else '✗'}")
    print(f"Existing logic finds ch1-event: {'✓' if existing_success else '✗'}")
    print(f"New logic finds ch1-event: {'✓' if new_success else '✗'}")
    
    if new_success and not existing_success:
        print("\n✓ SUCCESS: New logic fixes the branch discovery issue!")
    elif existing_success:
        print("\n? WARNING: Both logics work, issue might be elsewhere")
    else:
        print("\n✗ FAILURE: Neither logic finds ch1-event")
