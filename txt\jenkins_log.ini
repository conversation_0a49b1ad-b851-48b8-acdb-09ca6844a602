21:23:27 Started by user <PERSON><PERSON>, <PERSON>ang

21:23:27 Rebuilds build #9

21:23:27 Running as SYSTEM
21:23:27 [EnvInject] - Loading node environment variables.
21:23:27 [EnvInject] - Preparing an environment for the build.
21:23:27 [EnvInject] - Keeping Jenkins system variables.
21:23:27 [EnvInject] - Keeping Jenkins build variables.
21:23:27 [EnvInject] - Executing and processing the following script content: 
21:23:27 @ECHO OFF
21:23:27 DEL /F /Q /S D:\dev\logs\* > nul 2>&1
21:23:27 mkdir D:\dev\logs 2> NUL
21:23:27 START /wait taskkill /f /im python.exe >> D:\dev\logs\initial_p4_code_and_data_revert.log 2>&1
21:23:27 START /wait taskkill /f /im fbenvconfigservice.exe >> D:\dev\logs\initial_p4_code_and_data_revert.log 2>&1
21:23:27 p4.exe -p oh-p4edge-fb.eu.ad.ea.com:2001 -u %USERDOMAIN%\%USERNAME% -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\dev\logs\initial_p4_code_and_data_revert.log 2>&1
21:23:27 RD /S /Q D:\dev\Python
21:23:27 p4.exe -p oh-p4edge-fb.eu.ad.ea.com:2001 -u %USERDOMAIN%\%USERNAME% -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\dev\TnT\masterconfig.xml > NUL
21:23:27 IF NOT ERRORLEVEL 1 del /s /q /f D:\dev\TnT\Bin\Python\*.pyc >> D:\dev\logs\initial_p4_code_and_data_revert.log 2>&1
21:23:27 p4.exe -p oh-p4edge-fb.eu.ad.ea.com:2001 -u %USERDOMAIN%\%USERNAME% -c jenkins-%NODE_NAME%-codestream clean -m D:\dev\TnT\Bin\Python/...
21:23:27 exit 0
21:23:27 
21:23:27 [JenkinsSlave] $ cmd /c call C:\Users\<USER>\AppData\Local\Temp\jenkins13016896315854636522.bat
21:23:34 D:\dev\TnT\Bin\Python/... - no file(s) to reconcile.
21:23:34 [EnvInject] - Script executed successfully.
21:23:34 [EnvInject] - Injecting contributions.
21:23:34 Building remotely on bct1-0f002b
 (bct_criterion deleter) in workspace D:\dev
21:23:34 Executor number at runtime: 0
21:23:34 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:23:34 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:23:34 ... p4 counter change +
21:23:34 ... p4 changes -m1 -ssubmitted //jenkins-bct1-0f002b-codestream/...@24315856 +
21:23:45 P4: found 24278823 revision in //jenkins-bct1-0f002b-codestream/...@24315856
21:23:45 ... p4 changes -m1 -ssubmitted //jenkins-bct1-0f002b-codestream/...@24277823,24278823 +
21:23:50 P4: found 24278823 revision in //jenkins-bct1-0f002b-codestream/...@24277823,24278823
21:23:50 ... p4 repos -C +
21:23:50 P4: builds: 24278823 
21:23:50 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:23:50 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:23:50 
21:23:50 P4 Task: establishing connection.
21:23:50 ... server: oh-p4edge-fb.eu.ad.ea.com:2001
21:23:50 ... node: bct1-0f002b
21:23:50 Building on Node: bct1-0f002b
21:23:50 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:23:50 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:23:50 
21:23:50 P4 Task: establishing connection.
21:23:50 ... server: oh-p4edge-fb.eu.ad.ea.com:2001
21:23:50 ... node: bct1-0f002b
21:23:50 
21:23:50 P4 Task: reverting all pending and shelved revisions.
21:23:50 ... p4 revert D:\dev/...
 +
21:23:50 ... rm [abandoned files]
21:23:50 duration: (16ms)
21:23:50 
21:23:50 P4 Task: syncing files at change: 24278823
21:23:50 ... p4 sync --parallel=threads=8 -q D:\dev/...@24278823
 +
21:24:06 duration: 0m 15s
21:24:06 
21:24:06 P4: saving built changes.
21:24:06 WARNING: duplicate syncID found: jenkins-NODE_NAME-codestream
21:24:06 Found last change 24278823 on syncID jenkins-NODE_NAME-codestream
21:24:06 Found last change 24278823 on syncID jenkins-NODE_NAME-codestream
21:24:06 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:06 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:06 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:06 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:06 ... done
21:24:06 
21:24:06 Executor number at runtime: 0
21:24:06 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:06 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:06 ... p4 counter change +
21:24:06 ... p4 changes -m1 -ssubmitted //jenkins-bct1-0f002b-codestream/...@24315859 +
21:24:17 Change 24278823 on 2025/07/01 by DICE\svc_bct.dre.build.02@jenkins-bct2-ad3a07-codestream 'Drone binaries for CL 24278534
21:24:17 '
21:24:17 21:24:17 P4: found 24278823 revision in //jenkins-bct1-0f002b-codestream/...@24315859
21:24:17 ... p4 changes -m1 -ssubmitted //jenkins-bct1-0f002b-codestream/...@24277823,24278823 +
21:24:21 Change 24278823 on 2025/07/01 by DICE\svc_bct.dre.build.02@jenkins-bct2-ad3a07-codestream 'Drone binaries for CL 24278534
21:24:21 '
21:24:21 21:24:21 P4: found 24278823 revision in //jenkins-bct1-0f002b-codestream/...@24277823,24278823
21:24:21 ... p4 repos -C +
21:24:21 P4: builds: 24278823 
21:24:21 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:24:21 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:24:21 
21:24:21 P4 Task: establishing connection.
21:24:21 ... server: oh-p4edge-fb.eu.ad.ea.com:2001
21:24:21 ... node: bct1-0f002b
21:24:21 Building on Node: bct1-0f002b
21:24:21 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:24:21 ... p4 client -o jenkins-bct1-0f002b-codestream
 +
21:24:21 
21:24:21 P4 Task: establishing connection.
21:24:21 ... server: oh-p4edge-fb.eu.ad.ea.com:2001
21:24:21 ... node: bct1-0f002b
21:24:21 
21:24:21 P4 Task: reverting all pending and shelved revisions.
21:24:21 ... p4 revert D:\dev/...
 +
21:24:22 ... rm [abandoned files]
21:24:22 duration: (22ms)
21:24:22 
21:24:22 P4 Task: syncing files at change: 24278823
21:24:22 ... p4 sync --parallel=threads=8 -q D:\dev/...@24278823
 +
21:24:37 21:24:37 duration: 0m 15s
21:24:37 
21:24:37 P4: saving built changes.
21:24:37 WARNING: duplicate syncID found: jenkins-NODE_NAME-codestream
21:24:37 Found last change 24278823 on syncID jenkins-NODE_NAME-codestream
21:24:37 Found last change 24278823 on syncID jenkins-NODE_NAME-codestream
21:24:37 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:38 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:38 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:38 ... p4 client -o jenkins-bct1-0f002b-codestream +
21:24:38 ... done
21:24:38 
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 Run condition [Files match] enabling prebuild for step [[Archive the artifacts]]
21:24:38 New run name is 'hvu-utility.build-deleter.Bct-criterion.10'
21:24:38 Retrieving secret: secrets/kv/artifacts/automation/dre-pypi-federated/ro

21:24:38 [dev] $ cmd /c call C:\Users\<USER>\AppData\Local\Temp\jenkins12938710141956630648.bat
21:24:39 
21:24:39 D:\dev>tnt\bin\fbcli\cli.bat x64   && C:\dev\ci\install-elipy.bat elipy_bct.yml "10.2a1.dev13960" "17.3a1.dev8953"  1>>D:\dev\logs\install-elipy.log 2>&1 
21:24:39 Executing cli.bat
21:24:39 Executing fbenvstartup

21:24:41 
21:24:41 -- Locating TnT folder
21:24:41 FbEnvConfigService is not currently running, checking for deployed version
21:24:42 Launched fbenvconfigservice process at C:\Users\<USER>\AppData\Local\FbEnvConfigService\win-x64\fbenvconfigservice.exe

21:24:44 Created FbEnvConfigService Session with id: 1f58bdca-d3ef-4e87-ba22-c4f3d4576b26
21:24:44 Found TNT_ROOT at D:\dev\TnT
21:24:44 
21:24:44 -- Setting environment.
21:24:44 Run: fbenv initialization on D:\dev

21:24:46 No extra environment found in fbcli_init.bat files
21:24:46 Extra environment read from (took 0.2241 s):
21:24:46 	D:\dev\TnT\Code\DICE\BattlefieldGame\fbcli\fbcli_init.bat
21:24:46 	D:\dev\TnT\bin\fbcli\fbcli_init.bat
21:24:46 

21:24:47 Skipping clink init for automation build.

21:25:15 [dev] $ cmd /c call C:\Users\<USER>\AppData\Local\Temp\jenkins15899571314816459798.bat
21:25:15 
21:25:15 D:\dev>tnt\bin\fbcli\cli.bat x64   && C:\dev\ci\setup-elipy-env.bat elipy_bct.yml   1>>D:\dev\logs\setup-elipy-env.log 2>&1  && elipy --location Guildford deleter --include-path-retention  --dry-run 
21:25:15 Executing cli.bat
21:25:15 Executing fbenvstartup

21:25:16 
21:25:16 -- Locating TnT folder
21:25:16 Created FbEnvConfigService Session with id: c5391791-a9c3-4ce7-aa58-3f4902b96d07
21:25:16 Found TNT_ROOT at D:\dev\TnT

21:25:16 
21:25:16 -- Setting environment.
21:25:16 Run: fbenv initialization on D:\dev

21:25:18 No extra environment found in fbcli_init.bat files
21:25:18 Extra environment read from (took 0.2125 s):
21:25:18 	D:\dev\TnT\Code\DICE\BattlefieldGame\fbcli\fbcli_init.bat
21:25:18 	D:\dev\TnT\bin\fbcli\fbcli_init.bat
21:25:18 

21:25:19 Skipping clink init for automation build.

21:25:22 2025-07-03 20:25:22 elipy2 [WARNING]: Got python\virtual\Lib\site-packages\dice_elipy_scripts\yml\elipy_bct.yml for ELIPY_CONFIG, relative paths must be from %GAME_ROOT%.
21:25:22 2025-07-03 20:25:22 elipy2 [INFO]: Found ELIPY config file at D:\dev\python\virtual\Lib\site-packages\dice_elipy_scripts\yml\elipy_bct.yml 
21:25:22 2025-07-03 20:25:22 elipy2 [INFO]: Initializing ELIPY2 using config file: D:\dev\python\virtual\Lib\site-packages\dice_elipy_scripts\yml\elipy_bct.yml
21:25:22 2025-07-03 20:25:22 elipy2 [INFO]: Elipy was installed from AF2.
21:25:22 2025-07-03 20:25:22 elipy2 [INFO]: elipy2 version: 17.3a1.dev8953
21:25:22 2025-07-03 20:25:22 elipy2 [INFO]: dice_elipy_scripts version: 10.2a1.dev13960
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: performaing a --dry-run? True
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 13 builds at \\eauk-file.eu.ad.ea.com\Glacier\Shift\auto_submissions.
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Would delete \\eauk-file.eu.ad.ea.com\Glacier\Shift\auto_submissions\747e84be-7b7c-4c7b-b822-8a634f372089 [Created: 2025-07-03 20:03:17.087043]
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Would delete \\eauk-file.eu.ad.ea.com\Glacier\Shift\auto_submissions\634bef35-f187-423b-82ae-e16897903066 [Created: 2025-07-03 20:04:46.709122]
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Would delete \\eauk-file.eu.ad.ea.com\Glacier\Shift\auto_submissions\fe92512f-8039-4acf-b0a2-5d6710cd9d54 [Created: 2025-07-03 20:05:23.206039]

21:25:23 2025-07-03 20:25:23 elipy2 [WARNING]: Directory does not exist, skipping: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\offsite_basic\trunk-code-dev
21:25:23 2025-07-03 20:25:23 elipy2 [WARNING]: Directory does not exist, skipping: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\offsite_basic_drone\trunk-code-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Selected categories are: {'code': [{'default': 10}, {'trunk-content-dev-test': 0}, {'trunk-content-dev': 5}, {'criterion-content-warm': 5}], 'tnt_local': [{'default': 0}], 'frosty\\BattlefieldGame': [{'default': 8}, {'CH1-event-release': 12}, {'CH1-release': 35}, {'CH1-SP-release': 10}, {'CH1-bflabs-release': 10}, {'CH1-qol': 10}, {'CH1-event': 10}, {'CH1-bflabs-qol': 10}]}
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Disk-based discovery found 15 branches: ['CH1-SP-content-dev', 'CH1-SP-stage', 'CH1-code-dev', 'CH1-content-dev', 'CH1-stage', 'ch1-bflabs-qol', 'ch1-bflabs-release', 'ch1-event', 'ch1-event-release', 'ch1-qol', 'ch1-release', 'ch1-sp-release', 'criterion-content-warm', 'task1', 'trunk-content-dev']
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: CH1-event debug: path=\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code, branch=ch1-event-release, maxamount=10
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: CH1-event debug: path=\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code, branch=ch1-event, maxamount=10
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 5 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 5 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 10 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Bilbo version 2
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 10 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 13 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 13 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 3 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 3 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 4 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 7 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 4 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 10 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 7 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 4 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 10 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 7 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 4 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 13 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 7 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 14 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 10 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 13 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 10 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 14 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 10 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 13 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 10 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 13 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 93 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 93 builds on disk
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Found 148 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event
21:25:23 2025-07-03 20:25:23 elipy2 [INFO]: Processing orphaned builds for 148 builds on disk

21:25:27 2025-07-03 20:25:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\22893066
21:25:27 2025-07-03 20:25:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23792556

21:25:27 2025-07-03 20:25:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage\22818118

21:25:29 2025-07-03 20:25:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23280034
21:25:29 2025-07-03 20:25:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24121081
21:25:29 2025-07-03 20:25:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\23709176

21:25:30 2025-07-03 20:25:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23930681
21:25:30 2025-07-03 20:25:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23841282
21:25:30 2025-07-03 20:25:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\23703772

21:25:31 2025-07-03 20:25:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\23681229
21:25:31 2025-07-03 20:25:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24113348

21:25:32 2025-07-03 20:25:32 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\23742094
21:25:32 2025-07-03 20:25:32 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\23982232
21:25:32 2025-07-03 20:25:32 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24191213

21:25:33 2025-07-03 20:25:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24244697

21:25:35 2025-07-03 20:25:35 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23082491

21:25:36 2025-07-03 20:25:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23793327
21:25:36 2025-07-03 20:25:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage\24268682
21:25:36 2025-07-03 20:25:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23933476

21:25:37 2025-07-03 20:25:37 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24253749
21:25:38 2025-07-03 20:25:37 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\24099626
21:25:38 2025-07-03 20:25:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23418706
21:25:38 2025-07-03 20:25:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23845011

21:25:38 2025-07-03 20:25:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24221294
21:25:39 2025-07-03 20:25:39 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24190217
21:25:39 2025-07-03 20:25:39 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\24099626

21:25:40 2025-07-03 20:25:40 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\23992284

21:25:41 2025-07-03 20:25:41 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24249467

21:25:42 2025-07-03 20:25:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24248385
21:25:42 2025-07-03 20:25:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24191334
21:25:42 2025-07-03 20:25:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23099353

21:25:43 2025-07-03 20:25:43 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23946317
21:25:43 2025-07-03 20:25:43 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23793503

21:25:45 2025-07-03 20:25:45 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24254058
21:25:45 2025-07-03 20:25:45 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage\24286607
21:25:45 2025-07-03 20:25:45 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23849655
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: Orphaned builds processing complete: 3 orphaned, 0 kept
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: Deleting 3 builds (0 regular, 3 orphan)
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage\22818118
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage\24268682
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-stage\24286607
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:25:45 2025-07-03 20:25:45 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:25:46 2025-07-03 20:25:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23447457
21:25:46 2025-07-03 20:25:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\24099719
21:25:47 2025-07-03 20:25:47 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24191206
21:25:47 2025-07-03 20:25:47 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\24099719

21:25:48 2025-07-03 20:25:48 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24221740

21:25:48 2025-07-03 20:25:48 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24288537
21:25:49 2025-07-03 20:25:49 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24251102

21:25:49 2025-07-03 20:25:49 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24249692
21:25:50 2025-07-03 20:25:50 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23954382

21:25:51 2025-07-03 20:25:51 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23240963
21:25:51 2025-07-03 20:25:51 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24197099

21:25:52 2025-07-03 20:25:52 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24302739 is an orphan but is newer than 2 days, keeping.
21:25:52 2025-07-03 20:25:52 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23798233

21:25:53 2025-07-03 20:25:53 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23470375

21:25:54 2025-07-03 20:25:54 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23855358
21:25:54 2025-07-03 20:25:54 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\24240140
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: Orphaned builds processing complete: 4 orphaned, 0 kept
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: Deleting 4 builds (0 regular, 4 orphan)
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\23742094
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\24099626
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\24099719
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-qol\24240140
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:25:54 2025-07-03 20:25:54 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:25:55 2025-07-03 20:25:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24197099
21:25:55 2025-07-03 20:25:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\24240140
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: Orphaned builds processing complete: 4 orphaned, 0 kept
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: Deleting 4 builds (0 regular, 4 orphan)
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\23709176
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\24099626
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\24099719
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-qol\24240140
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:25:55 2025-07-03 20:25:55 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:25:56 2025-07-03 20:25:56 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24286403
21:25:56 2025-07-03 20:25:56 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24296221 is an orphan but is newer than 2 days, keeping.
21:25:57 2025-07-03 20:25:57 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24250984
21:25:57 2025-07-03 20:25:57 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24304950 is an orphan but is newer than 2 days, keeping.

21:25:58 2025-07-03 20:25:58 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23962956

21:25:59 2025-07-03 20:25:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23549282
21:25:59 2025-07-03 20:25:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24212042
21:25:59 2025-07-03 20:25:59 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24303145 is an orphan but is newer than 2 days, keeping.

21:26:00 2025-07-03 20:26:00 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23253540
21:26:00 2025-07-03 20:26:00 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23798888

21:26:01 2025-07-03 20:26:01 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23858160

21:26:02 2025-07-03 20:26:02 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24288396 is an orphan but is newer than 2 days, keeping.
21:26:03 2025-07-03 20:26:03 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24251634

21:26:04 2025-07-03 20:26:04 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24296789 is an orphan but is newer than 2 days, keeping.
21:26:04 2025-07-03 20:26:04 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24212042

21:26:05 2025-07-03 20:26:05 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23565313
21:26:05 2025-07-03 20:26:05 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24305369 is an orphan but is newer than 2 days, keeping.
21:26:05 2025-07-03 20:26:05 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24017988

21:26:06 2025-07-03 20:26:06 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24304026 is an orphan but is newer than 2 days, keeping.
21:26:06 2025-07-03 20:26:06 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24226287
21:26:06 2025-07-03 20:26:06 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23391385

21:26:07 2025-07-03 20:26:07 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23800642

21:26:08 2025-07-03 20:26:08 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24252650
21:26:08 2025-07-03 20:26:08 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23861641
21:26:09 2025-07-03 20:26:09 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24289113 is an orphan but is newer than 2 days, keeping.

21:26:10 2025-07-03 20:26:10 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24226287

21:26:10 2025-07-03 20:26:10 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24305993 is an orphan but is newer than 2 days, keeping.
21:26:11 2025-07-03 20:26:11 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24297082 is an orphan but is newer than 2 days, keeping.
21:26:11 2025-07-03 20:26:11 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24305102 is an orphan but is newer than 2 days, keeping.
21:26:12 2025-07-03 20:26:12 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23593217

21:26:12 2025-07-03 20:26:12 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24023394

21:26:13 2025-07-03 20:26:13 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23801087
21:26:14 2025-07-03 20:26:14 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24281479

21:26:14 2025-07-03 20:26:14 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23417369
21:26:15 2025-07-03 20:26:15 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24306645 is an orphan but is newer than 2 days, keeping.
21:26:16 2025-07-03 20:26:16 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23863353

21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24289137 is an orphan but is newer than 2 days, keeping.
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24281479 is an orphan but is newer than 2 days, keeping.
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: Orphaned builds processing complete: 6 orphaned, 1 kept
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: Deleting 6 builds (0 regular, 6 orphan)
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\23703772
21:26:16 2025-07-03 20:26:16 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24253991
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24190217
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24191206
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24197099
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24212042
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/trunk-content-dev\24226287
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:16 2025-07-03 20:26:16 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:26:17 2025-07-03 20:26:17 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23680912
21:26:18 2025-07-03 20:26:18 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24308165 is an orphan but is newer than 2 days, keeping.

21:26:18 2025-07-03 20:26:18 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24069100
21:26:18 2025-07-03 20:26:18 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24305605 is an orphan but is newer than 2 days, keeping.

21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24309475 is an orphan but is newer than 2 days, keeping.
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: Orphaned builds processing complete: 6 orphaned, 1 kept
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: Nothing to delete! builds available 1 <= 5 maximum builds
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: Deleting 6 builds (0 regular, 6 orphan)
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24191213
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24191334
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24197099
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24212042
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24226287
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/criterion-content-warm\24281479
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:19 2025-07-03 20:26:19 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:20 2025-07-03 20:26:20 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23805141

21:26:21 2025-07-03 20:26:21 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24306828 is an orphan but is newer than 2 days, keeping.
21:26:21 2025-07-03 20:26:21 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23421549
21:26:21 2025-07-03 20:26:21 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24296189 is an orphan but is newer than 2 days, keeping.

21:26:22 2025-07-03 20:26:22 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23867588
21:26:23 2025-07-03 20:26:23 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24254234

21:26:23 2025-07-03 20:26:23 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24310188 is an orphan but is newer than 2 days, keeping.

21:26:24 2025-07-03 20:26:24 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24122272
21:26:25 2025-07-03 20:26:25 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24306381 is an orphan but is newer than 2 days, keeping.
21:26:25 2025-07-03 20:26:25 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23697084
21:26:25 2025-07-03 20:26:25 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23640650

21:26:26 2025-07-03 20:26:26 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23805439
21:26:26 2025-07-03 20:26:26 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24297127 is an orphan but is newer than 2 days, keeping.

21:26:27 2025-07-03 20:26:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24254799
21:26:27 2025-07-03 20:26:27 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24307872 is an orphan but is newer than 2 days, keeping.

21:26:28 2025-07-03 20:26:28 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23871337
21:26:29 2025-07-03 20:26:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23734386
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: Orphaned builds processing complete: 10 orphaned, 0 kept
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: Deleting 10 builds (0 regular, 10 orphan)
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23280034
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23418706
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23447457
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23470375
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23549282
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23565313
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23593217
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23680912
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23697084
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event-release\23734386
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:29 2025-07-03 20:26:29 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24310497 is an orphan but is newer than 2 days, keeping.

21:26:30 2025-07-03 20:26:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24142060
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Orphaned builds processing complete: 10 orphaned, 0 kept
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Deleting 10 builds (0 regular, 10 orphan)
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23930681
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23933476
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23946317
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23954382
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\23962956
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24017988
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24023394
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24069100
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24122272
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/task1\24142060
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:30 2025-07-03 20:26:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\24261835

21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Orphaned builds processing complete: 10 orphaned, 0 kept
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Deleting 10 builds (0 regular, 10 orphan)
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\22893066
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23082491
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23099353
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24307012 is an orphan but is newer than 2 days, keeping.
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23240963
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23253540
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23391385
21:26:30 2025-07-03 20:26:30 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23417369
21:26:30 2025-07-03 20:26:31 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23421549
21:26:30 2025-07-03 20:26:31 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\23640650
21:26:30 2025-07-03 20:26:31 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-sp-release\24261835
21:26:31 2025-07-03 20:26:31 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:31 2025-07-03 20:26:31 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:31 2025-07-03 20:26:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23808305
21:26:31 2025-07-03 20:26:31 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24297444 is an orphan but is newer than 2 days, keeping.
21:26:32 2025-07-03 20:26:32 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24254802
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: Orphaned builds processing complete: 10 orphaned, 0 kept
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: Deleting 10 builds (0 regular, 10 orphan)
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24244697
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24248385
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24249692
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24250984
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24251634
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24252650
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24253991
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24254234
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24254799
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-code-dev\24254802
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:32 2025-07-03 20:26:32 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:26:33 2025-07-03 20:26:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23873946

21:26:33 2025-07-03 20:26:33 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24308055 is an orphan but is newer than 2 days, keeping.
21:26:34 2025-07-03 20:26:34 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24309099 is an orphan but is newer than 2 days, keeping.

21:26:35 2025-07-03 20:26:35 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24312228 is an orphan but is newer than 2 days, keeping.
21:26:35 2025-07-03 20:26:35 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23811689

21:26:36 2025-07-03 20:26:36 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24310231 is an orphan but is newer than 2 days, keeping.
21:26:37 2025-07-03 20:26:37 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24312396 is an orphan but is newer than 2 days, keeping.

21:26:37 2025-07-03 20:26:37 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23876768
21:26:38 2025-07-03 20:26:38 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24309682 is an orphan but is newer than 2 days, keeping.
21:26:38 2025-07-03 20:26:38 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24312365 is an orphan but is newer than 2 days, keeping.

21:26:40 2025-07-03 20:26:40 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23812666
21:26:40 2025-07-03 20:26:40 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24310570 is an orphan but is newer than 2 days, keeping.
21:26:40 2025-07-03 20:26:40 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23880267

21:26:41 2025-07-03 20:26:41 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24313518 is an orphan but is newer than 2 days, keeping.
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24314017 is an orphan but is newer than 2 days, keeping.
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: Orphaned builds processing complete: 3 orphaned, 10 kept
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: Nothing to delete! builds available 10 <= 10 maximum builds
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: Deleting 3 builds (0 regular, 3 orphan)
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24121081
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24253749
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-SP-content-dev\24254058
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:42 2025-07-03 20:26:42 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24315154 is an orphan but is newer than 2 days, keeping.

21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24310823 is an orphan but is newer than 2 days, keeping.
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: Orphaned builds processing complete: 4 orphaned, 9 kept
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: Nothing to delete! builds available 9 <= 10 maximum builds
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: Deleting 4 builds (0 regular, 4 orphan)
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\23681229
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24249467
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24251102
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-stage\24286403
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:43 2025-07-03 20:26:43 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:44 2025-07-03 20:26:44 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23895096
21:26:44 2025-07-03 20:26:44 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23813307

21:26:45 2025-07-03 20:26:45 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24313917 is an orphan but is newer than 2 days, keeping.

21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24315818 is an orphan but is newer than 2 days, keeping.
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: Orphaned builds processing complete: 3 orphaned, 10 kept
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: Nothing to delete! builds available 10 <= 10 maximum builds
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: Deleting 3 builds (0 regular, 3 orphan)
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\23982232
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\23992284
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-release\24288537
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:46 2025-07-03 20:26:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23895977
21:26:46 2025-07-03 20:26:46 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:26:47 2025-07-03 20:26:47 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23833874
21:26:48 2025-07-03 20:26:48 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24314423 is an orphan but is newer than 2 days, keeping.
21:26:48 2025-07-03 20:26:48 elipy2 [INFO]: Orphaned builds processing complete: 3 orphaned, 11 kept

21:26:49 2025-07-03 20:26:49 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23899742
21:26:49 2025-07-03 20:26:49 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23848413

21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: Deleting 4 builds (1 regular, 3 orphan)
21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24304950
21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24113348
21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24221294
21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/CH1-content-dev\24221740
21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:26:50 2025-07-03 20:26:50 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:26:51 2025-07-03 20:26:51 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23901631
21:26:51 2025-07-03 20:26:51 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23850123

21:26:53 2025-07-03 20:26:53 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23903911
21:26:53 2025-07-03 20:26:53 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23854160

21:26:55 2025-07-03 20:26:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23906138
21:26:55 2025-07-03 20:26:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23854927

21:26:57 2025-07-03 20:26:57 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23907338
21:26:57 2025-07-03 20:26:57 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23855543

21:26:59 2025-07-03 20:26:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23912351
21:26:59 2025-07-03 20:26:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23856305

21:27:02 2025-07-03 20:27:02 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23914208
21:27:02 2025-07-03 20:27:02 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23856701

21:27:03 2025-07-03 20:27:03 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23857271
21:27:04 2025-07-03 20:27:04 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23916030

21:27:05 2025-07-03 20:27:05 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23867196
21:27:06 2025-07-03 20:27:06 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23919035

21:27:07 2025-07-03 20:27:07 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23867812
21:27:07 2025-07-03 20:27:07 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23919730

21:27:09 2025-07-03 20:27:09 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23868592
21:27:09 2025-07-03 20:27:09 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23920798

21:27:11 2025-07-03 20:27:11 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23870843

21:27:11 2025-07-03 20:27:11 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23923300

21:27:13 2025-07-03 20:27:13 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23872075

21:27:13 2025-07-03 20:27:13 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23926965

21:27:15 2025-07-03 20:27:15 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23872312

21:27:16 2025-07-03 20:27:16 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23929585

21:27:17 2025-07-03 20:27:17 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23872712

21:27:18 2025-07-03 20:27:18 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23933119

21:27:19 2025-07-03 20:27:19 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23880092

21:27:20 2025-07-03 20:27:20 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23937576
21:27:20 2025-07-03 20:27:20 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23882059

21:27:22 2025-07-03 20:27:22 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23941963
21:27:23 2025-07-03 20:27:23 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23883264

21:27:24 2025-07-03 20:27:24 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23944844
21:27:25 2025-07-03 20:27:25 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23883509

21:27:26 2025-07-03 20:27:26 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23886299
21:27:26 2025-07-03 20:27:26 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23948536

21:27:28 2025-07-03 20:27:28 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23953366

21:27:29 2025-07-03 20:27:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23886813

21:27:30 2025-07-03 20:27:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23957420
21:27:31 2025-07-03 20:27:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23887023

21:27:33 2025-07-03 20:27:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23887946

21:27:33 2025-07-03 20:27:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23960294

21:27:36 2025-07-03 20:27:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23892805

21:27:36 2025-07-03 20:27:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23963844

21:27:38 2025-07-03 20:27:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23964726
21:27:38 2025-07-03 20:27:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23894842

21:27:41 2025-07-03 20:27:41 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23968362

21:27:42 2025-07-03 20:27:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23895115

21:27:43 2025-07-03 20:27:43 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23972424
21:27:43 2025-07-03 20:27:43 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23897588

21:27:46 2025-07-03 20:27:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23976200
21:27:46 2025-07-03 20:27:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23899071

21:27:47 2025-07-03 20:27:47 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23979140

21:27:48 2025-07-03 20:27:48 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23899187

21:27:50 2025-07-03 20:27:50 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23982685

21:27:50 2025-07-03 20:27:50 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23899590

21:27:52 2025-07-03 20:27:52 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23991736

21:27:52 2025-07-03 20:27:52 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23900067

21:27:53 2025-07-03 20:27:53 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23993636
21:27:54 2025-07-03 20:27:54 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23900327

21:27:55 2025-07-03 20:27:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23993765

21:27:56 2025-07-03 20:27:56 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23900531

21:27:57 2025-07-03 20:27:57 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24005309

21:27:58 2025-07-03 20:27:58 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23901028

21:27:59 2025-07-03 20:27:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24015379

21:28:00 2025-07-03 20:28:00 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23917114

21:28:01 2025-07-03 20:28:01 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24019374

21:28:02 2025-07-03 20:28:02 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23924157

21:28:03 2025-07-03 20:28:03 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24024638
21:28:04 2025-07-03 20:28:04 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23928123

21:28:05 2025-07-03 20:28:05 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24034533

21:28:06 2025-07-03 20:28:06 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23928394

21:28:08 2025-07-03 20:28:08 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24050830

21:28:08 2025-07-03 20:28:08 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23939837

21:28:09 2025-07-03 20:28:09 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24052571

21:28:11 2025-07-03 20:28:11 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23940134
21:28:12 2025-07-03 20:28:12 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24054386

21:28:13 2025-07-03 20:28:13 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23943348

21:28:14 2025-07-03 20:28:14 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24056745
21:28:15 2025-07-03 20:28:15 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23944899

21:28:16 2025-07-03 20:28:16 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24058250

21:28:17 2025-07-03 20:28:17 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23950557

21:28:18 2025-07-03 20:28:18 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24060510

21:28:19 2025-07-03 20:28:19 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23953912

21:28:20 2025-07-03 20:28:20 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24061462

21:28:22 2025-07-03 20:28:22 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23958242

21:28:22 2025-07-03 20:28:22 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24062605

21:28:24 2025-07-03 20:28:24 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23959188
21:28:24 2025-07-03 20:28:24 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24064989

21:28:26 2025-07-03 20:28:26 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23963941

21:28:27 2025-07-03 20:28:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24067162
21:28:27 2025-07-03 20:28:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23965475

21:28:29 2025-07-03 20:28:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24069289
21:28:30 2025-07-03 20:28:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23965602

21:28:31 2025-07-03 20:28:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24071104

21:28:32 2025-07-03 20:28:32 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23965642

21:28:33 2025-07-03 20:28:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24073398

21:28:34 2025-07-03 20:28:34 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23968820

21:28:35 2025-07-03 20:28:35 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24075130
21:28:36 2025-07-03 20:28:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23971755

21:28:36 2025-07-03 20:28:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24076104

21:28:38 2025-07-03 20:28:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23978533
21:28:38 2025-07-03 20:28:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24077091

21:28:39 2025-07-03 20:28:39 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23982232
21:28:40 2025-07-03 20:28:40 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24078404

21:28:41 2025-07-03 20:28:41 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23992284

21:28:42 2025-07-03 20:28:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24079048

21:28:43 2025-07-03 20:28:43 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23992479
21:28:44 2025-07-03 20:28:44 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24081324

21:28:45 2025-07-03 20:28:45 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24124200
21:28:46 2025-07-03 20:28:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24089174

21:28:46 2025-07-03 20:28:46 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24124631

21:28:47 2025-07-03 20:28:47 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24094398
21:28:48 2025-07-03 20:28:48 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24143830

21:28:49 2025-07-03 20:28:49 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24096044
21:28:50 2025-07-03 20:28:50 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24248453

21:28:51 2025-07-03 20:28:51 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24098532

21:28:52 2025-07-03 20:28:52 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24254078

21:28:53 2025-07-03 20:28:53 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24100192
21:28:53 2025-07-03 20:28:53 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24270490

21:28:55 2025-07-03 20:28:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24101992
21:28:56 2025-07-03 20:28:56 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24283046

21:28:57 2025-07-03 20:28:57 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24103633

21:28:58 2025-07-03 20:28:58 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24284339

21:28:59 2025-07-03 20:28:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24107059

21:29:00 2025-07-03 20:29:00 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24284535
21:29:01 2025-07-03 20:29:01 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24109276

21:29:02 2025-07-03 20:29:02 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24285494

21:29:02 2025-07-03 20:29:02 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24111639

21:29:03 2025-07-03 20:29:03 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24286233
21:29:04 2025-07-03 20:29:04 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24113274

21:29:05 2025-07-03 20:29:05 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24288006

21:29:06 2025-07-03 20:29:06 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24115658

21:29:08 2025-07-03 20:29:08 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24288112

21:29:08 2025-07-03 20:29:08 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24116136
21:29:09 2025-07-03 20:29:09 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24288537 is an orphan but is newer than 2 days, keeping.

21:29:10 2025-07-03 20:29:10 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24120073

21:29:11 2025-07-03 20:29:11 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24296221 is an orphan but is newer than 2 days, keeping.

21:29:12 2025-07-03 20:29:12 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24121495

21:29:13 2025-07-03 20:29:13 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24297082 is an orphan but is newer than 2 days, keeping.
21:29:14 2025-07-03 20:29:14 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24124142

21:29:15 2025-07-03 20:29:15 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24308165 is an orphan but is newer than 2 days, keeping.

21:29:15 2025-07-03 20:29:15 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24126599

21:29:17 2025-07-03 20:29:17 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24310188 is an orphan but is newer than 2 days, keeping.

21:29:17 2025-07-03 20:29:17 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24128825

21:29:19 2025-07-03 20:29:19 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24310497 is an orphan but is newer than 2 days, keeping.
21:29:19 2025-07-03 20:29:19 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24132278

21:29:21 2025-07-03 20:29:21 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24312197 is an orphan but is newer than 2 days, keeping.
21:29:21 2025-07-03 20:29:21 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24134544

21:29:23 2025-07-03 20:29:23 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24312365 is an orphan but is newer than 2 days, keeping.
21:29:23 2025-07-03 20:29:23 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24137142

21:29:25 2025-07-03 20:29:25 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24138867
21:29:25 2025-07-03 20:29:25 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24315154 is an orphan but is newer than 2 days, keeping.

21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24315293 is an orphan but is newer than 2 days, keeping.
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: Orphaned builds processing complete: 83 orphaned, 10 kept
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: Nothing to delete! builds available 10 <= 10 maximum builds
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: Deleting 83 builds (0 regular, 83 orphan)
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23792556
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23793327
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23793503
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23798233
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23798888
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23800642
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23801087
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23805141
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23805439
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23808305
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23811689
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23812666
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23813307
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23833874
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23848413
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23850123
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23854160
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23854927
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23855543
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23856305
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23856701
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23857271
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23867196
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23867812
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23868592
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23870843
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23872075
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23872312
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23872712
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23880092
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23882059
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23883264
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23883509
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23886299
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23886813
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23887023
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23887946
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23892805
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23894842
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23895115
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23897588
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23899071
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23899187
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23899590
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23900067
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23900327
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23900531
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23901028
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23917114
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23924157
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23928123
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23928394
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23939837
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23940134
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23943348
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23944899
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23950557
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23953912
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23958242
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23959188
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23963941
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23965475
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23965602
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23965642
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23968820
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23971755
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23978533
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23982232
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23992284
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\23992479
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24124200
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24124631
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24143830
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24248453
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24254078
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24270490
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24283046
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24284339
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24284535
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24285494
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24286233
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24288006
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-bflabs-release\24288112
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:29:27 2025-07-03 20:29:27 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:29:27 2025-07-03 20:29:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24141451

21:29:29 2025-07-03 20:29:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24143697

21:29:31 2025-07-03 20:29:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24144858

21:29:33 2025-07-03 20:29:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24146708

21:29:34 2025-07-03 20:29:34 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24148326

21:29:36 2025-07-03 20:29:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24151273

21:29:37 2025-07-03 20:29:37 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24154111

21:29:39 2025-07-03 20:29:39 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24155416

21:29:40 2025-07-03 20:29:40 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24156034

21:29:42 2025-07-03 20:29:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24159079

21:29:43 2025-07-03 20:29:43 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24160937

21:29:45 2025-07-03 20:29:45 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24170729

21:29:47 2025-07-03 20:29:47 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24177326

21:29:48 2025-07-03 20:29:48 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24179193

21:29:50 2025-07-03 20:29:50 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24180857

21:29:51 2025-07-03 20:29:51 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24182199

21:29:52 2025-07-03 20:29:52 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24184521

21:29:54 2025-07-03 20:29:54 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24187936

21:29:55 2025-07-03 20:29:55 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24192260

21:29:57 2025-07-03 20:29:57 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24194466

21:29:58 2025-07-03 20:29:58 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24197194

21:29:59 2025-07-03 20:29:59 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24198366

21:30:01 2025-07-03 20:30:01 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24207150

21:30:03 2025-07-03 20:30:03 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24207808

21:30:04 2025-07-03 20:30:04 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24212698

21:30:06 2025-07-03 20:30:06 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24214776

21:30:07 2025-07-03 20:30:07 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24216947

21:30:09 2025-07-03 20:30:09 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24217333

21:30:10 2025-07-03 20:30:10 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24221370

21:30:12 2025-07-03 20:30:12 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24223891

21:30:14 2025-07-03 20:30:14 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24226186

21:30:15 2025-07-03 20:30:15 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24227648

21:30:16 2025-07-03 20:30:16 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24230552

21:30:18 2025-07-03 20:30:18 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24235171

21:30:20 2025-07-03 20:30:20 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24236375

21:30:22 2025-07-03 20:30:22 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24237548

21:30:24 2025-07-03 20:30:24 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24239286

21:30:25 2025-07-03 20:30:25 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24240192

21:30:27 2025-07-03 20:30:27 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24242555

21:30:29 2025-07-03 20:30:29 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24245167

21:30:30 2025-07-03 20:30:30 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24247189

21:30:31 2025-07-03 20:30:31 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24248219

21:30:33 2025-07-03 20:30:33 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24251384

21:30:35 2025-07-03 20:30:35 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24252467

21:30:36 2025-07-03 20:30:36 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24254078

21:30:38 2025-07-03 20:30:38 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24269306

21:30:39 2025-07-03 20:30:39 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24283046

21:30:40 2025-07-03 20:30:40 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24286233

21:30:42 2025-07-03 20:30:42 elipy2 [WARNING]: Found an orphaned build: \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24288006
21:30:43 2025-07-03 20:30:43 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24288537 is an orphan but is newer than 2 days, keeping.

21:30:45 2025-07-03 20:30:45 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24296221 is an orphan but is newer than 2 days, keeping.

21:30:46 2025-07-03 20:30:46 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24297082 is an orphan but is newer than 2 days, keeping.

21:30:47 2025-07-03 20:30:47 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24308165 is an orphan but is newer than 2 days, keeping.

21:30:48 2025-07-03 20:30:48 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24310188 is an orphan but is newer than 2 days, keeping.

21:30:49 2025-07-03 20:30:49 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24310479 is an orphan but is newer than 2 days, keeping.

21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: Build \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24312365 is an orphan but is newer than 2 days, keeping.
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: Orphaned builds processing complete: 141 orphaned, 7 kept
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: Nothing to delete! builds available 7 <= 10 maximum builds
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: Deleting 141 builds (0 regular, 141 orphan)
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23841282
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23845011
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23849655
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23855358
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23858160
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23861641
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23863353
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23867588
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23871337
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23873946
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23876768
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23880267
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23895096
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23895977
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23899742
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23901631
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23903911
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23906138
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23907338
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23912351
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23914208
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23916030
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23919035
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23919730
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23920798
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23923300
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23926965
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23929585
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23933119
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23937576
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23941963
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23944844
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23948536
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23953366
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23957420
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23960294
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23963844
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23964726
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23968362
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23972424
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23976200
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23979140
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23982685
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23991736
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23993636
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\23993765
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24005309
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24015379
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24019374
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24024638
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24034533
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24050830
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24052571
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24054386
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24056745
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24058250
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24060510
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24061462
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24062605
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24064989
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24067162
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24069289
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24071104
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24073398
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24075130
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24076104
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24077091
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24078404
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24079048
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24081324
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24089174
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24094398
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24096044
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24098532
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24100192
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24101992
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24103633
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24107059
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24109276
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24111639
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24113274
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24115658
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24116136
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24120073
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24121495
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24124142
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24126599
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24128825
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24132278
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24134544
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24137142
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24138867
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24141451
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24143697
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24144858
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24146708
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24148326
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24151273
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24154111
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24155416
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24156034
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24159079
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24160937
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24170729
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24177326
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24179193
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24180857
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24182199
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24184521
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24187936
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24192260
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24194466
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24197194
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24198366
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24207150
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24207808
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24212698
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24214776
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24216947
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24217333
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24221370
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24223891
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24226186
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24227648
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24230552
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24235171
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24236375
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24237548
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24239286
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24240192
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24242555
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24245167
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24247189
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24248219
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24251384
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24252467
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24254078
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24269306
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24283046
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24286233
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: about to delete: file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/code/ch1-event\24288006
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:51 2025-07-03 20:30:51 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Disk-based discovery found 7 branches: ['CH1-SP-release', 'CH1-bflabs-qol', 'CH1-bflabs-release', 'CH1-event', 'CH1-event-release', 'CH1-qol', 'CH1-release']
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-bflabs-qol
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-SP-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 12 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-event-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: CH1-event debug: path=\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame, branch=CH1-event-release, maxamount=12
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 35 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-event
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: CH1-event debug: path=\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame, branch=CH1-event, maxamount=10
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-bflabs-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-qol
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index bilbo_v2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Bilbo version 2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-qol
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-event-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 12 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-bflabs-qol
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-SP-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-bflabs-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-release
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 35 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2

21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Found 0 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-event
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Processing orphaned builds for 0 builds on disk
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Orphaned builds processing complete: 0 orphaned, 0 kept
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Nothing to delete! builds available 0 <= 10 maximum builds
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Deleting 0 builds (0 regular, 0 orphan)
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: Skipping azure_fileshare_path_retention files
21:30:52 2025-07-03 20:30:52 elipy2 [WARNING]: Skipping Avalanche db deletion.
21:30:52 2025-07-03 20:30:52 elipy2 [WARNING]: Skipping symstore cleanup.
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:52 2025-07-03 20:30:52 elipy2 [INFO]: FB version read from D:\dev\TnT\bin\FrostbiteRelease.txt is 2025-1-PR2
21:30:53 New run name is 'hvu-utility.build-deleter.Bct-criterion.10'
21:30:53 Files match condition: Matched [3] files
21:30:53 Run condition [Files match] enabling perform for step [[Archive the artifacts]]
21:30:53 Archiving artifacts
21:30:53 Files match condition: Matched [0] files
21:30:53 Run condition [Files match] preventing perform for step [[Archive the artifacts]]
21:30:53 Files match condition: Matched [0] files
21:30:53 Run condition [Files match] preventing perform for step [[Archive the artifacts]]
21:30:53 Files match condition: Matched [0] files
21:30:53 Run condition [Files match] preventing perform for step [[Archive the artifacts]]
21:30:53 Files match condition: Matched [0] files
21:30:53 Run condition [Files match] preventing perform for step [[Archive the artifacts]]
21:30:53 Files match condition: Matched [0] files
21:30:53 Run condition [Files match] preventing perform for step [[Archive the artifacts]]
21:30:53 Files match condition: Matched [0] files
21:30:53 Run condition [Files match] preventing perform for step [[Archive the artifacts]]

21:30:53 Finished: SUCCESS
