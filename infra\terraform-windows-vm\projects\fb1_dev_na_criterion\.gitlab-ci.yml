#**********************************************
#               fb1_dev_na_criterion PIPE                 *
#**********************************************
.default-fb1-dev-na-criterion-variables:
  extends: .secrets-fb1_dev_na_criterion
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "fb1_dev_na_criterion"
    WORKING_DIR: "projects/fb1_dev_na_criterion"
    VC_HOST: oh-vcenter1.ad.ea.com
    NODE_INFO_FILE: "node-info-fb1_dev_na_criterion.json"
    ansible_main_module: fb_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1_dev_na_criterion.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-fb1-dev-na-criterion:
  extends: ['.default-fb1-dev-na-criterion-variables', '.prepare_config']

validate-fb1-dev-na-criterion:
  extends: ['.default-fb1-dev-na-criterion-variables', '.validation_steps']

plan-fb1-dev-na-criterion:
  needs:
    - job: validate-fb1-dev-na-criterion
    - job: prepare-json-config-fb1-dev-na-criterion
  extends: ['.default-fb1-dev-na-criterion-variables','.plan_steps']

apply-fb1-dev-na-criterion:
  needs:
    - job: plan-fb1-dev-na-criterion
    - job: prepare-json-config-fb1-dev-na-criterion
  extends: ['.default-fb1-dev-na-criterion-variables','.apply_steps']

attache-fb1-dev-na-criterion:
  needs:
    - job: apply-fb1-dev-na-criterion
    - job: prepare-json-config-fb1-dev-na-criterion
  extends: ['.default-fb1-dev-na-criterion-variables','.attache_vmdk_step']

sync-fb1-dev-na-criterion:
  needs:
    - job: apply-fb1-dev-na-criterion
    - job: attache-fb1-dev-na-criterion
    - job: prepare-json-config-fb1-dev-na-criterion
  extends: ['.default-fb1-dev-na-criterion-variables','.sync_vmdk_step']

ansible-fb1-dev-na-criterion:
  needs:
    - job: apply-fb1-dev-na-criterion
    - job: sync-fb1-dev-na-criterion
    - job: prepare-json-config-fb1-dev-na-criterion
  extends: ['.default-fb1-dev-na-criterion-variables', '.ansible_common_secrets', '.run_ansible_step']
