#**********************************************
#                  bct_ps PIPE
#**********************************************
.default-bct-ps-variables:
  extends: .secrets-bct_ps
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ps"
    WORKING_DIR: "projects/bct_ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_PS.json"

prepare-json-config-bct-ps:
  extends: ['.default-bct-ps-variables', '.prepare_config']

validate-bct-ps:
  extends: ['.default-bct-ps-variables', '.validation_steps']

plan-bct-ps:
  needs:
    - job: validate-bct-ps
    - job: prepare-json-config-bct-ps
  extends: ['.default-bct-ps-variables', '.plan_steps']

apply-bct-ps:
  needs:
    - job: plan-bct-ps
    - job: prepare-json-config-bct-ps
  extends: ['.default-bct-ps-variables', '.apply_steps']

attache-bct-ps:
  needs:
    - job: apply-bct-ps
    - job: prepare-json-config-bct-ps
  extends: ['.default-bct-ps-variables', '.attache_vmdk_step']

sync-bct-ps:
  needs:
    - job: apply-bct-ps
    - job: attache-bct-ps
    - job: prepare-json-config-bct-ps
  extends: ['.default-bct-ps-variables', '.sync_vmdk_step']

ansible-bct-ps:
  needs:
    - job: apply-bct-ps
    - job: sync-bct-ps
    - job: prepare-json-config-bct-ps
  extends: ['.default-bct-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
