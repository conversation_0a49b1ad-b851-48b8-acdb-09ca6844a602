#**********************************************
#               TERRA RUNNERS PIPE
#**********************************************
.default-criterion-terra-runners-variables:
  extends: .secrets-criterion_terra_runners
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "criterion_terra_runners"
    WORKING_DIR: "projects/gitlab_runners/criterion_terra_runners"
    NODE_INFO_FILE: "node-info-criterion-terra-runners.json"
    VC_HOST: oh-vcenter1.ad.ea.com
    VAULT_RATE_LIMIT: 50
    VAULT_ADDR: "https://ess.ea.com"
    VAULT_NAMESPACE: "cds-dre-prod"
    VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
    VAULT_AUTH_LOGIN_PATH: "auth/jwt/gitlab/login"
    SECRET_TOP: "secrets/kv/cobra/automation/gitlab"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log
    ansible_main_module: terra_runner_silverback
    SILVERBACK_CONFIG_JSON_FILE: "terra_runners.json"

prepare-json-config-criterion-terra-runners:
  extends: ['.default-criterion-terra-runners-variables', '.prepare_config']

validate-criterion-terra-runners:
  extends: ['.default-criterion-terra-runners-variables', '.validation_steps']

plan-criterion-terra-runners:
  extends: ['.default-criterion-terra-runners-variables', '.plan_steps_runner']

apply-criterion-terra-runners:
  needs:
    - job: plan-criterion-terra-runners
  extends: ['.default-criterion-terra-runners-variables', '.apply_steps']

ansible-criterion-terra-runners:
  needs:
    - job: apply-criterion-terra-runners
    - job: prepare-json-config-criterion-terra-runners
  extends: ['.default-criterion-terra-runners-variables', '.ansible_common_secrets', '.run_ansible_step']
