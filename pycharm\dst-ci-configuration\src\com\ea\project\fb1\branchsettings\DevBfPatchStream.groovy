package com.ea.project.fb1.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.fb1.Fb1Battlefieldgame

class DevBfPatchStream {
    // Settings for jobs
    static Class project = Fb1Battlefieldgame
    static Map general_settings = [
        dataset                                : 'bfdata',
        elipy_install_call                     : project.elipy_install_call,
        elipy_call                             : "${project.elipy_call_eala} --use-fbenv-core",
        frostbite_licensee                     : 'BattlefieldGame',
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : "${project.azure_elipy_call_eala} --use-fbenv-core",
        azure_elipy_install_call               : project.azure_elipy_install_call,
        azure_workspace_root                   : project.azure_workspace_root,
        p4_code_creds                          : 'fb1-la-p4',
        p4_data_creds                          : 'fb1-la-p4',
        p4_code_server                         : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_data_server                         : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_data_client                         : 'jenkins-${NODE_NAME}-bfdatastream',
        p4_data_client_env                     : 'jenkins-%NODE_NAME%-bfdatastream',
        job_label_statebuild                   : 'statebuild_eala',
        poolbuild_label                        : 'poolbuild_eala',
        azure_fileshare                        : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'fb1_azure_fileshare',
            target_build_share         : 'fb1',
        ],
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [
                    config: 'release',
                ],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
                (LibPerforce.GAMETOOL_FROSTYISOTOOL)              : [],
                (LibPerforce.GAMETOOL_DRONE)                      : [],
                (LibPerforce.GAMETOOL_FRAMEWORK)                  : [],
            ],
        ],
        autotest_remote_settings               : [
            eala: [
                credentials   : 'monkey.commons',
                p4_code_creds : 'fb1-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                p4_data_creds : 'fb1-la-p4',
                p4_data_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            ],
            dice: [
                p4_code_creds : 'perforce-frostbite02-commons',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                p4_data_creds : 'perforce-frostbite02-commons',
                p4_data_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: 'H */4 * * *',
            referenceJob: '.data.start',
            label: 'statebuild_eala'
        ),
    ]
    static Map standard_jobs_settings = [
        asset                       : 'DevLevels',
        archive_pipelinelog_success : true,
        baseline_set                : false,
        bilbo_store_offsite         : false,
        claim_builds                : true,
        clean_local                 : false,
        code_data_sync              : true,
        data_reference_job          : '2024_1_dev-bf.bfdata.upgrade.data',
        reporting_branch            : '2024_1_dev-bf',
        deploy_frostedtests         : true,
        deploy_tests                : true,
        enable_daily_data_clean     : true,
        enable_lkg_cleaning         : true,
        enable_lkg_p4_counters      : true,
        extra_data_args             : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -Pipeline.AssetTypeTimeSummary --pipeline-args -1 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.EmitAssetTypeSummaryFile --pipeline-args true --pipeline-args -Pipeline.MaxWarnings --pipeline-args 0 --pipeline-args Pipeline.EnableWarningCountSummary --pipeline-args true '],
        extra_frosty_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        import_avalanche_autotest   : false,
        use_super_bundles           : true,
        strip_symbols               : false,
        poolbuild_data              : false,
        statebuild_data             : false,
        poolbuild_frosty            : true,
        poolbuild_patchdata         : true,
        poolbuild_patchfrosty       : true,
        produce_build_status        : false,
        trigger_type_data           : 'none',
        trigger_type_patchdata      : 'none',
        server_asset                : 'Game/Setup/Build/DevMPLevels',
        skip_icepick_settings_file  : true,
        slack_channel_code          : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_code_nomaster : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data          : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchdata     : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty   : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        split_code_data_sync        : true,
        statebuild_code             : false,
        timeout_hours_frosty        : 10,
        timeout_hours               : 8,
        timeout_hours_data          : 8,
        timeout_hours_upgrade_data  : 2,
        upgrade_data_job            : true,
        upgrade_data_downstream_jobs: '2024_1_dev-bf.data.start',
        use_linuxclient             : true,
        use_recompression_cache     : false,
        use_zipped_drone_builds     : true,
        webexport_branch            : true,
        cadet_activate_toolset      : true,
        cadet_p4_fb_settings        : [
            p4_creds: 'perforce-frostbite02-commons',
            p4_port : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        ],
    ]

    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]

    static Map preflight_settings = [
        concurrent_code            : 11,
        enable_custom_cl           : true,
        codepreflight_reference_job: '2024_1_dev-bf.code.lastknowngood',
        datapreflight_reference_job: '2024_1_dev-bf.data.lastknowngood',
        extra_codepreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml  --framework-args -D:eaconfig.optimization.ltcg=off",
        force_rebuild_preflight    : true,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        statebuild_codepreflight   : false,
        statebuild_datapreflight   : false,
        use_icepick_test           : true,
        pre_preflight              : true,
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
        [name: 'win64trial', configs: ['final', 'retail']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true], [name: 'deprecation-test', allow_failure: true, compile_unit_tests: true]]],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.bfdata.upgrade.data', args: ['code_changelist']],
    ]
    static List data_matrix = [
        'win64',
        'ps5',
        'xbsx',
        'linux64',
        'server',
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'final', region: 'ww', args: '', allow_failure: true],
                                   [format: 'files', config: 'performance', region: 'ww', args: '']
        ]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev', args: '', allow_failure: true],
                                 [format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: '']
        ]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: '', allow_failure: true],
                                  [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']
        ]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '', allow_failure: true],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.bilbo.register-bfdata-dronebuild', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = [
    ]
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['final']],
    ]
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64']
    ]
}
