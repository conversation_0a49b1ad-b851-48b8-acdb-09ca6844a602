#**********************************************
#                  dice-joss PIPE
#**********************************************
.default-dice-joss-variables:
  extends: .secrets-dice-joss
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "dice-joss"
    WORKING_DIR: "projects/dice-joss"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-dice-joss.json"
    ansible_main_module: fb_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1.json"

prepare-json-config-dice-joss:
  extends: ['.default-dice-joss-variables', '.prepare_config']

validate-dice-joss:
  extends: ['.default-dice-joss-variables', '.validation_steps']

plan-dice-joss:
  needs:
    - job: validate-dice-joss
    - job: prepare-json-config-dice-joss
  extends: ['.default-dice-joss-variables','.plan_steps']

apply-dice-joss:
  needs:
    - job: plan-dice-joss
    - job: prepare-json-config-dice-joss
  extends: ['.default-dice-joss-variables','.apply_steps']

attache-dice-joss:
  needs:
    - job: apply-dice-joss
    - job: prepare-json-config-dice-joss
  extends: ['.default-dice-joss-variables','.attache_vmdk_step']

sync-dice-joss:
  needs:
    - job: attache-dice-joss
    - job: prepare-json-config-dice-joss
  extends: ['.default-dice-joss-variables','.sync_vmdk_step']

ansible-dice-joss:
  needs:
    - job: sync-dice-joss
    - job: prepare-json-config-dice-joss
  extends: ['.default-dice-joss-variables', '.ansible_common_secrets', '.run_ansible_step']
