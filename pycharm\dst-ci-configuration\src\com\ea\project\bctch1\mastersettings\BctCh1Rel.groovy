package com.ea.project.bctch1.mastersettings

import com.ea.project.bctch1.BctCh1

class BctCh1Rel {
    static Class project = BctCh1
    static Map branches = [
        'CH1-stage'                  : [
            code_folder: 'CH1',
            code_branch: 'CH1-stage',
            data_folder: 'CH1',
            data_branch: 'CH1-stage',
        ],
        'CH1-stage-playtest-sp'      : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-stage',
            data_folder            : 'CH1',
            data_branch            : 'CH1-stage-playtest-sp',
            non_virtual_data_branch: 'CH1-stage',
        ],
        'CH1-release'                : [
            code_folder: 'CH1',
            code_branch: 'CH1-release',
            data_folder: 'CH1',
            data_branch: 'CH1-release',
        ],
        'CH1-release-playtest-gnt-na': [
            code_folder            : 'CH1',
            code_branch            : 'CH1-release',
            data_folder            : 'CH1',
            data_branch            : 'CH1-release-playtest-gnt-na',
            non_virtual_data_branch: 'CH1-release',
        ],
        'CH1-bflabs-stage'           : [
            'code_folder'            : 'CH1',
            'code_branch'            : 'CH1-bflabs-stage',
            'data_folder'            : 'CH1',
            'data_branch'            : 'CH1-bflabs-stage',
            'non_virtual_code_branch': 'CH1-stage',
            'non_virtual_data_branch': 'CH1-stage',
        ],
        'CH1-bflabs-release'         : [
            'code_folder'            : 'CH1',
            'code_branch'            : 'CH1-bflabs-release',
            'data_folder'            : 'CH1',
            'data_branch'            : 'CH1-bflabs-release',
            'non_virtual_code_branch': 'CH1-release',
            'non_virtual_data_branch': 'CH1-release',
        ],
        'CH1-bflabs-qol'             : [
            'code_folder'            : 'CH1',
            'code_branch'            : 'CH1-bflabs-qol',
            'data_folder'            : 'CH1',
            'data_branch'            : 'CH1-bflabs-qol',
            'non_virtual_code_branch': 'CH1-qol',
            'non_virtual_data_branch': 'CH1-qol',
        ],
        'CH1-qol'                    : [
            'code_folder': 'CH1',
            'code_branch': 'CH1-qol',
            'data_folder': 'CH1',
            'data_branch': 'CH1-qol',
        ],
        'CH1-SP-stage'               : [
            code_folder: 'CH1',
            code_branch: 'CH1-SP-stage',
            data_folder: 'CH1',
            data_branch: 'CH1-SP-stage',
        ],
        'CH1-SP-release'             : [
            code_folder: 'CH1',
            code_branch: 'CH1-SP-release',
            data_folder: 'CH1',
            data_branch: 'CH1-SP-release',
        ],
        'CH1-event-release'          : [
            code_folder: 'CH1',
            code_branch: 'CH1-event-release',
            data_folder: 'CH1',
            data_branch: 'CH1-event-release',
        ],
        'CH1-event'                  : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-event',
            data_folder            : 'CH1',
            data_branch            : 'CH1-event',
            non_virtual_code_branch: 'CH1-release',
            non_virtual_data_branch: 'CH1-release',
        ],
    ]
    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        // Temporarily enabling ch1-release to ch1-content-dev integrations for COBRA-7061
        'CH1-release_to_CH1-content-dev'              : [
            asset                       : 'Task1ClientLevels',
            branch_guardian             : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            get_integration_info        : true,
            integrate_mapping           : 'BF_[CH1-release]_to_[CH1-content-dev]_IgnoreData_Manual',
            integrate_upgrade_one_stream: true,
            integration_reference_job   : 'CH1-release.data.start',
            job_label                   : 'CH1-release-to-CH1-stage-branch-guardian', //use existing VM for the temporary integrations
            manual_trigger              : false,
            preview_project             : project,
            preview_folder              : 'CH1',
            preview_branch              : 'CH1-release',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-release',
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-content-dev',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
            p4_code_creds               : 'perforce-battlefield-criterion',
            p4_code_server              : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        ],
        'CH1-stage-to-CH1-content-dev-branch-guardian': [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            get_integration_info        : true,
            integrate_mapping           : 'BF_[CH1-stage]_to_[CH1-content-dev]_IgnoreData',
            integrate_upgrade_one_stream: true,
            integration_reference_job   : 'CH1-stage.data.start',
            job_label                   : 'CH1-stage-to-CH1-content-dev-branch-guardian',
            manual_trigger              : true,
            no_submit                   : false,
            preview_project             : project,
            preview_folder              : 'mainline',
            preview_branch              : 'CH1-content-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-stage',
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-content-dev',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
            p4_code_creds               : 'perforce-battlefield-criterion',
            p4_code_server              : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        ],
    ]

    static Map copy_branches = [
        'CH1-release_to_CH1_qol': [
            source_folder               : 'CH1', source_branch: 'CH1-release',
            target_folder               : 'CH1', target_branch: 'CH1-qol',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#bct-build-notify',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'CH1-stage': [code_folder: 'CH1', code_branch: 'CH1-stage', data_folder: 'CH1', data_branch: 'CH1-stage'],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
