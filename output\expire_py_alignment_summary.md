# Expire.py Alignment with PowerShell Script Logic

## Overview

Updated `expire.py` to align with the PowerShell script deletion logic, ensuring consistent behavior across build deletion tools. The key change is implementing a **priority-based deletion system** that matches the PowerShell script approach.

## Key Changes Made

### 1. **Deletion Priority Order (NEW)**

The updated logic now follows this strict priority order:

```
1. 🔴 Orphan builds first (if older than 2 days) - HIGHEST PRIORITY
2. 🟡 Spin builds (by branch configuration)
3. 🟡 Smoke builds (by branch configuration)  
4. 🟡 Release candidate builds
5. 🟡 Promoted builds
6. 🟡 Drone builds (legacy support)
7. 🟢 Count-based retention (delete oldest to maintain maxamount)
```

### 2. **Enhanced Orphan Build Processing**

#### **Before:**
- Orphan builds were processed alongside regular builds
- Simple 2-day age check

#### **After:**
- **Orphan builds get absolute priority for deletion**
- Enhanced logging with precise age calculation
- Better error handling for age determination
- Orphan builds are deleted in Phase 1, before any retention logic

```python
# New logic ensures orphans are deleted first, regardless of count limits
if orphaned_builds:
    LOGGER.info("Phase 1: Deleting %d orphan builds (priority deletion)", len(orphan_builds))
    # Delete orphans first
    
if builds_to_expire:
    LOGGER.info("Phase 2: Deleting %d builds based on retention policy", len(builds_to_expire))
    # Then apply retention logic
```

### 3. **Improved Retention Logic**

#### **Spin/Smoke Build Protection:**
- Now checks spin builds first, then smoke builds
- Proper branch-based retention counting
- Better error handling for configuration issues

#### **Release Candidate Protection:**
- Added dedicated release candidate counter
- Integrated into the priority system

#### **Enhanced Logging:**
- Clear phase-based deletion logging
- Detailed preservation reasons
- Better debugging information

### 4. **Updated Method Signatures and Documentation**

All methods now include comprehensive documentation explaining:
- The priority order alignment with PowerShell script
- Expected behavior for each build type
- Error handling approaches

## Behavioral Changes

### **Before (Old Logic):**
1. Scan all builds
2. Separate orphans (if >2 days old)
3. Apply count-based retention to remaining builds
4. Apply special retention (spin/smoke/RC/promoted) to deletion candidates
5. Delete both orphans and retention-based builds

### **After (New Logic - Aligned with PowerShell):**
1. Scan all builds
2. **Phase 1: Delete orphan builds first (if >2 days old)**
3. **Phase 2: Apply retention logic to remaining builds:**
   - Protect spin builds (by branch)
   - Protect smoke builds (by branch)
   - Protect release candidates
   - Protect promoted builds
   - Apply count-based retention to unprotected builds
4. Clear phase-based execution with detailed logging

## Configuration Alignment

The updated logic now properly respects:

- **`spin_retention`** settings from configuration
- **`smoke_retention`** settings from configuration  
- **`release_candidate_retention`** settings
- **Build promotion levels** from Bilbo metadata
- **2-day minimum age** for orphan deletion

## Logging Improvements

### **New Log Messages:**
```
Phase 1: Deleting X orphan builds (priority deletion)
Phase 2: Deleting X builds based on retention policy
Deletion plan: X orphan builds (priority), X regular builds (retention)
Build X is an orphan but is newer than 2 days (age: X.X hours), keeping for now
Found orphaned build older than 2 days (age: X.X days): path
Preserving build X (spin branch_name build)
```

### **Enhanced Debug Information:**
- Precise age calculations for orphan builds
- Detailed preservation reasons
- Phase-based execution tracking
- Configuration counter initialization logging

## Impact on Build Deletion

### **Positive Changes:**
1. **Consistent behavior** with PowerShell script
2. **Orphan builds are prioritized** for deletion (frees space faster)
3. **Better protection** for important builds (spin/smoke/RC/promoted)
4. **Clearer logging** for debugging and monitoring
5. **More predictable deletion order**

### **Risk Mitigation:**
- Maintains 2-day protection for new orphan builds
- Preserves all existing retention logic
- Backward compatible with existing configurations
- Enhanced error handling prevents unexpected behavior

## Testing Recommendations

1. **Verify orphan build detection** works correctly
2. **Test spin/smoke build protection** with various branch configurations
3. **Confirm release candidate builds** are properly preserved
4. **Check logging output** for clarity and completeness
5. **Validate count-based retention** still works after special build protection

## Configuration Requirements

Ensure these settings are properly configured:

```yaml
spin_retention:
  - branch_name: retention_count

smoke_retention:
  - branch_name: retention_count

release_candidate_retention: count
```

## Conclusion

The updated `expire.py` now provides:
- **Consistent deletion behavior** with PowerShell script
- **Priority-based deletion** (orphans first)
- **Enhanced build protection** for special build types
- **Better logging and debugging** capabilities
- **Maintained backward compatibility** with existing configurations

This alignment ensures that both Jenkins (using expire.py) and the PowerShell script follow the same deletion logic, eliminating the mismatches identified in the earlier analysis.
