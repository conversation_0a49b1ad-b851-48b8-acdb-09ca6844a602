Check this Jenkins job https://dice-build-jenkins.cobra.dre.ea.com/job/hvu-utility.build-deleter.Bct-criterion/7/console
It can fetch the correct retention from elipy_bct.yml
14:49:31 2025-07-03 13:49:31 elipy2 [INFO]: Selected categories are: {'code': [{'default': 10}, {'trunk-content-dev-test': 0}, {'trunk-content-dev': 5}, {'criterion-content-warm': 5}], 'tnt_local': [{'default': 0}], 'frosty\\BattlefieldGame': [{'default': 8}, {'CH1-event-release': 12}, {'CH1-release': 35}, {'CH1-SP-release': 10}, {'CH1-bflabs-release': 10}, {'CH1-qol': 10}, {'CH1-event': 10}, {'CH1-bflabs-qol': 10}]}
14:49:31 2025-07-03 13:49:31 elipy2 [INFO]: Bilbo initalized with endpoint https://bct-bilbo-eck.cobra.dre.ea.com and index criterion_bilbo

But it still could not check the ch1-event branch under the path \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame only those from below
14:51:23 2025-07-03 13:51:23 elipy2 [INFO]: Found 3 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\ch1-event-release
14:51:23 2025-07-03 13:51:23 elipy2 [INFO]: Processing orphaned builds for 3 builds on disk
14:51:23 2025-07-03 13:51:23 elipy2 [INFO]: Found 3 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\ch1-qol
14:51:23 2025-07-03 13:51:23 elipy2 [INFO]: Processing orphaned builds for 3 builds on disk
14:51:23 2025-07-03 13:51:23 elipy2 [INFO]: Found 5 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\ch1-sp-release
14:51:23 2025-07-03 13:51:23 elipy2 [INFO]: Processing orphaned builds for 5 builds on disk
14:51:24 2025-07-03 13:51:23 elipy2 [INFO]: Found 15 builds on disk at \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\ch1-release

Please investigate and fix it. 
The debug script C:\Users\<USER>\vscode\output\total_build_under_path.ps1 can be used as it give the status of both disk and bilbo
the problem now is still why we do not look for ch1-event in our job (either it's in the expire.py or deleter.py)

I think the branch should be fetched from disk rather than from bilbo
so logic should be
1. given this path \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame
2. anything under that path 1 level should be branch
3. 2 level should be CL1
4. 3 level should be branch again
5. 4 level should be CL2
Now we got the full path of the build, and we should start querying Bilbo with that path