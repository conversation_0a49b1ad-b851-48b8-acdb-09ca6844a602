package com.ea.project.bctch1.mastersettings

import com.ea.project.bctch1.BctCh1

class BctCh1Autotest {
    static Class project = BctCh1
    static Map branches = [:]
    static Map preflight_branches = [:]
    static Map autotest_branches = [
        'CH1-code-dev'           : [
            code_folder               : 'CH1',
            code_branch               : 'CH1-code-dev',
            data_folder               : 'CH1',
            data_branch               : 'CH1-code-dev',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            koala_autotest            : true,
        ],
        'CH1-content-dev'        : [
            code_folder               : 'CH1',
            code_branch               : 'CH1-content-dev',
            data_folder               : 'CH1',
            data_branch               : 'CH1-content-dev',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            set_integration_info      : [
                remote_jenkins: 'bct-ch1-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'CH1-content-dev.autotest-to-integration.code',
                test_category : 'lkg_checkmate',
            ],
        ],
        '2024_1_dev-bf-to-CH1'   : [
            code_folder               : 'CH1',
            code_branch               : '2024_1_dev-bf-to-CH1',
            data_folder               : 'CH1',
            data_branch               : '2024_1_dev-bf-to-CH1',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            koala_autotest            : true,
        ],
        'CH1-content-dev-sanitizers': [
            code_folder               : 'CH1',
            code_branch               : 'CH1-content-dev-sanitizers',
            data_folder               : 'CH1',
            data_branch               : 'CH1-content-dev-sanitizers',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
        ],
        'CH1-to-trunk'           : [
            code_folder               : 'CH1',
            code_branch               : 'CH1-to-trunk',
            data_folder               : 'CH1',
            data_branch               : 'CH1-to-trunk',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            koala_autotest            : true,
            set_integration_info      : [
                remote_jenkins: 'bct-ch1-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'CH1-to-trunk.autotest-to-integration.code',
                test_category : 'lkg_checkmate',
            ],
        ],
        'CH1-stage'              : [
            code_folder               : 'CH1',
            code_branch               : 'CH1-stage',
            data_folder               : 'CH1',
            data_branch               : 'CH1-stage',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            set_integration_info      : [
                remote_jenkins: 'bct-ch1-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'CH1-stage.autotest-to-integration.code',
                test_category : 'lkg_checkmate',
            ],
        ],
        'CH1-release'              : [
            code_folder               : 'CH1',
            code_branch               : 'CH1-release',
            data_folder               : 'CH1',
            data_branch               : 'CH1-release',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
        'task2'                    : [
            code_folder               : 'tasks',
            code_branch               : 'task2',
            data_folder               : 'tasks',
            data_branch               : 'task2',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
        ],
        'ecs-splines'              : [
            code_folder               : 'tasks',
            code_branch               : 'ecs-splines',
            data_folder               : 'tasks',
            data_branch               : 'ecs-splines',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
        ],
    ]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'CH1-content-dev': [code_folder: 'CH1', code_branch: 'CH1-content-dev', data_folder: 'CH1', data_branch: 'CH1-content-dev'],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
