#*************************************************************
#  Sets up the initial needs to point to our vSphere server
#*************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# BCT: bct-x, check CONTRIBUTING.md before editing here.
# Labels bct_statebuild and bct_poolbuild are used for bct when we have
# vms with bct code server and bct data server
# *************************************************************
locals {
  module_settings = {
    # EXECUTOR NODES
    "bct_ch1_ps02_executor_node_002" = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 executor_agent", cpu_core = "5", ram_count = 16384 }

    # SMOKE TEST NODE
    "bct_ch1_ps02_smoke_test_002" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps smoke", cpu_core = "5" }

    # STATEBUILD NODES
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_win64_004"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps win64_1 statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_win64_005"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "4", labels = "ps win64_2 statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_win64_006"   = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "6", labels = "ps win64_4 statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_ps5_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps ps5_1 statebuild poolbuild ps5" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_ps5_002"     = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps ps5_2 statebuild poolbuild ps5" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_ps5_003"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "3", labels = "ps ps5_4 statebuild poolbuild ps5" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_ps5_004"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "3", labels = "ps ps5_7 statebuild poolbuild ps5" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_xbsx_001"    = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "3", labels = "ps xbsx_1 statebuild poolbuild xbsx" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_xbsx_002"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps xbsx_3 statebuild poolbuild xbsx" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_xbsx_003"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps xbsx_7 statebuild poolbuild xbsx" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_xbsx_004"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "3", labels = "ps xbsx_8 statebuild poolbuild xbsx" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_linux64_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps linux64_1 statebuild poolbuild linux64" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_servers_002" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps servers_1 statebuild poolbuild server linuxserver" }
    "bct_ch1_ps02_pool_state_bct_ch1_ps02_servers_003" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "3", labels = "ps servers_3 statebuild poolbuild server linuxserver" }

    # CH1-CONTENT-DEV CODE
    "ps02_ch1_content_dev_code_tool_deprecation_test_002" = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps CH1-content-dev code tool deprecation-test" }
    "ps02_ch1_content_dev_code_win64_final_002"           = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code win64game final" }
    "ps02_ch1_content_dev_code_win64_performance_002"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps CH1-content-dev code win64game performance" }
    "ps02_ch1_content_dev_code_win64_retail_ps_002"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code win64game retail" }
    "ps02_ch1_content_dev_code_win64_release_ps_002"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps CH1-content-dev code win64game release" }
    "ps02_ch1_content_dev_code_ps5_final_002"             = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code ps5 final" }
    "ps02_ch1_content_dev_code_ps5_performance_002"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps CH1-content-dev code ps5 performance" }
    "ps02_ch1_content_dev_code_ps5_retail_ps_002"         = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code ps5 retail" }
    "ps02_ch1_content_dev_code_ps5_release_ps_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code ps5 release" }
    "ps02_ch1_content_dev_code_xbsx_final_001"            = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code xbsx final" }
    "ps02_ch1_content_dev_code_xbsx_performance_001"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code xbsx performance" }
    "ps02_ch1_content_dev_code_xbsx_retail_ps_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code xbsx retail" }
    "ps02_ch1_content_dev_code_xbsx_release_ps_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code xbsx release" }
    "ps02_ch1_content_dev_code_win64server_final_001"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code win64server final" }
    "ps02_ch1_content_dev_code_win64server_release_001"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code win64server release" }
    "ps02_ch1_content_dev_code_linuxserver_final_001"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code linux64server final" }
    "ps02_ch1_content_dev_code_linux64_final_001"         = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code linux64 final" }
    "ps02_ch1_content_dev_code_tool_release_001"          = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-content-dev code tool release" }
    "ps02_ch1_content_dev_code_tool_stressbulkbuild_001"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-content-dev tool release code-stressbulkbuild" }

    # STATEBUILD CH1-TO-TRUNK
    "bct_ch1_ps02_pool_state_ch1-to-trunk_xbsx_001"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps xbsx_5 statebuild_CH1-to-trunk poolbuild_CH1-to-trunk xbsx" }
    "bct_ch1_ps02_pool_state_ch1-to-trunk_win64_001"   = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps win64_4 statebuild_CH1-to-trunk poolbuild_CH1-to-trunk win64" }
    "bct_ch1_ps02_pool_state_ch1-to-trunk_ps5_001"     = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps ps5_4 statebuild_CH1-to-trunk poolbuild_CH1-to-trunk ps5" }
    "bct_ch1_ps02_pool_state_ch1-to-trunk_linux64_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps ps5_4 statebuild_CH1-to-trunk poolbuild_CH1-to-trunk linux64" }
    "bct_ch1_ps02_pool_state_ch1-to-trunk_servers_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps servers_1 statebuild_CH1-to-trunk poolbuild_CH1-to-trunk server linuxserver" }

    # CH1-CODE-DEV CODE-NOMASTER
    "ps02_ch1_code_dev_code_nomaster_win64_retail_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code-nomaster win64game retail" }
    "ps02_ch1_code_dev_code_nomaster_ps5_final_001"         = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code-nomaster ps5 final" }
    "ps02_ch1_code_dev_code_nomaster_xbsx_retail_001"       = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code-nomaster xbsx retail" }
    "ps02_ch1_code_dev_code_nomaster_win64server_final_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code-nomaster win64server final" }
    "ps02_ch1_code_dev_code_nomaster_linuxserver_final_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code-nomaster linux64server final" }
    "ps02_ch1_code_dev_code_nomaster_tool_release_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code-nomaster tool release" }

    # CH1-CODE-DEV CODE
    "ps02_ch1_code_dev_code_win64_final_001"           = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code win64game final" }
    "ps02_ch1_code_dev_code_win64_release_ps_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code win64game release" }
    "ps02_ch1_code_dev_code_win64_retail_ps_001"       = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code win64game retail" }
    "ps02_ch1_code_dev_code_win64_performance_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code win64game performance" }
    "ps02_ch1_code_dev_code_ps5_final_001"             = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code ps5 final" }
    "ps02_ch1_code_dev_code_ps5_release_ps_001"        = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code ps5 release" }
    "ps02_ch1_code_dev_code_ps5_retail_ps_001"         = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code ps5 retail" }
    "ps02_ch1_code_dev_code_ps5_performance_001"       = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code ps5 performance" }
    "ps02_ch1_code_dev_code_xbsx_final_001"            = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code xbsx final" }
    "ps02_ch1_code_dev_code_xbsx_release_ps_001"       = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code xbsx release" }
    "ps02_ch1_code_dev_code_xbsx_retail_ps_001"        = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code xbsx retail" }
    "ps02_ch1_code_dev_code_xbsx_performance_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code xbsx performance" }
    "ps02_ch1_code_dev_code_win64server_final_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code win64server final" }
    "ps02_ch1_code_dev_code_win64server_release_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code win64server release" }
    "ps02_ch1_code_dev_code_linuxserver_final_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code linux64server final" }
    "ps02_ch1_code_dev_code_linux64_final_001"         = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code linux64 final" }
    "ps02_ch1_code_dev_code_tool_release_001"          = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code tool release" }
    "ps02_ch1_code_dev_code_tool_deprecation_test_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev code tool deprecation-test" }
    "ps02_ch1_code_dev_code_tool_stressbulkbuild_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps02 CH1-code-dev tool release code-stressbulkbuild" }

    # CH1-CODE-DEV DATA
    "ps02_ch1_code_dev_data_win64_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "3", labels = "ps CH1-code-dev data win64" }
    "ps02_ch1_code_dev_data_win64_002"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data win64" }
    "ps02_ch1_code_dev_data_ps5_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data ps5" }
    "ps02_ch1_code_dev_data_ps5_002"     = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps CH1-code-dev data ps5" }
    "ps02_ch1_code_dev_data_xbsx_001"    = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data xbsx" }
    "ps02_ch1_code_dev_data_xbsx_002"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps CH1-code-dev data xbsx" }
    "ps02_ch1_code_dev_data_server_001"  = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data server" }
    "ps02_ch1_code_dev_data_server_002"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data server" }
    "ps02_ch1_code_dev_data_linux64_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data linux64" }
    "ps02_ch1_code_dev_data_linux64_002" = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev data linux64" }
    "ps02_ch1_code_dev_webexport_001"    = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev webexport" }

    # CH1 PIPELINE DETERMINISM - dedicated VMs required to keep oplogs for investigation - COBRA-4898
    "ps02_ch1_code_dev_code_tool_pipeline_determinism_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-code-dev code win64 pipeline_determinism", cpu_core = "32", cores_per_socket = "32" }
    "ps02_ch1_code_dev_code_tool_pipeline_determinism_002" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-code-dev code debugdb pipeline_determinism", cpu_core = "32", cores_per_socket = "32" }
    "ps02_ch1_code_dev_code_tool_pipeline_determinism_003" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-code-dev code ps5 ", cpu_core = "32", cores_per_socket = "32" }
    "ps02_ch1_code_dev_code_tool_pipeline_determinism_004" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-code-dev code xbsx pipeline_determinism", cpu_core = "32", cores_per_socket = "32" }
    "ps02_ch1_content_dev_code_tool_pipeline_determinism"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-content-dev pipeline_determinism win64", cpu_core = "32", cores_per_socket = "32" }

    # CH1-CONTENT-DEV CODE NOMASTER
    "ps02_ch1_content_dev_code_nomaster_win64_retail_ps_01"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code-nomaster win64game retail" }
    "ps02_ch1_content_dev_code_nomaster_ps5_final_01"         = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code-nomaster ps5 final" }
    "ps02_ch1_content_dev_code_nomaster_xbsx_retail_ps_01"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code-nomaster xbsx retail" }
    "ps02_ch1_content_dev_code_nomaster_win64server_final_01" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code-nomaster win64server final" }
    "ps02_ch1_content_dev_code_nomaster_linuxserver_final_01" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code-nomaster linux64server final" }
    "ps02_ch1_content_dev_code_nomaster_tool_release_01"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev code-nomaster tool release" }

    # CH1-CONTENT-DEV DATA
    "ps02_ch1_content_dev_data_linux64_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps02 CH1-content-dev data linux64" }
    "ps02_ch1_content_dev_data_linux64_002" = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev data linux64" }
    "ps02_ch1_content_dev_data_ps5_001"     = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev data ps5" }
    "ps02_ch1_content_dev_data_ps5_002"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps02 CH1-content-dev data ps5" }
    "ps02_ch1_content_dev_data_server_001"  = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps02 CH1-content-dev data server" }
    "ps02_ch1_content_dev_data_server_002"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev data server" }
    "ps02_ch1_content_dev_data_win64_001"   = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 CH1-content-dev data win64" }
    "ps02_ch1_content_dev_data_win64_002"   = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps02 CH1-content-dev data win64" }
    "ps02_ch1_content_dev_data_xbsx_001"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps02 CH1-content-dev data xbsx" }
    "ps02_ch1_content_dev_data_xbsx_002"    = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev data xbsx" }
    "ps02_ch1_content_dev_webexport_001"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-content-dev webexport" }

    # CH1-playtest-san
    "ps02_ch1_playtest_san_frosty_win64_final_steam_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-playtest-san frosty win64 final steam_build" }
    "ps02_ch1_playtest_san_frosty_win64_performance_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-playtest-san frosty win64 performance steam_build" }

    # CH1-playtest-sp
    "ps02_ch1_playtest_sp_frosty_win64_final_steam_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-playtest-sp frosty win64 final steam_build" }
    "ps02_ch1_playtest_sp_frosty_win64_performance_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 CH1-playtest-sp frosty win64 performance steam_build" }

    # CH1-playtest-gnt
    "ps02_ch1_playtest_gnt_frosty_win64_final_steam_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 CH1-playtest-gnt frosty win64 final steam_build" }
    "ps02_ch1_playtest_gnt_frosty_win64_performance_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-playtest-gnt frosty win64 performance steam_build" }

    # CH1-CONTENT-DEV FROSTY
    "ps02_ch1_content_dev_frosty_win64_final_combine_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty win64 final combine" }
    "ps02_ch1_content_dev_frosty_win64_retail_combine_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty win64 retail combine" }
    "ps02_ch1_content_dev_frosty_win64_retail_steam_build_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty win64 retail steam_build" }
    "ps02_ch1_content_dev_frosty_win64_final_files_001"        = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty win64 final files" }
    "ps02_ch1_content_dev_frosty_win64_performance_files_001"  = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty win64 performance files" }
    "ps02_ch1_content_dev_frosty_win64_release_files_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty win64 release files" }
    "ps02_ch1_content_dev_frosty_ps5_final_combine_001"        = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty ps5 final combine" }
    "ps02_ch1_content_dev_frosty_ps5_retail_combine_001"       = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty ps5 retail combine" }
    "ps02_ch1_content_dev_frosty_ps5_final_files_001"          = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty ps5 final files" }
    "ps02_ch1_content_dev_frosty_ps5_performance_files_001"    = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty ps5 performance files" }
    "ps02_ch1_content_dev_frosty_xbsx_final_combine_001"       = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty xbsx final combine" }
    "ps02_ch1_content_dev_frosty_xbsx_retail_combine_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty xbsx retail combine" }
    "ps02_ch1_content_dev_frosty_xbsx_final_files_001"         = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty xbsx final files" }
    "ps02_ch1_content_dev_frosty_xbsx_performance_files_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty xbsx performance files" }
    "ps02_ch1_content_dev_frosty_linux64_final_files_001"      = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty linux64 final files" }
    "ps02_ch1_content_dev_frosty_server_final_001"             = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty server final files digital" }
    "ps02_ch1_content_dev_frosty_linuxserver_final_001"        = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-content-dev frosty linuxserver final files digital" }

    # COPY BUILD TO AZURE
    "ps02_ch1_copy_build_to_azure_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps02 copy_build_to_azure" }

    # INTEGRATIONS
    "ps02_ch1_content_dev_to_ch1_code_dev_bg_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "CH1-content-dev-to-CH1-code-dev-branch-guardian", ram_count = 131072 }
    "ps02_ch1_code_dev_to_ch1_content_dev_bg_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "CH1-code-dev_to_CH1-content-dev" }
    "ps02_ch1_to_trunk_to_trunk_content_dev_bg_001"     = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "1", labels = "CH1-to-trunk-to-trunk-content-dev-branch-guardian" }
    "ps02_ch1_content_dev_to_CH1_SP_content_dev_bg_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "CH1-content-dev-to-CH1-SP-content-dev-branch-guardian" }
    "ps02_trunk-content-dev_to_CH1-to-trunk_bg_001"     = { datastore = "BPS02-A2_DRE-BUILD-VMS-01", vm_count = "1", labels = "trunk-content-dev_to_CH1-to-trunk-guardian" }
    "ps02_ch1_content_dev_to_ch1_to_trunk_bg_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "CH1-content-dev-to-CH1-to-trunk-branch-guardian" }
    "ps02_ch1-code-dev_to_task5_001"                    = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "CH1-code-dev_to_task5" }
    "ps02_ch1-content-dev_to_task4_001"                 = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "CH1-content-dev_to_task4" }
    "ps02_ch1_sp_content_dev_to_ch1_content_dev_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "CH1-SP-content-dev-to-CH1-content-dev" }

    # CH1-CONTENT-DEV-METRICS
    "ps02_ch1_content_dev_metrics_001"           = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev-metrics code tool release data win64 linux64server final" }
    "ps02_ch1_content_dev_metrics_win64game_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev-metrics code win64game release final performance retail" }
    "ps02_ch1_content_dev_metrics_ps5_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev-metrics code ps5 release final performance retail" }
    "ps02_ch1_content_dev_metrics_xbsx_001"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps CH1-content-dev-metrics code xbsx release final performance retail" }

    # CH1-code-dev-sanitizers
    "ps02_ch1_code_asan_tool_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev-sanitizers code release xbsx linux64server win64server linux64 final retail" }
    "ps02_ch1_code_asan_tool_002" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps CH1-code-dev-sanitizers code tool release win64game ps5 linux64 final retail" }

    # CH1-content-dev-sanitizers
    "ps02_ch1_content_asan_tool_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps CH1-content-dev-sanitizers code release xbsx linux64server win64server linux64 final retail" }
    "ps02_ch1_content_asan_tool_002" = { datastore = "BPS02-A2_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps CH1-content-dev-sanitizers code tool release win64game ps5 linux64 final retail" }

    # UTILITIES
    "bct_ch1_ps02_deleter_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "1", labels = "deleter bct_ch1_ps02", role = "https://dice-build-jenkins.cobra.dre.ea.com/" }

    # CH1-TO-TRUNK
    "ps_ch1_to_trunk_code_tool_linux_001" = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-to-trunk code tool linux64 release final" }
    "ps_ch1_to_trunk_code_win64_001"      = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 CH1-to-trunk code win64game final release retail performance" }
    "ps_ch1_to_trunk_code_ps5_001"        = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 CH1-to-trunk code ps5 final release retail performance" }
    "ps_ch1_to_trunk_code_xbsx_001"       = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 CH1-to-trunk code xbsx final release retail performance" }
    "ps_ch1_to_trunk_code_servers_001"    = { datastore = "BPS02-A2_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 CH1-to-trunk code win64server linux64server final" }

    # STEAM BUILD
    "ch1_content_dev_steam_build_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "win64 CH1-content-dev final steam_build frosty" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bct2-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-ch1-dev-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DICE-BUILD")
  ram_count               = try(each.value.ram_count, 65536)
  cpu_core                = try(each.value.cpu_core, "")
  cores_per_socket        = try(each.value.cores_per_socket, "")
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")

  vsphere_template      = var.packer_template
  vsphere_network       = var.bct_network
  vsphere_datacenter    = var.bct_datacenter
  vsphere_folder        = "DICE/dre-terraform-nodes/bct_nodes"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = "dice.ad.ea.com"
  domain_ou             = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
  local_admin_group     = var.local_admin_group
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
