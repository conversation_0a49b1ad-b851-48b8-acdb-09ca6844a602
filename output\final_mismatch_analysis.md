# Final Mismatch Analysis: <PERSON> vs <PERSON><PERSON><PERSON> Deletion Lists

## Executive Summary

The investigation reveals a **fundamental difference** in deletion criteria between <PERSON> and the PowerShell script, explaining the mismatch in the file comparison report.

## Key Findings

### 🔍 **Root Cause Identified**

**<PERSON> and the script use completely different deletion criteria:**

1. **Jenkins**: Uses **retention-based logic** (age, count limits, orphaned detection)
2. **Script**: Uses **Bilbo deletion status** (only builds marked as "deleted")

### 📊 **Detailed Results**

#### **Jenkins-Only Builds (8 builds)**
<PERSON> wants to delete these, but script doesn't:

| Build | Found in Bilbo | Marked as Deleted | Jenkins Reason |
|-------|----------------|-------------------|----------------|
| 24216947 | ✅ Yes | ❌ No | Retention logic |
| 24221370 | ✅ Yes | ❌ No | Retention logic |
| 24230552 | ✅ Yes | ❌ No | Retention logic |
| 24237548 | ✅ Yes | ❌ No | Retention logic |
| 24254078 | ✅ Yes | ❌ No | Retention logic |
| 24283046 | ✅ Yes | ❌ No | Retention logic |
| 24286233 | ✅ Yes | ❌ No | Retention logic |
| 24288006 | ✅ Yes | ❌ No | Retention logic |

**Analysis**: All 8 builds exist in Bilbo but are **NOT marked as deleted**. Jenkins is deleting them based on retention policies (likely exceeding the 10-build limit for CH1-event), not because they're marked as deleted.

#### **Script-Only Build (1 build)**
Script wants to delete this, but Jenkins doesn't:

| Build | Found in Bilbo | Marked as Deleted | Creation Time | Issue |
|-------|----------------|-------------------|---------------|-------|
| 24310479 | ✅ Yes | ✅ **Yes** | 2025-05-06T16:00:18 | **Script error** |

**Analysis**: This build is marked as deleted in Bilbo (creation time shows May 6, 2025), but Jenkins didn't delete it. This suggests either:
- Jenkins has additional protection logic
- The build was marked as deleted after the Jenkins run
- There's an inconsistency in Jenkins deletion logic

## 🚨 **Critical Issues Identified**

### 1. **Jenkins Over-Deletion Risk**
- Jenkins is deleting 8 builds that are **NOT marked as deleted** in Bilbo
- This suggests Jenkins retention logic may be too aggressive
- These builds might still be valid and shouldn't be deleted

### 2. **Script Logic Error**
- The script identified build 24310479 as deletable because it's marked as deleted in Bilbo
- However, this build has a creation time of **May 6, 2025** (future date!)
- This indicates either:
  - Data corruption in Bilbo
  - Incorrect timestamp handling
  - The build was incorrectly marked as deleted

### 3. **Inconsistent Deletion Criteria**
- Jenkins uses retention policies (count-based, age-based)
- Script uses Bilbo deletion status
- **These should be aligned** for consistency

## 🔧 **Root Cause Analysis**

### **Why Jenkins Deletes More Builds**

Looking at the Jenkins log from earlier investigation:
```
retaining 10 at file:\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-event
```

Jenkins is configured to retain only **10 builds** for CH1-event. Since there are more than 10 builds on disk, Jenkins deletes the oldest ones to maintain the limit, regardless of their Bilbo deletion status.

### **Why Script Identifies Fewer Builds**

The script only looks for builds that are explicitly marked as "deleted" in Bilbo. It doesn't consider:
- Retention count limits
- Age-based policies
- Orphaned build detection

## 📋 **Recommendations**

### **Immediate Actions**

1. **Investigate Build 24310479**:
   - Verify why it has a future creation date (May 6, 2025)
   - Check if it was incorrectly marked as deleted
   - Determine if this represents a data integrity issue

2. **Review Jenkins Retention Logic**:
   - Confirm that deleting 8 non-deleted builds is intentional
   - Verify the 10-build retention limit is appropriate
   - Check if additional protection rules should apply

3. **Align Deletion Criteria**:
   - Decide whether to use Bilbo deletion status or retention policies as primary criteria
   - Update either Jenkins or script logic to match

### **Long-term Solutions**

1. **Standardize Deletion Logic**:
   - Create a unified deletion policy that both systems follow
   - Document the criteria clearly

2. **Improve Script Logic**:
   - Add retention count checking to match Jenkins
   - Include age-based validation
   - Add data integrity checks (e.g., future dates)

3. **Add Safety Checks**:
   - Implement cross-validation between systems
   - Add warnings for mismatched deletion lists
   - Include manual approval for large deletion batches

## 🎯 **Conclusion**

The mismatch is **expected behavior** given that Jenkins and the script use different deletion criteria:

- **Jenkins**: Retention-based (deletes oldest builds to maintain count limit)
- **Script**: Status-based (deletes builds marked as deleted in Bilbo)

However, the investigation revealed potential issues:
1. **Data integrity problem** with build 24310479 (future creation date)
2. **Possible over-deletion** by Jenkins of valid builds
3. **Need for aligned deletion policies** across systems

The mismatch itself isn't necessarily wrong, but it highlights the need for consistent deletion criteria and better data validation.
