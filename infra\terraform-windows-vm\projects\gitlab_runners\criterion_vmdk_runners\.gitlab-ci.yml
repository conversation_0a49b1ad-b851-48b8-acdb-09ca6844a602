#**********************************************
#               VMDK RUNNERS PIPE
#**********************************************
.default-criterion-vmdk-runners-variables:
  extends: .secrets-criterion_vmdk_runners
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "criterion_vmdk_runners"
    WORKING_DIR: "projects/gitlab_runners/criterion_vmdk_runners"
    NODE_INFO_FILE: "node-info-criterion-vmdk-runners.json"
    VC_HOST: oh-vcenter1.ad.ea.com
    VAULT_RATE_LIMIT: 50
    VAULT_ADDR: "https://ess.ea.com"
    VAULT_NAMESPACE: "cds-dre-prod"
    VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
    VAULT_AUTH_LOGIN_PATH: "auth/jwt/gitlab/login"
    SECRET_TOP: "secrets/kv/cobra/automation/gitlab"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log
    ansible_main_module: vmdk_runners_silverback
    SILVERBACK_CONFIG_JSON_FILE: "vmdk_runners.json"

prepare-json-config-criterion-vmdk-runners:
  extends: ['.default-criterion-vmdk-runners-variables', '.prepare_config']

validate-criterion-vmdk-runners:
  extends: ['.default-criterion-vmdk-runners-variables', '.validation_steps']

plan-criterion-vmdk-runners:
  extends: ['.default-criterion-vmdk-runners-variables', '.plan_steps_runner']

apply-criterion-vmdk-runners:
  needs:
    - job: plan-criterion-vmdk-runners
  extends: ['.default-criterion-vmdk-runners-variables', '.apply_steps']

ansible-criterion-vmdk-runners:
  needs:
    - job: apply-criterion-vmdk-runners
    - job: prepare-json-config-criterion-vmdk-runners
  extends: ['.default-criterion-vmdk-runners-variables', '.ansible_common_secrets', '.run_ansible_step']
