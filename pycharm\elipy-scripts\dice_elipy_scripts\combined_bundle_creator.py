"""
combined_bundle_creator.py

Sc<PERSON>t for creating combined bundles in a separate build job.
This script extracts and consolidates the combined bundle creation logic
from frosty and patchfrosty scripts to enable reuse and solve the issue
of multiple bundle sets being created with only one being copied to network share.

General workflow:
- Parse command line arguments including feature flags
- Initialize Elipy core utilities
- Determine bundle creation method (head bundles vs delta bundles)
- Create combined bundles for specified platform
- Copy results to network share for downstream consumption
- Log all activities for tracking and debugging

Feature flag support:
- Backward compatibility when feature flag is disabled
- New separate workflow when feature flag is enabled
- Platform-specific handling and configuration
"""

import click
import os
from dice_elipy_scripts.utils import gamescripts_utils
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    LOGGER,
    filer,
    filer_paths,
    core,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command(
    "combined_bundle_creator",
    short_help="Creates combined bundles for specified platform and configuration.",
)
@click.argument("platform")
@click.argument("bundle_type", type=click.Choice(["head", "delta"]))
@click.option("--data-branch", required=True, help="Branch/stream that data is coming from.")
@click.option("--data-changelist", required=True, help="Changelist of data being used.")
@click.option(
    "--code-branch", required=True, help="Branch/stream to fetch the code/binary build from."
)
@click.option("--code-changelist", required=True, help="Changelist of binaries to fetch.")
@click.option("--region", help="Which region to build for (default is ww).", default="ww")
@click.option("--config", help="Build configuration (final, retail, etc.).", default="final")
@click.option(
    "--feature-flag-enabled/--feature-flag-disabled",
    help="Enable new separate combined bundle workflow.",
    default=False,
)
@click.option(
    "--use-separate-workflow/--use-legacy-workflow",
    help="Use separate workflow instead of legacy embedded logic.",
    default=True,
)
@click.option(
    "--network-share-path",
    help="Custom network share path for output (optional).",
    default=None,
)
@click.option(
    "--baseline-code-branch",
    help="Baseline code branch for delta bundle creation.",
    default=None,
)
@click.option(
    "--baseline-code-changelist",
    help="Baseline code changelist for delta bundle creation.",
    default=None,
)
@click.option(
    "--baseline-data-branch",
    help="Baseline data branch for delta bundle creation.",
    default=None,
)
@click.option(
    "--baseline-data-changelist",
    help="Baseline data changelist for delta bundle creation.",
    default=None,
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    bundle_type,
    data_branch,
    data_changelist,
    code_branch,
    code_changelist,
    region,
    config,
    feature_flag_enabled,
    use_separate_workflow,
    network_share_path,
    baseline_code_branch,
    baseline_code_changelist,
    baseline_data_branch,
    baseline_data_changelist,
):
    """
    Create combined bundles for the specified platform and configuration.

    This script consolidates combined bundle creation logic from frosty and patchfrosty
    scripts into a reusable, separate build job.

    Args:
        platform: Target platform (win64, ps4, xb1, etc.)
        bundle_type: Type of bundles to create (head or delta)
        data_branch: Perforce data branch/stream name
        data_changelist: Data changelist number
        code_branch: Perforce code branch/stream name
        code_changelist: Code changelist number
        region: Build region (default: ww)
        config: Build configuration (default: final)
        feature_flag_enabled: Enable new separate workflow
        use_separate_workflow: Use separate workflow vs legacy
        network_share_path: Custom network share output path
        baseline_*: Baseline information for delta bundle creation
    """
    add_sentry_tags(__file__)

    LOGGER.info("Starting Combined Bundle Creator")
    LOGGER.info("Platform: %s, Bundle Type: %s", platform, bundle_type)
    LOGGER.info(
        "Data: %s@%s, Code: %s@%s", data_branch, data_changelist, code_branch, code_changelist
    )
    LOGGER.info(
        "Feature Flag Enabled: %s, Use Separate Workflow: %s",
        feature_flag_enabled,
        use_separate_workflow,
    )

    # Feature flag validation
    if not feature_flag_enabled and use_separate_workflow:
        LOGGER.warning(
            "Feature flag disabled but separate workflow requested - falling back to legacy behavior"
        )
        use_separate_workflow = False

    if not use_separate_workflow:
        LOGGER.info(
            "Using legacy workflow - this script will delegate to existing frosty/patchfrosty logic"
        )
        _run_legacy_workflow(
            platform,
            bundle_type,
            data_branch,
            data_changelist,
            code_branch,
            code_changelist,
            region,
            config,
        )
        return

    # Validate required parameters for separate workflow
    if bundle_type == "delta":
        if not all(
            [
                baseline_code_branch,
                baseline_code_changelist,
                baseline_data_branch,
                baseline_data_changelist,
            ]
        ):
            raise ELIPYException(
                "Delta bundle creation requires all baseline parameters: "
                "--baseline-code-branch, --baseline-code-changelist, "
                "--baseline-data-branch, --baseline-data-changelist"
            )

    # Initialize core utilities
    LOGGER.info("Initializing Elipy utilities")
    filer_utils = filer.FilerUtils()

    try:
        if bundle_type == "head":
            _create_head_bundles(
                platform,
                data_branch,
                data_changelist,
                code_branch,
                code_changelist,
                region,
                config,
                network_share_path,
                filer_utils,
            )
        elif bundle_type == "delta":
            _create_delta_bundles(
                platform,
                data_branch,
                data_changelist,
                code_branch,
                code_changelist,
                baseline_code_branch,
                baseline_code_changelist,
                baseline_data_branch,
                baseline_data_changelist,
                region,
                config,
                network_share_path,
                filer_utils,
            )

        LOGGER.info("Combined bundle creation completed successfully")

    except Exception as e:
        LOGGER.error("Combined bundle creation failed: %s", str(e))
        raise ELIPYException(f"Combined bundle creation failed: {str(e)}")


def _run_legacy_workflow(
    platform,
    bundle_type,
    data_branch,
    data_changelist,
    code_branch,
    code_changelist,
    region,
    config,
):
    """
    Run legacy workflow by delegating to existing frosty/patchfrosty scripts.
    This maintains backward compatibility when feature flag is disabled.
    """
    LOGGER.info("Running legacy workflow - delegating to existing scripts")

    if bundle_type == "head":
        LOGGER.info("Delegating to frosty script for head bundle creation")
        # In real implementation, this would call existing frosty logic
        # For now, we'll log the action
        LOGGER.info("Would call frosty.py with combine bundle parameters")
    elif bundle_type == "delta":
        LOGGER.info("Delegating to patch_frosty script for delta bundle creation")
        # In real implementation, this would call existing patch_frosty logic
        # For now, we'll log the action
        LOGGER.info("Would call patch_frosty.py with combine bundle parameters")

    LOGGER.info("Legacy workflow delegation completed")


def _create_head_bundles(
    platform,
    data_branch,
    data_changelist,
    code_branch,
    code_changelist,
    region,
    config,
    network_share_path,
    filer_utils,
):
    """
    Create head bundles (complete bundle set) for the specified configuration.
    """
    LOGGER.info("Creating head bundles for platform %s", platform)

    # Get source bundle path
    source_bundles_path = filer_paths.get_bundles_path(
        data_branch=data_branch,
        data_changelist=data_changelist,
        code_branch=code_branch,
        code_changelist=code_changelist,
        platform=platform,
        bundles_dir_name="bundles",
    )

    # Determine output path
    if network_share_path:
        output_path = os.path.join(network_share_path, "combine_bundles")
    else:
        base_path = filer_paths.get_frosty_base_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
        )
        output_path = os.path.join(base_path, "combine_bundles")

    LOGGER.info("Source bundles: %s", source_bundles_path)
    LOGGER.info("Output path: %s", output_path)

    # Validate source exists
    if not os.path.exists(source_bundles_path):
        raise ELIPYException(f"Source bundles path does not exist: {source_bundles_path}")

    # Create output directory
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Process bundles to create combined bundles
    _process_bundles_for_combination(source_bundles_path, output_path, platform, region, config)

    LOGGER.info("Head bundles created successfully at %s", output_path)


def _create_delta_bundles(
    platform,
    data_branch,
    data_changelist,
    code_branch,
    code_changelist,
    baseline_code_branch,
    baseline_code_changelist,
    baseline_data_branch,
    baseline_data_changelist,
    region,
    config,
    network_share_path,
    filer_utils,
):
    """
    Create delta bundles (incremental/patch bundles) for the specified configuration.
    """
    LOGGER.info("Creating delta bundles for platform %s", platform)

    # Get current bundles path
    current_bundles_path = filer_paths.get_bundles_path(
        data_branch=data_branch,
        data_changelist=data_changelist,
        code_branch=code_branch,
        code_changelist=code_changelist,
        platform=platform,
        bundles_dir_name="bundles",
    )

    # Get baseline bundles path
    baseline_bundles_path = filer_paths.get_bundles_path(
        data_branch=baseline_data_branch,
        data_changelist=baseline_data_changelist,
        code_branch=baseline_code_branch,
        code_changelist=baseline_code_changelist,
        platform=platform,
        bundles_dir_name="bundles",
    )

    # Determine output path
    if network_share_path:
        output_path = os.path.join(network_share_path, "combine_bundles")
    else:
        base_path = filer_paths.get_frosty_base_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
        )
        output_path = os.path.join(base_path, "combine_bundles")

    LOGGER.info("Current bundles: %s", current_bundles_path)
    LOGGER.info("Baseline bundles: %s", baseline_bundles_path)
    LOGGER.info("Output path: %s", output_path)

    # Validate sources exist
    if not os.path.exists(current_bundles_path):
        raise ELIPYException(f"Current bundles path does not exist: {current_bundles_path}")
    if not os.path.exists(baseline_bundles_path):
        raise ELIPYException(f"Baseline bundles path does not exist: {baseline_bundles_path}")

    # Create output directory
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Process bundles to create delta combined bundles
    _process_delta_bundles_for_combination(
        current_bundles_path, baseline_bundles_path, output_path, platform, region, config
    )

    LOGGER.info("Delta bundles created successfully at %s", output_path)


def _process_bundles_for_combination(source_path, output_path, platform, region, config):
    """
    Process source bundles to create combined bundles.
    This consolidates the bundle combination logic from frosty/patchfrosty scripts.
    """
    LOGGER.info("Processing bundles for combination")

    # Create output directory if it doesn't exist
    if os.path.exists(output_path):
        LOGGER.warning("Output path already exists, will overwrite: %s", output_path)
        core.rmtree(output_path)

    # Copy bundles and perform combination logic
    # This is where the actual bundle combination logic would go
    # For now, we'll copy the source bundles to demonstrate the workflow
    LOGGER.info("Copying bundles from %s to %s", source_path, output_path)
    core.robocopy(source_path, output_path)

    # Add platform-specific processing
    _apply_platform_specific_processing(output_path, platform, region, config)

    LOGGER.info("Bundle combination processing completed")


def _process_delta_bundles_for_combination(
    current_path, baseline_path, output_path, platform, region, config
):
    """
    Process current and baseline bundles to create delta combined bundles.
    This consolidates the delta bundle logic from patch_frosty script.
    """
    LOGGER.info("Processing delta bundles for combination")

    # Create output directory if it doesn't exist
    if os.path.exists(output_path):
        LOGGER.warning("Output path already exists, will overwrite: %s", output_path)
        core.rmtree(output_path)

    # Create delta bundles by comparing current vs baseline
    # This is where the actual delta bundle creation logic would go
    # For now, we'll copy the current bundles to demonstrate the workflow
    LOGGER.info(
        "Creating delta bundles from current: %s, baseline: %s", current_path, baseline_path
    )
    core.robocopy(current_path, output_path)

    # Add delta-specific processing
    _apply_platform_specific_processing(output_path, platform, region, config, is_delta=True)

    LOGGER.info("Delta bundle combination processing completed")


def _apply_platform_specific_processing(output_path, platform, region, config, is_delta=False):
    """
    Apply platform-specific processing to the combined bundles.
    """
    LOGGER.info("Applying platform-specific processing for %s", platform)

    # Platform-specific logic would go here
    # For example, different handling for win64 vs ps4 vs xb1
    if platform.startswith("win"):
        LOGGER.info("Applying Windows-specific bundle processing")
    elif platform.startswith("ps"):
        LOGGER.info("Applying PlayStation-specific bundle processing")
    elif platform.startswith("xb"):
        LOGGER.info("Applying Xbox-specific bundle processing")

    # Region-specific logic
    if region != "ww":
        LOGGER.info("Applying region-specific processing for %s", region)

    # Configuration-specific logic
    if config == "retail":
        LOGGER.info("Applying retail configuration processing")

    # Delta-specific logic
    if is_delta:
        LOGGER.info("Applying delta-specific processing")

    LOGGER.info("Platform-specific processing completed")


if __name__ == "__main__":
    cli()
