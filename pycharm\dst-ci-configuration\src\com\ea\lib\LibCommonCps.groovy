package com.ea.lib

import com.ea.exceptions.CobraException
import groovy.json.JsonOutput
import hudson.model.Result
import io.jenkins.cli.shaded.org.apache.commons.lang.exception.ExceptionUtils
import javaposse.jobdsl.dsl.jobs.FreeStyleJob

/*
This class is intended to hold CPS compliant code. This means you are restricted in what Groovy language
features you can use. You should avoid using any features that require a Closure to be passed, for example:
    - list.toSorted { ... }
    - list.each { ... }
    - list.foreach { ... }

The code in this class can be used in both Job DSL script as well as Pipeline scripts.

Its important to note that you should not annotate functions with "@NonCPS" since JobDSL scripts cannot import
the require class.

See https://www.jenkins.io/doc/book/pipeline/cps-method-mismatches/#calling-non-cps-transformed-methods-with-cps-transformed-arguments for
more details.
 */

class LibCommonCps {

    /**
     * Prints the exception and exits the Jenkins pipeline
     * @param context Scheduler context
     * @param exception the error to print
     * @param result the pipeline result to set. Default FAILURE
     * @param optionalMessage if set, prints the message in addition to the exception
     * @param doExitPipeline whether or not to exit the pipeline after printing the exception. Default true.
     */
    static void handleExceptionWithResult(def context, Exception exception, Result result = Result.FAILURE, String optionalMessage = null,
                                          boolean doExitPipeline = true) {
        String message = optionalMessage
        context.currentBuild.result = result
        if (message) {
            context.echo message
        }
        context.echo "Exception: ${ExceptionUtils.getStackTrace(exception)}"
        if (doExitPipeline) {
            message = message ? "$message\n" : ''
            context.error "${message}${exception}"
        }
    }

    /**
     * Function to get the curl command for offside build trigger
     */
    static String get_offsite_curl_cmd(def branch_info) {
        def cmd = 'C:\\ProgramData\\Chocolatey\\bin\\curl -X POST -u ' + branch_info.offsite_code_token + ' ' + branch_info.offsite_job_remote + ' || exit /b 0'
        return cmd
    }

    /**
     * Function to get the curl command for triggering ADO Code Preflight Pipeline
     */
    static String get_ado_code_trigger_curl_cmd(def context, def branchFile, def adoToken, def preflighter, def shelfChangelist, def baseChangelist) {
        def pipeline_id = branchFile.preflight_settings.ado_code_preflight_pipeline_id
        Map parametersMap = [
            'Preflighter'    : preflighter,
            'BaseChangelist' : baseChangelist,
            'ShelfChangelist': shelfChangelist,
        ]
        def cmd = get_ado_curl_cmd(context, branchFile, adoToken, pipeline_id, parametersMap)
        return cmd
    }

    /**
     * Function to get the curl command for triggering ADO Data Preflight Pipeline
     */
    static String get_ado_data_trigger_curl_cmd(def context, def branchFile, def adoToken, def preflighter, def shelfChangelist, def baseChangelist, def codeChangelist) {
        def pipeline_id = branchFile.preflight_settings.ado_data_preflight_pipeline_id
        Map parametersMap = [
            'Preflighter'       : preflighter,
            'BaseChangelist'    : baseChangelist,
            'PipelineChangelist': codeChangelist,
            'ShelfChangelist'   : shelfChangelist,
        ]
        def cmd = get_ado_curl_cmd(context, branchFile, adoToken, pipeline_id, parametersMap)
        return cmd
    }

    /**
     * Function to get the curl command for triggering ADO Pipelines
     */
    static String get_ado_curl_cmd(def context, def branchFile, def adoToken, def pipelineId, def parametersMap) {
        def ado_organization = branchFile.preflight_settings.ado_organization
        def ado_project = branchFile.preflight_settings.ado_project
        def adoPipelineTriggerUrl = "https://dev.azure.com/${ado_organization}/${ado_project}/_apis/pipelines/${pipelineId}/runs?api-version=7.2-preview.1"

        Map requestData = [
            'templateParameters': [
                'BuildLink' : "${context.env.BUILD_URL}",
                'JenkinsUrl': "${context.env.JENKINS_URL}",
                'BuildId'   : "${context.env.BUILD_ID}",
                'Source'    : 'Jenkins',
            ]
        ]
        requestData.templateParameters += parametersMap
        def jsonBody = JsonOutput.toJson(requestData)

        def cmd = "curl -X POST -H 'Content-Type: application/json' -H 'Authorization: Basic ${adoToken}' -d '${jsonBody}' ${adoPipelineTriggerUrl}"
        return cmd
    }
    /*
    * Checks for a certain build if there is a previous one in the last XX specified number of days when a clean-build label is on it.
    * @param currentBuild	The build in question with which to compare previous builds.
    * @param day	Specifies number of days.
    * @param hour	Specifies number of hours.
    * @param min	Specifies number of minutes.
    * @param sec	Specifies number of seconds.
    */

    static boolean isRecentCleanBuildByTime(def currentBuild, int day = 0, int hour = 0, int min = 0, int sec = 0) {
        if ((day < 0) || (hour < 0 || hour > 23) || (min < 0 || min > 59) || (sec < 0 || sec > 59)) {
            throw new IllegalArgumentException(" Illegal date values passed: day(s) [>0]: ${day}, hour(s) [0-23]: ${hour}, min(s) [0-59]: ${min}, sec(s) [0-59]: ${sec}")
        }

        int totalMilliseconds = (day * 24 * 60 * 60 + hour * 60 * 60 + min * 60 + sec) * 1000

        boolean foundRecentCleanBuild = currentBuild.rawBuild.parent.builds.any { previousBuild ->
            System.currentTimeMillis() - previousBuild.startTimeInMillis <= totalMilliseconds &&
                previousBuild.description != null &&
                previousBuild.description.contains('clean-build')
        }

        return foundRecentCleanBuild
    }

    /*
    * Checks for a given build if there has been a previous clean build in the last XX specified # of builds, relying on a clean-build label.
    * @param currentBuild	The build in question with which to compare previous builds.
    * @param project	The project settings. Might contain a clean_build_cadence variable that sets buildsPassed.
    * @param branchfile	The branch settings. Might contain a clean_build_cadence varialbe that sets buildsPassed.
    * @param defaultBuildsPassed	The default value for buildsPassed. If project and branchfile lack clean_build_cadence, this value will get chosen and can even be overloaded.
    */

    static boolean isRecentCleanBuildByBuilds(def currentBuild, def project, def branchfile, int defaultBuildsPassed = 15) {
        Map branchInfo = branchfile.standard_jobs_settings

        int buildsPassed = branchInfo.clean_build_cadence ?: safelyAccessClassProperty(project, 'clean_build_cadence') ?: defaultBuildsPassed

        if (buildsPassed < 0) {
            throw new IllegalArgumentException("Illegal clean_build_cadence or buildsPassed value [<0]: ${buildsPassed}")
        }

        boolean foundRecentCleanBuild = currentBuild.rawBuild.parent.builds.any { previousBuild ->
            currentBuild.number - previousBuild.number <= buildsPassed &&
                previousBuild.description != null &&
                previousBuild.description.contains('clean-build')
        }
        return foundRecentCleanBuild
    }

    /*
    * Ensures that if you try to access a possibly non-existent value in a class (e.g., a project settings file),
    * it returns null or the value. Trying to directly access a non-existent class property results in a 'Missing Method' exception.
    * @param item	A class, e.g. a project settings file.
    * @param property	The class property which is intended to be accessed safely access
    */
    // Only disable when you want a method to be able to return several different types.
    @SuppressWarnings('MethodReturnTypeRequired')
    static def safelyAccessClassProperty(def item, String property) {
        def value = item.metaClass.hasProperty(item, property)
        if (value != null) {
            value = item."$property"
        }

        return value
    }

    /**
     * When we have support for all platforms we should probably rename 'SUPPORTED_PLATFORMS'. It's
     * quite often we need to know which platforms we support and it avoids mistakes if we get them
     * programmatically instead of writing from memory.
     */
    static final List VAULT_PLATFORMS = ['linuxserver', 'ps4', 'ps5', 'server', 'win64', 'xb1', 'xbsx']

    /**
     * This function will return a list of assets that a given platform should build for preflights
     *
     * @param dataPreflightMatrix - List of data objects containing the platform name and the assets to build
     * @param platformName - Target platform
     * @return - List of assets to cook
     */
    static List<String> getDataPreflightPlatformAssets(List<Map> dataPreflightMatrix, String platformName) {
        def nodeAssets = []
        for (entry in dataPreflightMatrix) {
            if (entry.platform == platformName) {
                nodeAssets = entry.assets
            }
        }

        return nodeAssets
    }

    /**
     * This function will generate the p4 revert script
     *
     * There's a bunch of weird if statements here that are just to keep backwards compatibility.
     * I'll refactor them away later
     * @param project - project settings
     * @param branch - branch settings
     * @param code - code is synced to the agent
     * @param data - data is synced to the agent
     * @param wipePythonVenv - should we wipe the python virtual environment
     * @param p4CodeServer - P4 Code server details
     * @param p4DataServer - P4 Data server details
     * @return - Script to run
     */
    static String p4RevertScript(
        def project, def branch, boolean code = true, boolean data = true, boolean wipePythonVenv = true,
        def p4CodeServer = null, def p4DataServer = null, boolean is_cloud = false, modifiers = []
    ) {
        final WORKSPACE = is_cloud ? branch.azure_workspace_root : branch.workspace_root
        final LOG_FOLDER = "${WORKSPACE}\\logs"
        final LOG_SUFFIX = " >> ${LOG_FOLDER}\\initial_p4_code_and_data_revert.log 2>&1"
        final P4_USER_SINGLE_SLASH = is_cloud ? '%FROSTED_P4_DOMAINUSER%' : LibCommonNonCps.get_setting_value(branch, modifiers, 'p4_user_single_slash', '', project)
        final CODE_SERVER = p4CodeServer ?: LibCommonNonCps.get_setting_value(branch, modifiers, 'p4_code_server', '', project)
        final CODE_CLIENT = project.p4_code_client_env
        final CODE_P4_PREFIX = "p4.exe -p ${CODE_SERVER} -u ${P4_USER_SINGLE_SLASH} -c ${CODE_CLIENT} "

        def scriptLines = [
            '@ECHO OFF',
            "DEL /F /Q /S ${LOG_FOLDER}\\* > nul 2>&1",
            "mkdir ${LOG_FOLDER} 2> NUL",
            "START /wait taskkill /f /im python.exe${LOG_SUFFIX}",
            "START /wait taskkill /f /im fbenvconfigservice.exe${LOG_SUFFIX}",
        ]

        if (code) {
            final P4_REVERT = "${CODE_P4_PREFIX}revert -w //...${LOG_SUFFIX}"
            scriptLines.add(P4_REVERT.toString())
        }

        if (data) {
            final DATA_SERVER = p4DataServer ?: LibCommonNonCps.get_setting_value(branch, modifiers, 'p4_data_server', '', project)
            final DATA_CLIENT = project.p4_data_client_env
            final DATA_P4_PREFIX = "p4.exe -p ${DATA_SERVER} -u ${P4_USER_SINGLE_SLASH} -c ${DATA_CLIENT} "
            final DATA_P4_REVERT = "${DATA_P4_PREFIX}revert -w //...${LOG_SUFFIX}"
            scriptLines.add(DATA_P4_REVERT)
        }

        if (code) {
            if (wipePythonVenv) {
                scriptLines.add("RD /S /Q ${WORKSPACE}\\Python")
            }
            final PYTHON_PATH = "${WORKSPACE}\\TnT\\Bin\\Python"

            if (branch.wipe_python_dir in [true]) {
                scriptLines.add("${CODE_P4_PREFIX}print -m 1 -q ${WORKSPACE}\\TnT\\masterconfig.xml > NUL")
                scriptLines.add("IF NOT ERRORLEVEL 1 RD /S /Q ${PYTHON_PATH}")
                scriptLines.add("${CODE_P4_PREFIX}sync -f --parallel=threads=8 -q ${PYTHON_PATH}/...#head".toString())
            }
            if (branch.clean_python_dir in [null, true]) {
                scriptLines.add("${CODE_P4_PREFIX}print -m 1 -q ${WORKSPACE}\\TnT\\masterconfig.xml > NUL")
                scriptLines.add("IF NOT ERRORLEVEL 1 del /s /q /f ${PYTHON_PATH}\\*.pyc${LOG_SUFFIX}")
                scriptLines.add("${CODE_P4_PREFIX}clean -m ${PYTHON_PATH}/...")
            }
        }
        scriptLines.add('exit 0')
        return scriptLines.join('\n')
    }

    /**
     * This function will trigger downstream jobs from a list of jobs.
     * @param context - the pipeline job context
     * @param downstreamMatrix - list of jobs to trigger
     * @param jobType - code, data, patchdata, frosty, shift and patchfrosty are currently used
     * @param branchName - Perforce branch name
     * @param branchFile - The branch class used to access branch specific config within this method
     * @param codeChangelist - Perforce changelist for code
     * @param dataChangelist - Perforce changelist for data
     */
    static void triggerDownstreamJobs(
        context, List downstreamMatrix, String jobType, String branchName, Class branchFile, String codeChangelist = '',
        String dataChangelist = '', String combineCodeChangelist = '', String combineDataChangelist = ''
    ) {
        context.steps.echo('Triggering downstream job(s):')
        for (downstreamJob in downstreamMatrix) {
            def downstreamJobName = downstreamJob
            if (downstreamJob instanceof Map) {
                downstreamJobName = downstreamJob.name ?: downstreamJobName
                if (downstreamJobName.contains('https') && downstreamJob.creds) {
                    def downstreamJobArgs = ''
                    def creds = downstreamJob.creds
                    if (downstreamJob.args.contains('code_changelist')) {
                        downstreamJobArgs = 'code_changelist=' + codeChangelist
                        if (downstreamJob.args.contains('data_changelist')) {
                            downstreamJobArgs += '\ndata_changelist=' + dataChangelist
                        }
                    }
                    context.steps.triggerRemoteJob(
                        job: downstreamJobName,
                        blockBuildUntilComplete: false,
                        shouldNotFailBuild: true,
                        parameters: downstreamJobArgs,
                        auth: context.steps.CredentialsAuth(credentials: creds)
                    )
                } else {
                    def downstreamJobArgs = []
                    if (downstreamJob.args) {
                        if (downstreamJob.args.contains('code_changelist')) {
                            downstreamJobArgs += context.steps.string(name: 'code_changelist', value: codeChangelist)
                        }
                        if (downstreamJob.args.contains('combine_code_changelist')) {
                            downstreamJobArgs += context.steps.string(name: 'combine_code_changelist', value: combineCodeChangelist)
                        }
                        if (downstreamJob.args.contains('code_countername')) {
                            String code_countername = getp4Countername(branchName, "lkg.$jobType", branchFile, 'code')
                            downstreamJobArgs += context.steps.string(name: 'code_countername', value: code_countername)
                        }
                        if (downstreamJob.args.contains('region')) {
                            downstreamJobArgs += context.steps.string(name: 'region', value: downstreamJob.region ?: 'ww')
                        }
                        if (downstreamJob.args.contains('mirror_changelist')) {
                            downstreamJobArgs += context.steps.string(name: 'data_changelist', value: codeChangelist)
                        }
                        if (jobType in ['data', 'patchdata', 'frosty', 'patchfrosty', 'shift']) {
                            if (downstreamJob.args.contains('data_changelist')) {
                                downstreamJobArgs += context.steps.string(name: 'data_changelist', value: dataChangelist)
                            }
                            if (downstreamJob.args.contains('combine_data_changelist')) {
                                downstreamJobArgs += context.steps.string(name: 'combine_data_changelist', value: combineDataChangelist)
                            }
                            if (downstreamJob.args.contains('data_countername')) {
                                String data_countername = getp4Countername(branchName, "lkg.$jobType", branchFile, 'data')
                                downstreamJobArgs += context.steps.string(name: 'data_countername', value: data_countername)
                            }
                        }
                    }
                    if (downstreamJobName.startsWith('.')) {
                        downstreamJobName = branchName + downstreamJobName
                    }
                    context.steps.build(job: downstreamJobName, wait: false, parameters: downstreamJobArgs, propagate: false)
                }
            }
        }
    }

    static void add_downstream_freestyle_job_triggers(List freestyle_jobs, String job_name_prefix, List<Map> freestyle_job_trigger_matrix = []) {
        for (job in freestyle_jobs) {
            for (freestyle_job_trigger in freestyle_job_trigger_matrix) {
                def upstream_job_name = freestyle_job_trigger.upstream_job
                def downstream_job_name = freestyle_job_trigger.downstream_job

                if (freestyle_job_trigger.upstream_job.startsWith('.')) {
                    upstream_job_name = "${job_name_prefix}${upstream_job_name}"
                }

                if (freestyle_job_trigger.downstream_job.startsWith('.')) {
                    downstream_job_name = "${job_name_prefix}${downstream_job_name}"
                }

                if (upstream_job_name == job.name) {
                    def jobParameters = [:]
                    def creds = freestyle_job_trigger.creds

                    if (freestyle_job_trigger.args instanceof List) {
                        def requiredParams = freestyle_job_trigger.args

                        for (param in requiredParams) {
                            if (param == 'code_changelist') {
                                jobParameters['code_changelist'] = '${code_changelist}'
                            }
                            if (param == 'data_changelist') {
                                jobParameters['data_changelist'] = '${data_changelist}'
                            }
                        }
                    }

                    LibJobDsl.addAnyDownstreamJobToFreestyleJob(job, downstream_job_name, jobParameters, creds)
                }
            }
        }
    }

    static String getp4Countername(String targetBranch, String jobType, Class branchFile, String counterType) {
        /**
         * This function builds the p4 countername prioritising branch specific, then global project settings, finally applying a default.
         * @param String targetBranch - Perforce branch name on which the counter is being modified
         * @param String jobType - e.g. 'lkg.data' or 'lkg.clean.data'
         * @param def branchFile - branchfile object (from com.ea.project.GetBranchFile)
         * @param String counterType - which counter type does the name refer to (only 'code' and 'data' supported).
         */

        String p4CounternamePrefix = LibCommonNonCps.get_setting_value(branchFile.standard_jobs_settings, [], 'p4_countername_prefix', 'dice', branchFile.project)
        String cleanCounterType = counterType.toLowerCase().replace('.', '')

        // Throw on non-supported counter types
        def acceptableCounterTypes = ['code', 'data']
        if (!acceptableCounterTypes.contains(cleanCounterType)) {
            throw new CobraException("counterType ${counterType} not supported (only ${acceptableCounterTypes} supported).")
        }

        String counterName = p4CounternamePrefix + '.' + targetBranch + '.' + jobType + '.' + cleanCounterType
        counterName = counterName.replace('..', '.') // Forgive pre-dotted parameters
        return counterName
    }

    static void triggerAzureUploads(def freeStyleJob, String build_type, String branch_name, String platform, String config, def azure_uploads_matrix) {
        def supportedBuildTypes = ['code'] // update with data and frosty when we have support for those

        if (!supportedBuildTypes.contains(build_type)) {
            throw new CobraException("build_type ${build_type} not in supportedBuildTypes ${supportedBuildTypes}.")
        }

        if (!(freeStyleJob instanceof FreeStyleJob)) {
            throw new CobraException("job ${freeStyleJob} not an instance of FreeStyleJob.")
        }

        azure_uploads_matrix.each { azure_upload ->
            def isMatchingBuildType = azure_upload.build_type.equalsIgnoreCase(build_type)
            def isMatchingPlatform = azure_upload.platforms.any { it.equalsIgnoreCase(platform) }
            def isMatchingConfig = azure_upload.configs.any { it.equalsIgnoreCase(config) }

            if (isMatchingBuildType && isMatchingPlatform && isMatchingConfig) {
                String downstreamJobName = "${branch_name}.${build_type}.${platform}.${config}.copy-build-to-azure"
                LibJobDsl.addAnyDownstreamJobToFreestyleJob(freeStyleJob, downstreamJobName)
            }
        }
    }

    static void triggerAdoCodePreflights(def context, def branchFile, String preflighter, String unshelveChangelist, String baseChangelist) {
        try {
            context.steps.echo('Triggering Azure DevOps code preflight pipeline...')
            context.steps.withCredentials([context.steps.string(credentialsId: 'ado-trigger-token', variable: 'adoToken')]) {
                def encodedAdoPAT = ":${context.env.adoToken}".bytes.encodeBase64().toString()
                context.steps.wrap([$class: 'MaskPasswordsBuildWrapper', varPasswordPairs: [[password: encodedAdoPAT]]]) {
                    def curlCmd = LibCommonCps.get_ado_code_trigger_curl_cmd(context, branchFile, encodedAdoPAT, preflighter, unshelveChangelist, baseChangelist)
                    def response = context.steps.sh(script: curlCmd, returnStdout: true)
                    context.steps.echo(response)
                }
            }
        }
        catch (Exception e) {
            context.steps.echo('Failed to trigger Azure DevOps code preflight pipeline')
            context.steps.echo(e.message)
            context.steps.echo(e.toString())
        }
    }

    static void triggerAdoDataPreflights(def context, def branchFile, String preflighter, String unshelveChangelist, String baseChangelist, String codeChangelist) {
        try {
            context.steps.echo('Triggering Azure DevOps data preflight pipeline...')
            context.steps.withCredentials([context.steps.string(credentialsId: 'ado-trigger-token', variable: 'adoToken')]) {
                def encodedAdoPAT = ":${context.env.adoToken}".bytes.encodeBase64().toString()
                context.steps.wrap([$class: 'MaskPasswordsBuildWrapper', varPasswordPairs: [[password: encodedAdoPAT]]]) {
                    def curlCmd = LibCommonCps.get_ado_data_trigger_curl_cmd(context, branchFile, encodedAdoPAT, preflighter, unshelveChangelist, baseChangelist, codeChangelist)
                    def response = context.steps.sh(script: curlCmd, returnStdout: true)
                    context.steps.echo(response)
                }
            }
        }
        catch (Exception e) {
            context.steps.echo('Failed to trigger Azure DevOps data preflight pipeline')
            context.steps.echo(e.message)
            context.steps.echo(e.toString())
        }
    }

    /**
     * This method is used to configure a Jenkins job for uploading builds to Steam.
     * It sets up the job with the necessary parameters and configurations for uploading builds to Steam.
     *
     * @param currentBranch The current branch name
     * @param platformName The name of the platform
     * @param branchInfo Information about the branch
     * @param variant The build variant
     * @param isPatch Indicates if this is a patch build
     * @return A map containing the job name and steam branch information
     */
    static Map configure_steam_upload_job(Object currentBranch, String platformName, Object branchInfo, Object variant, Boolean isPatch = false) {
        def config = variant.config
        def region = variant.region
        def patchIdentifier = ''
        def combineIdentifier = ''
        def nodeLabel = 'steam_upload'
        def combineInfo = [:]

        def steamBranchInfo = branchInfo + ['node_label': nodeLabel, 'platform': platformName, 'format': variant.format, 'config': config, 'region': region]
        def combineReferenceJob = branchInfo.combine_bundles?.combine_reference_job

        if (variant.format.contains('combine')) {
            combineIdentifier = 'combine.'
            combineInfo = [
                'combine_args': '--code-combine-changelist %combine_code_changelist%' +
                    ' --data-combine-changelist %combine_data_changelist%' +
                    ' --code-combine-branch ' + combineReferenceJob.split('\\.')[0] +
                    ' --data-combine-branch ' + combineReferenceJob.split('\\.')[0],
            ]
        } else {
            combineInfo = ['combine_args': '--code-combine-branch None',]
        }

        if (isPatch) {
            patchIdentifier = 'patch.'
            combineInfo.combine_args += ' --upload-patch'
        }

        def jobName = currentBranch + '.' + platformName + '.upload_to_steam.' + patchIdentifier + combineIdentifier + variant.region + '.' + variant.config

        steamBranchInfo += combineInfo

        return ['job_name': jobName, 'steam_branch_info': steamBranchInfo]
    }
}
