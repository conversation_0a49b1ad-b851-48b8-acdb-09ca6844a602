"""
shift_utils.py

Module for holding the code which is common to all shifters and
helpers for shift operations.

Enforces shifter subclass usage api using abstract methods.
"""

from __future__ import absolute_import
from abc import abstractmethod
from builtins import str
import os
import glob
import time
import datetime
import uuid

import yaml
import re
import json
import requests
import collections
import urllib3
import shutil
import retry

from elipy2.telemetry import collect_metrics
from elipy2 import (
    build_metadata_utils,
    core,
    local_paths,
    LOGGER,
    SETTINGS,
    frostbite_core,
)
from elipy2.exceptions import ELIPYException


# pylint: disable=too-many-instance-attributes
# pylint: disable=too-many-lines
# pylint: disable=too-many-public-methods
class ShiftUtils:
    """
    This super class for ShiftUtils and variants of it enforces implementation of
    2 process_shift_upload and determine_build_location methods to simplify calling code.
    """

    def __init__(
        self,
        user,
        password,
        artifactory_api_key=None,
        artifactory_user=None,
        code_branch=None,
        code_changelist=None,
        compression=False,
        data_branch=None,
        data_changelist=None,
        re_shift=False,
        required_arguments=[],
        shift_file="submit.shift",
        shift_url="https://shift.ea.com",
        shift_retention_policy=None,
        submission_path=None,
        submission_tool=None,
        use_bilbo=True,
        use_elipy_config=False,
        use_zipped_drone_builds=False,
        build_id=None,
    ):
        LOGGER.info("Initializing {}...".format(self.__class__.__name__))
        self.artifactory_api_key = artifactory_api_key
        self.artifactory_user = artifactory_user
        self.code_branch = code_branch
        self.code_changelist = code_changelist
        self.compression = compression
        self.data_branch = data_branch or ""  # avoid os.path.join exceptions
        self.data_changelist = data_changelist or ""  # avoid os.path.join exceptions
        self.password = password
        self.re_shift = re_shift
        self.required_arguments = required_arguments
        self.shift_file = shift_file
        self.shift_url = shift_url
        self.shift_retention_policy = shift_retention_policy
        self.submission_path = submission_path
        self.submission_tool = submission_tool
        self.use_bilbo = use_bilbo
        self.use_elipy_config = use_elipy_config
        self.use_zipped_drone_builds = use_zipped_drone_builds
        self.user = user
        self.build_id = build_id

        self.verify_required_arguments_set()

        if self.submission_path is None:
            self.submission_path = SETTINGS.get("shift_submission_path")

        if use_bilbo:
            self.bilbo = build_metadata_utils.setup_metadata_manager()

    @abstractmethod
    @collect_metrics()
    def process_shift_upload(self):
        """
        Preferred method for launching shift upload proces.
        """
        raise NotImplementedError

    @abstractmethod
    def determine_build_location(self):
        """
        Public method to determine where the build to shift is
        """
        raise NotImplementedError

    def verify_required_arguments_set(self):
        """
        Checks if all required arguments are present
        """
        for arg in self.required_arguments:
            req_not_set = arg not in self.__dict__
            req_is_none = arg in self.__dict__ and self.__dict__[arg] is None
            if req_not_set or req_is_none:
                raise ELIPYException(f"Required argument '{arg}' is not set")
        return True

    def get_shift_data(
        self,
        code_changelist,
        code_branch,
        data_changelist,
        data_branch,
        platform,
        config,
        format,
        region,
        content_layer=None,
    ):
        """
        Reads and stores shift values from file
        """
        shift_data = {
            "directory": [],
            "filename": [],
            "upload_loop_filenames": [],
            "upload_loop": "",
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "buildtype": "",
            "version": "1.0",
            "platform": "",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
            "content_layer": "",
        }

        shift_config = self.get_config_data()

        if data_branch:
            parse_branch = data_branch
        else:
            parse_branch = code_branch
        parse_list = [
            parse_branch,
            platform,
            config,
            format,
            region,
        ]
        if not content_layer:
            parse_list.append("source")
            shift_data["content_layer"] = "source"
        else:
            parse_list.append(content_layer)
            shift_data["content_layer"] = content_layer

        shift_data = self.parse_config_data(shift_config, shift_data, parse_list)

        if data_changelist and data_changelist != code_changelist:
            changelist_label = code_changelist + "_" + data_changelist
        else:
            changelist_label = code_changelist
        shift_data["changelist"] = changelist_label
        shift_data["platform"] = platform

        name = "{} {} {}".format(format, config, changelist_label)

        if not shift_data["skuname"]:
            shift_data["skuname"] = name
        if not shift_data["buildname"]:
            shift_data["buildname"] = name

        return shift_data

    @staticmethod
    def get_config_data():
        """
        Reads shift config data into an ordered dict
        """
        _mapping_tag = yaml.resolver.BaseResolver.DEFAULT_MAPPING_TAG

        config_path = ShiftUtils.get_config_path()

        def dict_representer(dumper, data):
            """
            Helper function for ordered yaml load
            """
            return dumper.represent_dict(data.iteritems())

        def dict_constructor(loader, node):
            """
            Helper function for ordered yaml load
            """
            return collections.OrderedDict(loader.construct_pairs(node))

        yaml.add_representer(collections.OrderedDict, dict_representer)
        yaml.add_constructor(_mapping_tag, dict_constructor)

        with open(config_path, "r") as config_stream:
            shift_config = yaml.safe_load(config_stream)
        return shift_config

    def parse_config_data(self, config_data, shift_data, build_values):
        """
        Recursively goes through the config data and puts it into the shift data structure
        """
        build_values = [val.lower() for val in build_values]

        for key_original, value in config_data.items():
            key = key_original.lower()
            if key == "buildtype":
                shift_data["buildtype"] = value
            if key == "version":
                shift_data["version"] = value
            if key == "milestone":
                shift_data["milestone"] = value
            if key == "distribution_type":
                shift_data["distributiontype"] = value
            if key == "incremental_delivery":
                shift_data["incrementaldelivery"] = value
            if key == "retention_policy":
                shift_data["retentionpolicy"] = value
            if key == "priority":
                shift_data["priority"] = value
            if key == "sku_id":
                shift_data["skuid"] = value
            if key == "sku_name":
                shift_data["skuname"] = value
            if key == "upload_loop":
                shift_data["upload_loop"] = value
            if key == "upload_loop_filenames":
                for val in value:
                    if val != "":
                        shift_data["upload_loop_filenames"] = shift_data[
                            "upload_loop_filenames"
                        ] + [val]
            if key == "file_names":
                for val in value:
                    if val != "":
                        shift_data["filename"] = shift_data["filename"] + [val]
            if key == "supplemental_files":
                for val in value:
                    if val != "":
                        shift_data["supplementalfilepath"] = shift_data["supplementalfilepath"] + [
                            val
                        ]  # pylint: disable=line-too-long
            if key == "directory":
                for val in value:
                    if val != "":
                        shift_data["directory"] = shift_data["directory"] + [val]
            if key in build_values + ["content", "settings"]:
                shift_data = self.parse_config_data(value, shift_data, build_values)
        return shift_data

    @staticmethod
    def validate_shiftdata(shift_data, shift_dir=""):
        """
        Validates shift values set. List for specifying required and default values
        """
        # pylint: disable=too-many-statements

        mandatory_keys = ["buildname", "platform", "skuid", "skuname", "changelist"]
        shift_data_new = shift_data

        pattern = re.compile(".{8}-.{4}-.{4}-.{4}-.{12}")
        if not pattern.match(shift_data_new["skuid"]):
            raise ELIPYException("Shift: Invalid SKUID format {}".format(shift_data_new["skuid"]))

        for key in mandatory_keys:
            if key not in shift_data_new or shift_data_new[key] == "":
                raise ELIPYException(
                    "Shift: Mandatory key {}, not found in shift submit file.".format(key)
                )
        retail_build = "retail" in os.path.basename(shift_dir).lower()

        LOGGER.info("Checking for files:\n  {}".format("\n  ".join(shift_data_new["filename"])))
        if "content" in str(shift_data_new["filename"]) and "1" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " CP1"  # Labs CP 1 zip - shift only allows 50 chars
        if "content" in str(shift_data_new["filename"]) and "2" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " CP2"  # Labs CP 2 zip - shift only allows 50 chars
        if "content" in str(shift_data_new["filename"]) and "3" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " CP3"  # Labs CP3 zip - shift only allows 50 chars
        if "BattlefieldGameData" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " BG"  # Labs BG zip - shift only allows 50 chars
        if "LOREMIPSUMX" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " Main"  # PS5 BaseGame - shift only allows 50 chars
        if "BFLABSCONTENT001" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " CP1"  # Labs CP 1 pkg - shift only allows 50 chars
        if "BFLABSCONTENT002" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " CP2"  # Labs CP 2 pkg - shift only allows 50 chars
        if "BFLABSCONTENT003" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " CP3"  # Labs CP3 pkg - shift only allows 50 chars
        if "remastered" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " RM"  # remastered pkg - shift only allows 50 chars
        if "GLACIERGAME" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " Main"  # PS5 BaseGame - shift only allows 50 chars
        if "CONTENTPACK00001" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " DLC1"  # SP/MP Shared package
        if "CONTENTPACK00002" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " DLC2"  # MP package - shift only allows 50 chars
        if "CONTENTPACK00003" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " DLC3"  # SP package - shift only allows 50 chars
        if "CONTENTPACK00004" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " DLC4"  # GRA package - shift only allows 50 chars
        if "HDCONTENTPACK000" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " HDBG"  # HD Shared package - shift only allows 50 chars
        if "HDCONTENTPACK001" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " HDCP1"  # HD SP/MP Shared package
        if "HDCONTENTPACK002" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " HDCP2"  # HD MP package - shift only allows 50 chars
        if "HDCONTENTPACK003" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " HDCP3"  # HD SP package - shift only allows 50 chars
        if "HDCONTENTPACK004" in str(shift_data_new["filename"]):
            shift_data_new["buildname"] += " HDCP4"  # HD GRA package - shift only allows 50 chars
        if (
            shift_data_new.get("content_layer", "source").lower() != "source"
            and shift_data_new["content_layer"] not in shift_data_new["buildname"]
        ):
            shift_data_new["buildname"] += " " + shift_data_new["content_layer"]
        if len(shift_data_new["buildname"]) > 50:
            raise ELIPYException(
                "Build {} has too long name (> 50 char), skipping upload".format(
                    shift_data_new["buildname"]
                )
            )

        if shift_data_new["filename"]:
            for fil in shift_data_new["filename"]:
                file_path = os.path.join(os.path.join(shift_dir, "**"), fil)
                file_location = glob.glob(file_path, recursive=True)
                if file_location == []:
                    raise ELIPYException(
                        "Required file, {}, missing from build {}".format(fil, file_path)
                    )
                else:
                    shift_dir = os.path.dirname(file_location[0])
        else:
            format = shift_data_new["buildname"].split()[1]
            raise ELIPYException(
                f"No required files defined for {shift_data_new['platform']} {format}"
            )

        supfiles = []
        LOGGER.info(
            "Checking for supplemental files:\n  {}".format(
                "\n  ".join(shift_data_new["supplementalfilepath"])
            )
        )
        for fil in shift_data_new["supplementalfilepath"]:
            sup_files = [os.path.basename(i) for i in glob.glob(os.path.join(shift_dir, fil))]
            if sup_files == []:
                base_dir = os.path.dirname(shift_dir)
                sup_files = [os.path.basename(i) for i in glob.glob(os.path.join(base_dir, fil))]
                if sup_files != []:
                    LOGGER.info("Found suplemental files: {}, in {}".format(sup_files[0], base_dir))
                    LOGGER.info(
                        "Copy: {}, to {}".format(
                            os.path.join(base_dir, sup_files[0]),
                            os.path.join(shift_dir, sup_files[0]),
                        )
                    )
                    shutil.copy2(
                        os.path.join(base_dir, sup_files[0]), os.path.join(shift_dir, sup_files[0])
                    )
            if sup_files == []:
                if retail_build:
                    raise ELIPYException(
                        "Supplemental file, {}, missing from retail build".format(fil)
                    )
                else:
                    LOGGER.warning("Supplemental file, {}, missing from build".format(fil))
            else:
                supfiles = supfiles + sup_files

        shift_data_new["supplementalfilepath"] = supfiles

        return shift_data_new, shift_dir
        # pylint: enable=too-many-statements

    @staticmethod
    def create_template_file(
        shift_dir,
        shift_data,
        supplemental_dest,
        use_elipy_config=False,
        shift_template_folder="",
    ):
        """
        Creates a shift submission template file
        """
        if shift_template_folder:
            if not os.path.exists(shift_template_folder):
                os.makedirs(shift_template_folder)
            shift_template_filepath = os.path.join(shift_template_folder, "shift.template")
        else:
            shift_template_filepath = os.path.join(shift_dir, "shift.template")
        LOGGER.info("Creating shift template file {}".format(shift_template_filepath))

        if os.path.isfile(shift_template_filepath):
            os.remove(shift_template_filepath)
        pre = "shift.export."
        if not use_elipy_config:
            shift_data["buildname"] = shift_data["buildname"] + "_" + shift_data["changelist"]

        tmp_str = (
            "{0}version = {1}\n"
            + "{0}version.build.type = {2}\n"
            + "{0}perforce.depot.changelist = {3}\n"
            + "{0}platform.name = {4}\n"
            + "{0}sku.id = {5}\n"
            + "{0}sku.name = {6}\n"
            + "{0}version.name = {7}\n"
            + "{0}version.distribution.type = {8}\n"
            + "{0}version.retention.policy = {9}\n"
            + "{0}version.milestone = {10}\n"
            + "{0}version.priority.level = {11}\n"
        ).format(
            pre,
            shift_data["version"],
            shift_data["buildtype"],
            shift_data["changelist"],
            shift_data["platform"],
            shift_data["skuid"],
            shift_data["skuname"],
            shift_data["buildname"],
            shift_data["distributiontype"],
            shift_data["retentionpolicy"],
            shift_data["milestone"],
            shift_data["priority"],
        )

        if os.path.exists(supplemental_dest):
            supplemental_line = "{0}version.suplemental.folderPath = {1}\n".format(
                pre, supplemental_dest
            )
            tmp_str += supplemental_line

        with open(shift_template_filepath, "w+") as tmp:
            tmp.write(tmp_str)

        return shift_template_filepath

    @retry.retry(ELIPYException, tries=5, delay=10, backoff=2, logger=LOGGER)
    def run_upload(self, template_file, submission_dest, base_build_id=None):
        """
        Uploads a build to shift
        """
        args = "/template:{0} /user:{1} /password:{2} /source:{3} /webservice:{4}".format(
            template_file, self.user, "%SHIFT_PASSWORD%", submission_dest, self.shift_url
        )

        if base_build_id:
            args += " /diff-patch-base:{0}".format(base_build_id)

        shift_exe = self._get_shift_submission_tool()
        cmd = [shift_exe] + args.split()
        exit_code, stdout_lines, stderr_lines = core.run(
            cmd, print_std_out=True, env_patch={"SHIFT_PASSWORD": self.password}
        )

        if exit_code != 0:
            error_msg = "{0} returned exit code {1}:\n {2}".format(
                shift_exe, exit_code, "\n".join(stderr_lines)
            )
            LOGGER.error(error_msg)
            raise ELIPYException(error_msg)

        build_id_regex = (
            r"Version Id:([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})"
        )
        build_id_match = re.search(build_id_regex, "\n".join(stdout_lines))
        if build_id_match:
            return build_id_match.group(1)
        else:
            LOGGER.warning("Shift Build ID not found")
        return None

    def _upload_single_build(
        self,
        shift_dir,
        shift_file="submit.shift",
        code_changelist="",
        data_changelist="",
        code_branch="",
        data_branch="",
        use_elipy_config=False,
    ):
        """
        Uploads the build at $path to Shift.
        """

        def upload_build(shift_data, shift_dir, submission_path, fil=None):
            if "fake" not in shift_data["skuid"].lower() and shift_data["skuid"] != "":
                if fil is not None:
                    shift_data["filename"] = [fil]
                shift_data_validated, shift_dir = self.validate_shiftdata(
                    shift_data.copy(), shift_dir
                )
                guid = str(uuid.uuid4())
                submission_dest = os.path.join(submission_path, guid, "submissiondirectory")
                supplemental_dest = os.path.join(
                    self.submission_path, guid, "supplementaldirectory"
                )
                self.shift_copy(
                    shift_dir,
                    shift_data_validated,
                    submission_dest,
                    supplemental_dest,
                    fil,
                    self.compression,
                )
                template_file = self.create_template_file(
                    shift_dir,
                    shift_data_validated,
                    supplemental_dest,
                    use_elipy_config,
                )
                LOGGER.info(template_file)
                base_build_id = None

                if shift_data_validated.get("incrementaldelivery", False):
                    base_build_id = (
                        self.build_id
                        if self.build_id
                        else self.get_latest_build_id_by_sku(shift_data_validated["skuid"])
                    )

                if base_build_id is None:
                    LOGGER.info(
                        "Proceeding without a base build for SKU ID: {}. "
                        "Incremental delivery is not enabled or no base build is available.".format(
                            shift_data_validated["skuid"]
                        )
                    )
                build_id = self.run_upload(template_file, submission_dest, base_build_id)
                self._wait_until_build_available(build_id)

                if self.shift_retention_policy and build_id:
                    LOGGER.info(
                        "Setting retention policy of build {} to {}".format(
                            build_id, self.shift_retention_policy
                        )
                    )
                    response = self.set_retention_policy(build_id, self.shift_retention_policy)
                    LOGGER.info(
                        "Response from setting retention policy: {}, status_code: {}".format(
                            response.content, response.status_code
                        )
                    )
            else:
                LOGGER.warning(
                    "Build {} has an invalid SkuID, {}, skipping upload".format(
                        shift_data["skuname"], shift_data["skuid"]
                    )
                )

        LOGGER.info("Shifting build in {}".format(shift_dir))
        if use_elipy_config:
            platform, config, format, region, content_layer = self.parse_frosty_filer_path(
                shift_dir
            )
            # Sony's packaging tools for PS4 both creates an iso file and puts the pkg file that
            # is on the iso next to it. That means that if we build iso on a certain ps4 region we
            # will get a digital build for "free". Also, it's very important to use the pkg file
            # from the iso build for patching. In building patches we have thought of it in
            # vaulting and setting a baseline, but QV also needs it in Shift to quicker test
            # patches because installation of pkg is way faster than iso. Hence, what we do is that
            # if we have built ps4 iso on a branch we will automatically upload the pkg file using
            # the SKU ID for digital in Shift yaml so it's available for testing.
            formats = [format]

            if platform == "ps4" and format == "iso":
                formats.append("digital")
            if platform == "ps5" and format == "patch":
                shift_config = self.get_config_data()

                def recursive_lookup(key, _dict):
                    if key in _dict:
                        return True
                    for val in _dict.values():
                        if isinstance(val, dict):
                            return recursive_lookup(key, val)
                    return False

                if recursive_lookup("patch-remaster", shift_config[platform]):
                    formats.append("patch-remaster")

            shift_data = []

            for this_format in formats:
                shift_data.append(
                    self.get_shift_data(
                        code_changelist,
                        code_branch,
                        data_changelist,
                        data_branch,
                        platform,
                        config,
                        this_format,
                        region,
                        content_layer,
                    )
                )
        else:
            shift_data = [self.read_shift_file(shift_dir, shift_file)]
        # PS5 need to have 2 uploads per build so upload_loop_filenames is used to specify
        # the file/globs for each upload. upload_loop is used as a flag per branch to turn
        # on the upload loop
        for shift_datum in shift_data:
            if (
                len(shift_datum["upload_loop_filenames"]) > 0
                and " ".join(shift_datum["upload_loop"]).lower() == "true"
            ):
                for fil in shift_datum["upload_loop_filenames"]:
                    if "fake" not in shift_datum["skuid"].lower() and shift_datum["skuid"] != "":
                        upload_build(shift_datum, shift_dir, self.submission_path, fil)
            else:
                upload_build(shift_datum, shift_dir, self.submission_path)

        if self.use_bilbo:
            self.register_shift_in_bilbo(shift_dir)

    def _get_shift_submission_tool(self):
        """
        Returns a path to an existing ShiftSubmission.exe / ShiftSubmit.exe file
        or downloads it from artifactory if not found on disk.
        The tool will be downloaded to a version-specific folder.
        """
        tool_path = os.path.join(
            frostbite_core.get_tnt_root(),
            "build",
            "bin",
            "ShiftSubmit",
            "ShiftSubmit.exe",
        )
        tool_exists = False

        # check if shiftsubmit is present in the default path
        if self.submission_tool is None:
            tool_exists = os.path.exists(tool_path)
            if not tool_exists:
                LOGGER.warning("Default shift exe at {} does not exist.".format(tool_path))
        # check if shiftsubmit is present in the custom path
        elif os.path.exists(self.submission_tool):
            tool_path = self.submission_tool
            tool_exists = True
        else:
            LOGGER.warning("Custom shift exe at {} does not exist.".format(self.submission_tool))

        # download shiftsubmit from artifactory
        if not tool_exists:
            if self.artifactory_api_key and self.artifactory_user:
                # check if it exists on disk
                base_url = SETTINGS.get("shift_tool_url")

                # Split the URL and extract the version part
                url_parts = base_url.rstrip("/").split("/")
                if len(url_parts) >= 2 and url_parts[-2] == "shiftsubmission":
                    version = url_parts[-1]
                else:
                    raise ELIPYException(
                        "Invalid shift_tool_url format: version is missing in the URL"
                    )

                output_folder = os.path.join(
                    local_paths.get_tnt_local_path(), "shiftsubmit", version
                )
                shift_exe = os.path.join(output_folder, "ShiftSubmission.exe")
                if os.path.exists(shift_exe):
                    LOGGER.info("Found {}".format(shift_exe))
                    tool_exists = True
                    tool_path = shift_exe

                if not tool_exists:
                    # create output folder
                    if not os.path.exists(output_folder):
                        os.makedirs(output_folder)

                    base_url = SETTINGS.get("shift_tool_url")
                    # get the exe file
                    tool_path = self._download_file(base_url, "ShiftSubmission.exe", output_folder)
            else:
                raise ELIPYException("Cannot find ShiftSubmit.exe")

        return tool_path

    @staticmethod
    def read_shift_file(shift_dir, shift_file):
        """
        Reads and stores shift values from file
        """
        shift_data = {
            "directory": [],
            "filename": [],
            "supplementalfilepath": [],
            "buildname": "",
            "changelist": "",
            "version": "1.0",
            "buildtype": "",
            "platform": "",
            "skuid": "",
            "skuname": "",
            "distributiontype": "",
            "retentionpolicy": "",
            "milestone": "",
            "priority": "",
        }
        with open(os.path.join(shift_dir, shift_file), "r") as fil:
            for line in fil.readlines():
                name, var = line.partition(" ")[::2]
                name = name.strip().lower()
                if name in ["directory", "filename", "supplementalfilepath"]:
                    shift_data[name] = shift_data[name] + [var.strip()]
                else:
                    shift_data[name] = var.strip()
        return shift_data

    @staticmethod
    def parse_frosty_filer_path(filer_path):
        """
        Help function for finding config values in frosty filer path
        """
        path_parts = filer_path.split(os.sep)
        if "digital_combine" in path_parts or "patch_combine" in path_parts:
            path_parts = path_parts[:-4]
        content_layer = None
        platform = None
        config = path_parts[-1]
        region = path_parts[-2]
        format = path_parts[-3]
        component = path_parts[-4]
        if component.lower().startswith("contentlayer_"):
            content_layer = component[len("contentlayer_") :]
            platform = path_parts[-5]
        else:
            platform = component

        return platform, config, format, region, content_layer

    def find_builds(self, path, shift_file="submit.shift"):
        """
        Help function for finding all paths with shiftsubmit files
        """
        if os.path.isdir(path):
            shift_dirs = []
            for file_name in os.listdir(path):
                if "bundles" not in path:
                    if os.path.isfile(os.path.join(path, file_name)):
                        if file_name.endswith(shift_file):
                            shift_dirs.append(path)
                            break
                    elif os.path.isdir(os.path.join(path, file_name)):
                        shift_dirs = shift_dirs + self.find_builds(
                            os.path.join(path, file_name), shift_file
                        )
            return shift_dirs
        else:
            raise ELIPYException("Can't find shift builds in a non directory path, {}".format(path))

    def find_builds_bilbo(self, path):
        """
        Help function for finding all frosty builds in path on bilbo
        """
        if os.path.isdir(path):
            builds = self.bilbo.get_builds_matching(path)
            return [build.id for build in builds if "bundles" not in build.id]
        else:
            raise ELIPYException("Can't find shift builds in a non directory path, {}".format(path))

    def _reshift_check(self, shift_dirs):
        """
        Help function for filtering builds that have already been shifted
        """
        shift_dirs_to_keep = []
        for shift_dir in shift_dirs:
            if self.is_build_shifted(shift_dir):
                LOGGER.warning(
                    "Not shifting build at path: {}, since it has already been shifted".format(
                        shift_dir
                    )
                )
            else:
                shift_dirs_to_keep.append(shift_dir)
        return shift_dirs_to_keep

    def is_build_shifted(self, shift_dir):
        """
        Checks if a build has already been uploaded to shift
        """
        if self.use_bilbo:
            build = self.bilbo.get_builds_matching(shift_dir)
            for i in build:
                return "source" in dir(i) and "shift" in i.source
        else:
            return False

    @staticmethod
    def get_config_path():
        """
        Get shift config file
        """
        config_file = SETTINGS.get("shift_config_file")
        elipy_config = os.environ.get("ELIPY_CONFIG")
        config_path = os.path.join(os.path.dirname(elipy_config), config_file)
        if not os.path.isfile(config_path):
            config_path = os.path.join(frostbite_core.get_game_root(), config_path)
        return config_path

    def register_shift_in_bilbo(self, shift_dir):
        """
        Checks if a build has already been uploaded to shift
        """
        now = time.time()
        timestamp = datetime.datetime.fromtimestamp(now).strftime("%Y-%m-%d %H:%M")
        self.bilbo.register_shifted_build(shift_dir, timestamp)

    @staticmethod
    def shift_copy(
        shift_dir,
        shift_data,
        submission_dest,
        supplemental_dest,
        filename=None,
        compression=False,
    ):
        """
        Copies files to shift to local dir
        """
        args = []
        if filename is not None:
            for fil in [filename]:
                args.append(fil)
                core.robocopy(shift_dir, submission_dest, extra_args=args, include_empty_dirs=False)
                args = []
        else:
            if shift_data["filename"] != []:
                for fil in shift_data["filename"]:
                    args.append(fil)
                core.robocopy(shift_dir, submission_dest, extra_args=args, include_empty_dirs=False)
                args = []
        if shift_data["supplementalfilepath"] != []:
            for fil in shift_data["supplementalfilepath"]:
                if compression:
                    file_to_zip = os.path.join(shift_dir, fil)
                    if ShiftUtils.need_to_zip(file_to_zip):
                        file_zip = file_to_zip + ".zip"
                        core.create_zip(
                            file_to_zip,
                            file_zip,
                            additional_args=[" "],
                            keep_dir_source=True,
                        )
                        args.append(fil + ".zip")
                    else:
                        args.append(fil)
                else:
                    args.append(fil)
            core.robocopy(shift_dir, supplemental_dest, extra_args=args, single_file=True)

        for folder in shift_data["directory"]:
            core.robocopy(
                os.path.join(shift_dir, folder),
                os.path.join(submission_dest, folder),
                extra_args=[],
            )

    def get_builds_by_sku(self, sku_id, lifecycle_status="Available"):
        """
        Gets shift builds matching sku id from shift api
        """

        params = (
            '{"params": {"SKU ID": "'
            + sku_id
            + '", "Lifecycle Status": "'
            + lifecycle_status
            + '"}}'
        )  # pylint: disable=line-too-long
        function_path = "/nuxeo/site/automation/GetBuildsList_V2"

        response = self.post_to_shift_api(params=params, function_path=function_path)

        return json.loads(response.content)["value"]

    def get_latest_build_id_by_sku(self, sku_id):
        """
        Retrieves the latest build ID for a given SKU ID based on submission date.
        """
        builds = self.get_builds_by_sku(sku_id)
        if not builds:
            return None

        def parse_submission_date(build):
            date_str = build.get("Submission Date", "")
            try:
                return datetime.datetime.strptime(date_str, "%d %b %Y %I:%M %p (%Z)")
            except (ValueError, TypeError):
                return None

        valid_builds = [build for build in builds if parse_submission_date(build) is not None]

        if not valid_builds:
            return None

        sorted_builds = sorted(valid_builds, key=parse_submission_date)
        latest_build = sorted_builds[-1]

        return latest_build.get("Build ID", None)

    def set_retention_policy(self, build_id, retention_policy="Archive"):
        """
        Archives build with build id on shift
        """
        params = (
            '{"params": {"versionId": "'
            + build_id
            + '", "retentionPolicyId": "'
            + retention_policy
            + '"}}'
        )  # pylint: disable=line-too-long
        function_path = "/nuxeo/site/automation/UpdateVersionRetentionPolicy"
        return self.post_to_shift_api(params=params, function_path=function_path)

    def get_build_status(self, build_id):
        """
        Checks the status of a build on shift
        """
        params = f'{{"params": {{"Build ID": "{build_id}"}}}}'
        function_path = "/nuxeo/site/automation/GetBuildDetails"
        response = self.post_to_shift_api(params=params, function_path=function_path)
        response_json = json.loads(response.content)
        return response_json.get("value", {}).get("Lifecycle Status")

    def post_to_shift_api(self, params, function_path):
        """
        Helper function for posting to shift api
        """
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        session = requests.Session()
        session.auth = (self.user, self.password)
        url = self.shift_url + function_path

        response = session.post(
            url,
            headers={
                "content-type": "application/json",
                "Authorization": "Basic <auth token>",
            },
            data=params,
            verify=False,
        )

        if response.status_code < 200 or response.status_code >= 300:
            raise ELIPYException("{0} returned HTTP code {1}".format(url, response.status_code))

        return response

    @retry.retry(Exception, tries=3, delay=10, backoff=2, logger=LOGGER)
    def _download_file(self, base_url, file_name, output_folder, ignore_error=False):
        """
        Helper method to download a file from a given url and return the file path.
        Can ignore errors if necessary
        """
        try:
            url = "{}/{}".format(base_url, file_name)
            LOGGER.info("Downloading {}".format(url))
            response = requests.get(url, auth=(self.artifactory_user, self.artifactory_api_key))
            response.raise_for_status()
            file_path = os.path.join(output_folder, file_name)
            with open(file_path, "wb") as out_file:
                out_file.write(response.content)
        except (requests.exceptions.HTTPError, OSError) as exception:
            file_path = None
            if not ignore_error:
                raise exception

        return file_path

    @staticmethod
    def need_to_zip(fil):
        """
        Check if file need to zip or not
        """
        megabyte = 1024 * 1024
        file_size = os.path.getsize(fil)
        if fil[-4:] != ".zip" and file_size >= megabyte:
            return True
        return False

    # pylint: disable=inconsistent-return-statements
    def generate_shift_template(
        self,
        shift_dir,
        shift_data,
    ):
        """
        Generates a shift template file
        """

        if "fake" not in shift_data["skuid"].lower() and shift_data["skuid"] != "":
            shift_data, shift_dir = self.validate_shiftdata(shift_data, shift_dir)

            supplemental_dest = ""

            shift_template_folder = os.path.join(os.path.dirname(shift_dir), "template_tmp")

            shift_template_filepath = self.create_template_file(
                shift_dir,
                shift_data,
                supplemental_dest,
                self.use_elipy_config,
                shift_template_folder,
            )
            return shift_template_filepath
        else:
            LOGGER.warning(
                "Build {} has an invalid SkuID, {}, skipping upload".format(
                    shift_data["skuname"], shift_data["skuid"]
                )
            )

    def _wait_until_build_available(
        self, build_id: str, timeout: int = 600, interval: int = 10
    ) -> None:
        """
        Waits for a build to be available on shift
        """
        if not build_id:
            LOGGER.warning("Build ID not found. Skipping wait for build to become available")
            return

        start_time = time.time()
        end_time = start_time + timeout

        while time.time() < end_time:
            build_status = self.get_build_status(build_id)
            if build_status == "Available":
                LOGGER.info("Build {} is available on shift".format(build_id))
                return
            time.sleep(interval)

        LOGGER.warning(
            "Timed out waiting {}s for build {} to be available on shift".format(timeout, build_id)
        )
