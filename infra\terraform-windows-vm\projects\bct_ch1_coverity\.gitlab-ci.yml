#**********************************************
#               bct_ch1_coverity PIPE             *
#**********************************************
.default-bct-ch1-coverity-variables:
  extends: .secrets-bct_ch1_coverity
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ch1_coverity"
    WORKING_DIR: "projects/bct_ch1_coverity"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ch1-coverity.json"
    ansible_main_module: bct_ch1_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ch1_coverity.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-ch1-coverity:
  extends: ['.default-bct-ch1-coverity-variables', '.prepare_config']

validate-bct-ch1-coverity:
  extends: ['.default-bct-ch1-coverity-variables', '.validation_steps']

plan-bct-ch1-coverity:
  needs:
    - job: validate-bct-ch1-coverity
    - job: prepare-json-config-bct-ch1-coverity
  extends: ['.default-bct-ch1-coverity-variables','.plan_steps']

apply-bct-ch1-coverity:
  needs:
    - job: plan-bct-ch1-coverity
    - job: prepare-json-config-bct-ch1-coverity
  extends: ['.default-bct-ch1-coverity-variables','.apply_steps']

attache-bct-ch1-coverity:
  needs:
    - job: apply-bct-ch1-coverity
    - job: prepare-json-config-bct-ch1-coverity
  extends: ['.default-bct-ch1-coverity-variables','.attache_vmdk_step']

sync-bct-ch1-coverity:
  needs:
    - job: apply-bct-ch1-coverity
    - job: attache-bct-ch1-coverity
    - job: prepare-json-config-bct-ch1-coverity
  extends: ['.default-bct-ch1-coverity-variables','.sync_vmdk_step']

ansible-bct-ch1-coverity:
  needs:
    - job: apply-bct-ch1-coverity
    - job: sync-bct-ch1-coverity
    - job: prepare-json-config-bct-ch1-coverity
  extends: ['.default-bct-ch1-coverity-variables', '.ansible_common_secrets', '.run_ansible_step']
