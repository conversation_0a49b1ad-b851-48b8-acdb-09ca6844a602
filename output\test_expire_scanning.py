#!/usr/bin/env python3
"""
Test the expire.py scanning logic specifically for CH1-event branch
"""

import os

def _scan_frosty_builds_fixed_depth(path):
    """
    Copy of the _scan_frosty_builds_fixed_depth method from expire.py
    to test what it finds for CH1-event
    """
    builds = []

    if not os.path.exists(path):
        return builds

    try:
        # Level 1: direct subdirectories (branch names)
        level1_entries = os.listdir(path)
    except (OSError, PermissionError) as exc:
        print(f"Error scanning frosty path {path}: {exc}")
        return builds

    for branch1_entry in level1_entries:
        branch1_path = os.path.join(path, branch1_entry)
        if not os.path.isdir(branch1_path):
            continue

        print(f"Level 1 - Branch: {branch1_entry}")
        print(f"  Path: {branch1_path}")

        # Level 2: scan CL directories within each branch directory
        try:
            level2_entries = os.listdir(branch1_path)
        except (OSError, PermissionError) as exc:
            print(f"  Error scanning level 2 in {branch1_path}: {exc}")
            continue

        print(f"  Level 2 - Found {len(level2_entries)} entries")

        for cl1_entry in level2_entries:
            cl1_path = os.path.join(branch1_path, cl1_entry)
            if not os.path.isdir(cl1_path):
                print(f"    {cl1_entry} (not a directory)")
                continue

            print(f"    CL1: {cl1_entry}")

            # Level 3: scan branch directories within each CL directory
            try:
                level3_entries = os.listdir(cl1_path)
            except (OSError, PermissionError) as exc:
                print(f"      Error scanning level 3 in {cl1_path}: {exc}")
                continue

            print(f"      Level 3 - Found {len(level3_entries)} entries")

            for branch2_entry in level3_entries:
                branch2_path = os.path.join(cl1_path, branch2_entry)
                if not os.path.isdir(branch2_path):
                    print(f"        {branch2_entry} (not a directory)")
                    continue

                print(f"        Branch2: {branch2_entry}")

                # Level 4: scan final CL directories within each branch directory
                try:
                    level4_entries = os.listdir(branch2_path)
                except (OSError, PermissionError) as exc:
                    print(f"          Error scanning level 4 in {branch2_path}: {exc}")
                    continue

                print(f"          Level 4 - Found {len(level4_entries)} entries")

                for cl2_entry in level4_entries:
                    cl2_path = os.path.join(branch2_path, cl2_entry)
                    if os.path.isdir(cl2_path) and _is_cl_directory(cl2_entry):
                        builds.append(cl2_path)
                        print(f"            ✓ Build found: {cl2_path}")
                    else:
                        print(f"            {cl2_entry} (not a valid CL directory)")

        # Only process first few branches to avoid too much output
        if level1_entries.index(branch1_entry) >= 2:
            print(f"\n  ... (stopping after 3 branches)")
            break

    return builds

def _is_cl_directory(entry):
    """Check if entry looks like a CL directory (numeric with at least 7 digits)"""
    return entry.isdigit() and len(entry) >= 7

def test_expire_scanning():
    """Test the expire scanning logic"""
    path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    print("=" * 80)
    print("Testing expire.py scanning logic")
    print("=" * 80)
    print(f"Scanning path: {path}")
    print()
    
    builds = _scan_frosty_builds_fixed_depth(path)
    
    print("\n" + "=" * 80)
    print("RESULTS")
    print("=" * 80)
    print(f"Total builds found: {len(builds)}")
    
    # Group builds by branch
    branch_builds = {}
    for build in builds:
        # Extract branch name from path
        # Path format: .../BattlefieldGame/branch1/CL1/branch2/CL2
        parts = build.split(os.sep)
        if len(parts) >= 6:
            branch = parts[-5]  # branch1 (5 levels from end)
            if branch not in branch_builds:
                branch_builds[branch] = []
            branch_builds[branch].append(build)
    
    print("\nBuilds by branch:")
    for branch, builds_list in sorted(branch_builds.items()):
        print(f"  {branch}: {len(builds_list)} builds")
        if branch.upper() == "CH1-EVENT":
            print(f"    Sample builds:")
            for build in builds_list[:3]:
                print(f"      {build}")
    
    # Check specifically for CH1-event
    ch1_event_builds = branch_builds.get("CH1-event", [])
    print(f"\nCH1-event specific results:")
    print(f"  Builds found: {len(ch1_event_builds)}")
    
    if ch1_event_builds:
        print("  ✓ CH1-event builds were found by expire scanning")
    else:
        print("  ✗ CH1-event builds were NOT found by expire scanning")
        
        # Check if the directory exists but has issues
        ch1_event_path = os.path.join(path, "CH1-event")
        if os.path.exists(ch1_event_path):
            print(f"  CH1-event directory exists at: {ch1_event_path}")
            try:
                items = os.listdir(ch1_event_path)
                print(f"  Contains {len(items)} items")
                
                # Check first few items
                for item in items[:3]:
                    item_path = os.path.join(ch1_event_path, item)
                    if os.path.isdir(item_path):
                        print(f"    Directory: {item}")
                        try:
                            subitems = os.listdir(item_path)
                            print(f"      Contains {len(subitems)} subitems")
                        except (OSError, PermissionError) as exc:
                            print(f"      Error reading: {exc}")
                    else:
                        print(f"    File: {item}")
                        
            except (OSError, PermissionError) as exc:
                print(f"  Error reading CH1-event directory: {exc}")
        else:
            print("  CH1-event directory does not exist")

if __name__ == "__main__":
    test_expire_scanning()
