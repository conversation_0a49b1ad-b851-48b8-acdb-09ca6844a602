#**********************************************
#          KINGSTON PS AUTOTEST PIPE          *
#**********************************************
.default-kin-ps-autotest-variables:
  extends: .secrets-kingston_ps_autotest
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "kingston_ps_autotest"
    WORKING_DIR: "projects/kingston_ps_autotest"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-kin-ps-autotest.json"
    ansible_main_module: kingston_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "kingston_ps.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-kin-ps-autotest:
  extends: ['.default-kin-ps-autotest-variables', '.prepare_config']

validate-kin-ps-autotest:
  extends: ['.default-kin-ps-autotest-variables', '.validation_steps']

plan-kin-ps-autotest:
  needs:
    - job: validate-kin-ps-autotest
    - job: prepare-json-config-kin-ps-autotest
  extends: ['.default-kin-ps-autotest-variables','.plan_steps']

apply-kin-ps-autotest:
  needs:
    - job: plan-kin-ps-autotest
    - job: prepare-json-config-kin-ps-autotest
  extends: ['.default-kin-ps-autotest-variables','.apply_steps']

attache-kin-ps-autotest:
  needs:
    - job: apply-kin-ps-autotest
    - job: prepare-json-config-kin-ps-autotest
  extends: ['.default-kin-ps-autotest-variables','.attache_vmdk_step']

sync-kin-ps-autotest:
  needs:
    - job: apply-kin-ps-autotest
    - job: attache-kin-ps-autotest
    - job: prepare-json-config-kin-ps-autotest
  extends: ['.default-kin-ps-autotest-variables','.sync_vmdk_step']

ansible-kin-ps-autotest:
  needs:
    - job: apply-kin-ps-autotest
    - job: sync-kin-ps-autotest
    - job: prepare-json-config-kin-ps-autotest
  extends: ['.default-kin-ps-autotest-variables', '.ansible_common_secrets', '.run_ansible_step']
