#**********************************************
#                  bct_ps02 PIPE
#**********************************************
.default-bct-ps02-variables:
  extends: .secrets-bct_ps02
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ps02"
    WORKING_DIR: "projects/bct_ps02"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_PS02.json"

prepare-json-config-bct-ps02:
  extends: ['.default-bct-ps02-variables', '.prepare_config']

validate-bct-ps02:
  extends: ['.default-bct-ps02-variables', '.validation_steps']

plan-bct-ps02:
  needs:
    - job: validate-bct-ps02
    - job: prepare-json-config-bct-ps02
  extends: ['.default-bct-ps02-variables', '.plan_steps']

apply-bct-ps02:
  needs:
    - job: plan-bct-ps02
    - job: prepare-json-config-bct-ps02
  extends: ['.default-bct-ps02-variables', '.apply_steps']

attache-bct-ps02:
  needs:
    - job: apply-bct-ps02
    - job: prepare-json-config-bct-ps02
  extends: ['.default-bct-ps02-variables', '.attache_vmdk_step']

sync-bct-ps02:
  needs:
    - job: apply-bct-ps02
    - job: attache-bct-ps02
    - job: prepare-json-config-bct-ps02
  extends: ['.default-bct-ps02-variables', '.sync_vmdk_step']

ansible-bct-ps02:
  needs:
    - job: apply-bct-ps02
    - job: sync-bct-ps02
    - job: prepare-json-config-bct-ps02
  extends: ['.default-bct-ps02-variables', '.ansible_common_secrets', '.run_ansible_step']
