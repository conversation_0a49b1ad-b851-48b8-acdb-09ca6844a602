# *************************************************************
#  Sets up the initial needs to point to our vSphere server
# *************************************************************
# Point to our datacenter
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state legacy artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}

locals {
  module_settings = {
    # DELETER VMS
    "bct_criterion_deleter_001" = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "bct_criterion deleter", role = "https://dice-build-jenkins.cobra.dre.ea.com" }
    "bct_criterion_deleter_002" = { datastore = "OH-PSTORE01_3", vm_count = "1", labels = "bct_criterion deleter", role = "https://dice-build-jenkins.cobra.dre.ea.com" }

    # TEST VMS
    "bct_12_core_criterion_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "12_core criterion xbsx ps5 win64", cores_per_socket = "12", cpu_core = "12", ram_count = "131072", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
    "bct_18_core_criterion_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "18_core criterion xbsx ps5 win64", cores_per_socket = "9", cpu_core = "18", ram_count = "131072", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
    "bct_24_core_criterion_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "24_core criterion xbsx ps5 win64", cores_per_socket = "12", cpu_core = "24", ram_count = "131072", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }

    # BUILD WARM VMS
    "bct_poolbuild_criterion_win64_001"  = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "poolbuild_criterion win64 win64game", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
    "bct_poolbuild_criterion_xbsx_001"   = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "poolbuild_criterion xbsx", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
    "bct_poolbuild_criterion_ps5_001"    = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "poolbuild_criterion ps5", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
    "bct_poolbuild_criterion_linux_001"  = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "poolbuild_criterion linux64", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
    "bct_poolbuild_criterion_server_001" = { datastore = "OH-PSTORE02_7", vm_count = "1", labels = "poolbuild_criterion server linux64server linuxserver win64server", role = "https://bct-dev-jenkins.cobra.dre.ea.com" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore    = each.value.datastore
  vm_count             = each.value.vm_count
  vm_prefix            = try(each.value.vm_prefix, "bct1-")
  jenkins_slave_labels = each.value.labels
  role                 = each.value.role
  ram_count            = try(each.value.ram_count, 65536)
  cpu_core             = try(each.value.cpu_core, "")
  cores_per_socket     = try(each.value.cores_per_socket, "")

  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")
  vsphere_template        = try(each.value.windows_template, var.packer_template)
  vsphere_network         = var.network
  vsphere_datacenter      = var.datacenter
  vsphere_compute_cluster = var.compute_cluster
  vsphere_folder          = var.vsphere_location
  domain_admin            = var.domain_admin
  domain_admin_password   = var.domain_password
  local_admin_user        = var.local_username
  local_admin_password    = var.local_password
  local_admin_group       = var.local_admin_group
  project_dir             = var.project_dir
  project_name            = var.project_name
  commit_sha              = var.commit_sha
  commit_user             = var.commit_user
  commit_url              = var.commit_url
  disk_size               = var.disk_size
  domain_name             = var.domain_name
  domain_ou               = var.domain_ou
  hardware_version        = var.hardware_version
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
