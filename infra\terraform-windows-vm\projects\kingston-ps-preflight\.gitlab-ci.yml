#**********************************************
#               KINGSTON-PREFLIGHT-PS PIPE
#**********************************************
.default-kingston-ps-preflight-variables:
  extends: .secrets-kingston-ps-preflight
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "kingston-ps-preflight"
    WORKING_DIR: "projects/kingston-ps-preflight"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-kingston-ps-preflight.json"
    ansible_main_module: kingston_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "kingston_ps02.json"
    ANSIBLE_BRANCH: "COBRA-100-test-higher-parallelism"

prepare-json-config-kingston-ps-preflight:
  extends: ['.default-kingston-ps-preflight-variables', '.prepare_config']

validate-kingston-ps-preflight:
  extends: ['.default-kingston-ps-preflight-variables', '.validation_steps']

plan-kingston-ps-preflight:
  needs:
    - job: validate-kingston-ps-preflight
    - job: prepare-json-config-kingston-ps-preflight
  extends: ['.default-kingston-ps-preflight-variables','.plan_steps']

apply-kingston-ps-preflight:
  needs:
    - job: plan-kingston-ps-preflight
    - job: prepare-json-config-kingston-ps-preflight
  extends: ['.default-kingston-ps-preflight-variables','.apply_steps']

attache-kingston-ps-preflight:
  needs:
    - job: apply-kingston-ps-preflight
    - job: prepare-json-config-kingston-ps-preflight
  extends: ['.default-kingston-ps-preflight-variables','.attache_vmdk_step']

sync-kingston-ps-preflight:
  needs:
    - job: apply-kingston-ps-preflight
    - job: attache-kingston-ps-preflight
    - job: prepare-json-config-kingston-ps-preflight
  extends: ['.default-kingston-ps-preflight-variables','.sync_vmdk_step']

ansible-kingston-ps-preflight:
  needs:
    - job: apply-kingston-ps-preflight
    - job: sync-kingston-ps-preflight
    - job: prepare-json-config-kingston-ps-preflight
  extends: ['.default-kingston-ps-preflight-variables', '.ansible_common_secrets', '.run_ansible_step']
  tags:
    - cobra-runner
