#**********************************************
#                  Snowcache State PIPE
#**********************************************
.default-snowcache-troy-criterion-variables:
  extends: .secrets-snowcache_troy_criterion
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "snowcache_troy_criterion"
    WORKING_DIR: "projects/snowcache_troy_criterion"
    VC_HOST: "oh-vcenter1.ad.ea.com"
    NODE_INFO_FILE: "node-info-snowcache-criterion.json"
    ansible_main_module: avalanche_snowcache
    # TF_LOG: "DEBUG"
    # TF_LOG_PATH: $CI_PROJECT_DIR/debug.log
    SILVERBACK_CONFIG_JSON_FILE: "snowcache_troy_criterion.json"

prepare-json-config-snowcache-troy-criterion:
  extends: ['.default-snowcache-troy-criterion-variables', '.prepare_config']

validate-snowcache-troy-criterion:
  extends: ['.default-snowcache-troy-criterion-variables', '.validation_steps']

plan-snowcache-troy-criterion:
  needs:
    - job: validate-snowcache-troy-criterion
    - job: prepare-json-config-snowcache-troy-criterion
  extends: ['.default-snowcache-troy-criterion-variables', '.plan_steps']

apply-snowcache-troy-criterion:
  needs:
    - job: plan-snowcache-troy-criterion
    - job: prepare-json-config-snowcache-troy-criterion
  extends: ['.default-snowcache-troy-criterion-variables', '.apply_steps']

ansible-snowcache-troy-criterion:
  needs:
    - job: apply-snowcache-troy-criterion
    - job: prepare-json-config-snowcache-troy-criterion
  extends: ['.default-snowcache-troy-criterion-variables', '.ansible_common_secrets', '.run_ansible_step']

