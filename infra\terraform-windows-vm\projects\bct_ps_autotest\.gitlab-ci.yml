#**********************************************
#          BCT PS AUTOTEST PIPE          *
#**********************************************
.default-bct-ps-autotest-variables:
  extends: .secrets-bct_ps_autotest
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ps_autotest"
    WORKING_DIR: "projects/bct_ps_autotest"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ps-autotest.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_PS.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-ps-autotest:
  extends: ['.default-bct-ps-autotest-variables', '.prepare_config']

validate-bct-ps-autotest:
  extends: ['.default-bct-ps-autotest-variables', '.validation_steps']

plan-bct-ps-autotest:
  needs:
    - job: validate-bct-ps-autotest
    - job: prepare-json-config-bct-ps-autotest
  extends: ['.default-bct-ps-autotest-variables','.plan_steps']

apply-bct-ps-autotest:
  needs:
    - job: plan-bct-ps-autotest
    - job: prepare-json-config-bct-ps-autotest
  extends: ['.default-bct-ps-autotest-variables','.apply_steps']

attache-bct-ps-autotest:
  needs:
    - job: apply-bct-ps-autotest
    - job: prepare-json-config-bct-ps-autotest
  extends: ['.default-bct-ps-autotest-variables','.attache_vmdk_step']

sync-bct-ps-autotest:
  needs:
    - job: apply-bct-ps-autotest
    - job: attache-bct-ps-autotest
    - job: prepare-json-config-bct-ps-autotest
  extends: ['.default-bct-ps-autotest-variables','.sync_vmdk_step']

ansible-bct-ps-autotest:
  needs:
    - job: apply-bct-ps-autotest
    - job: sync-bct-ps-autotest
    - job: prepare-json-config-bct-ps-autotest
  extends: ['.default-bct-ps-autotest-variables', '.ansible_common_secrets', '.run_ansible_step']
