#!/usr/bin/env python3
"""
Simple test script to verify the frosty build scanning logic without dependencies
"""

import os

def _is_cl_directory(entry):
    """Check if entry looks like a CL directory (numeric with at least 7 digits)"""
    return entry.isdigit() and len(entry) >= 7

def _scan_frosty_branch_builds(branch_path):
    """<PERSON>an builds for a specific frosty branch
    
    Starting from a branch path (e.g., frosty/BattlefieldGame/CH1-event):
    - Level 2: CL directories (24269306, etc.)
    - Level 3: Branch directories again (CH1-event, etc.)
    - Level 4: Final CL directories (24269306, etc.) - these are the actual builds
    """
    builds = []
    
    try:
        # Level 2: scan CL directories within the branch directory
        level2_entries = os.listdir(branch_path)
        print(f"    Level 2 entries: {len(level2_entries)}")
    except (OSError, PermissionError) as exc:
        print(f"    Error scanning level 2 in {branch_path}: {exc}")
        return builds

    for cl1_entry in level2_entries:
        cl1_path = os.path.join(branch_path, cl1_entry)
        if not os.path.isdir(cl1_path):
            continue

        print(f"      CL1: {cl1_entry}")

        # Level 3: scan branch directories within each CL directory
        try:
            level3_entries = os.listdir(cl1_path)
            print(f"        Level 3 entries: {len(level3_entries)}")
        except (OSError, PermissionError) as exc:
            print(f"        Error scanning level 3 in {cl1_path}: {exc}")
            continue

        for branch2_entry in level3_entries:
            branch2_path = os.path.join(cl1_path, branch2_entry)
            if not os.path.isdir(branch2_path):
                continue

            print(f"          Branch2: {branch2_entry}")

            # Level 4: scan final CL directories within each branch directory
            try:
                level4_entries = os.listdir(branch2_path)
                print(f"            Level 4 entries: {len(level4_entries)}")
            except (OSError, PermissionError) as exc:
                print(f"            Error scanning level 4 in {branch2_path}: {exc}")
                continue

            for cl2_entry in level4_entries:
                cl2_path = os.path.join(branch2_path, cl2_entry)
                if os.path.isdir(cl2_path) and _is_cl_directory(cl2_entry):
                    builds.append(cl2_path)
                    print(f"              ✅ Build: {cl2_entry}")
                    
        # Only process first few CL1 directories to avoid too much output
        if level2_entries.index(cl1_entry) >= 2:
            print(f"      ... (stopping after 3 CL1 directories)")
            break
                    
    return builds

def test_frosty_branch_scanning():
    """Test the frosty branch scanning logic"""
    
    print("=" * 80)
    print("Testing Frosty Branch Scanning Logic")
    print("=" * 80)
    
    # Test CH1-event path
    ch1_event_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event"
    
    print(f"Testing path: {ch1_event_path}")
    print(f"Path exists: {os.path.exists(ch1_event_path)}")
    print()
    
    if not os.path.exists(ch1_event_path):
        print("❌ Path does not exist - cannot test")
        return
    
    # Test branch detection logic
    print("Testing branch detection logic:")
    try:
        entries = os.listdir(ch1_event_path)
        has_cl_dirs = any(
            entry.isdigit() and len(entry) >= 7 and os.path.isdir(os.path.join(ch1_event_path, entry))
            for entry in entries
        )
        print(f"  Directory entries: {len(entries)}")
        print(f"  Has CL directories: {has_cl_dirs}")
        print(f"  Should be detected as branch: {has_cl_dirs}")
    except (OSError, PermissionError) as exc:
        print(f"  Error reading directory: {exc}")
        return
    
    print()
    
    # Test the scanning logic
    print("Testing build scanning:")
    builds = _scan_frosty_branch_builds(ch1_event_path)
    
    print()
    print("=" * 80)
    print("Results")
    print("=" * 80)
    print(f"Total builds found: {len(builds)}")
    
    if builds:
        print("✅ SUCCESS: Builds found in CH1-event")
        print("Sample builds:")
        for build in builds[:5]:
            print(f"  {build}")
        if len(builds) > 5:
            print(f"  ... and {len(builds) - 5} more")
    else:
        print("❌ FAILURE: No builds found in CH1-event")
        
    print()
    print("Expected result: This should find the same builds that our earlier")
    print("investigation showed (34 CL1 directories with builds in each)")

def test_path_detection():
    """Test the path detection logic"""
    
    print("\n" + "=" * 80)
    print("Testing Path Detection Logic")
    print("=" * 80)
    
    test_paths = [
        (r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame", "Parent path", False),
        (r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event", "CH1-event path", True),
        (r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-qol", "CH1-qol path", True),
    ]
    
    for test_path, description, expected_is_branch in test_paths:
        print(f"\nTesting {description}:")
        print(f"  Path: {test_path}")
        print(f"  Exists: {os.path.exists(test_path)}")
        
        if not os.path.exists(test_path):
            print(f"  Skipping - path does not exist")
            continue
            
        # Check if path contains CL directories (branch detection logic)
        try:
            entries = os.listdir(test_path)
            has_cl_dirs = any(
                entry.isdigit() and len(entry) >= 7 and os.path.isdir(os.path.join(test_path, entry))
                for entry in entries
            )
            
            print(f"  Has CL directories: {has_cl_dirs}")
            print(f"  Expected to be branch: {expected_is_branch}")
            
            if has_cl_dirs == expected_is_branch:
                print(f"  ✅ Correct detection")
            else:
                print(f"  ❌ Incorrect detection")
                
        except (OSError, PermissionError) as exc:
            print(f"  Error reading directory: {exc}")

if __name__ == "__main__":
    test_frosty_branch_scanning()
    test_path_detection()
