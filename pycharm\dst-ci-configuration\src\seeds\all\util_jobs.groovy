// codenarc-disable UnnecessaryObjectReferences
package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.LibSlack
import com.ea.lib.jobs.LibBilbo
import com.ea.lib.jobs.LibCode
import com.ea.lib.jobs.LibCodeCoverage
import com.ea.lib.jobs.LibCustomTests
import com.ea.lib.jobs.LibData
import com.ea.lib.jobs.LibDatasnooper
import com.ea.lib.jobs.LibFrosty
import com.ea.lib.jobs.LibIntegration
import com.ea.lib.jobs.LibMapTool
import com.ea.lib.jobs.LibNavmesh
import com.ea.lib.jobs.LibOutsourcer
import com.ea.lib.jobs.LibPipelineWarning
import com.ea.lib.jobs.LibRemoteTriggers
import com.ea.lib.jobs.LibShift
import com.ea.lib.jobs.LibSparta
import com.ea.lib.jobs.LibStatus
import com.ea.lib.jobs.LibStoreBaselines
import com.ea.lib.jobs.LibWebexport
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.branches

    branches.each { current_branch, info ->
        out.println("   Processing branch: $current_branch")
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = info.project
        }
        def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
        def code_matrix = branchfile.code_matrix
        def bilbo_move_matrix = branchfile.metaClass.hasProperty(branchfile, 'bilbo_move_matrix') ? branchfile.bilbo_move_matrix : []
        def shift_upload_matrix = branchfile.shift_upload_matrix
        def general_settings = branchfile.general_settings
        def standard_jobs_settings = branchfile.standard_jobs_settings
        def branch_info = info + general_settings + standard_jobs_settings + [branch_name: current_branch]
        def freestyle_jobs = []
        String pipelineLogCodeBranch = branch_info.non_virtual_code_branch ?: branch_info.code_branch

        if (branch_info.prebuild_info != null) {
            out.println('      Processing prebuild_info')
            def prebuild_start = pipelineJob(current_branch + '.prebuild.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/prebuild_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibOutsourcer.prebuild_start(prebuild_start, project, branch_info)
        }

        if (branch_info.outsource_package_info != null) {
            out.println('      Processing outsourcePackageStart')
            def outsourcePackageStart = pipelineJob(current_branch + '.outsource-package.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/outsource_package_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibOutsourcer.outsourcePackageStart(outsourcePackageStart, project, branch_info)
        }

        if (branch_info.codecoverage_args != null) {
            out.println('      Processing codecoverage_args')
            def codecoverage_start = pipelineJob(current_branch + '.CodeCoverage.start') {
                //e.g dev-na-battlefieldgame.CodeCoverage.start is the upstream job
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/codecoverage.groovy'))
                        sandbox(true)
                    }
                }
            }
            //settings for '.CodeCoverage.start' job, will be used for src/scripts/schedulers/codecoverage.groovy
            LibCodeCoverage.codecoverage_start(codecoverage_start, project, branchfile, masterSettings, current_branch as String)

            //create downstream job doing real job
            def codecoverage = job(current_branch + '.CodeCoverage') {}
            freestyle_jobs.add(codecoverage)
            LibScm.codecoverage_sync_all(codecoverage, project, branch_info, '*****************:ease-qe/Scripts.git', '${code_changelist}', './autotest')
            LibCodeCoverage.run_codecoverage(codecoverage, project, branchfile, masterSettings, current_branch as String)
            LibJobDsl.addVaultSecrets(codecoverage, branch_info)
            LibJobDsl.archive_non_build_logs(codecoverage, branch_info)
            LibJobDsl.postclean_silverback(codecoverage, project, branch_info)
        }

        if (branch_info.navmesh == true) {
            out.println('      Processing navmesh')
            def navmesh_start = pipelineJob(current_branch + '.navmesh.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/navmesh_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibNavmesh.navmesh_start(navmesh_start, project, branchfile, masterSettings, current_branch as String)
        }

        if (branch_info.datasnooper == true) {
            out.println('      Processing datasnooper')
            def datasnooper_start = pipelineJob(current_branch + '.datasnooper.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/datasnooper_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibDatasnooper.datasnooper_start(datasnooper_start, project, branchfile, masterSettings, current_branch as String)
        }

        if (branch_info.sparta_branch == true) {
            out.println('      Processing sparta_branch')
            def sparta_start = pipelineJob(current_branch + '.sparta.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/sparta_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibSparta.sparta_start(sparta_start, project, branchfile, masterSettings, current_branch as String)
        }

        if (branch_info.produce_build_status == true) {
            out.println('      Processing produce_build_status')
            def produce_build_status_start = pipelineJob(current_branch + '.produce-build-status.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/produce_build_status.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibStatus.produce_build_status_start(produce_build_status_start, project, branchfile, masterSettings, current_branch as String)
        }

        if (branch_info.pipeline_warning_job == true) {
            out.println('      Processing pipeline_warning_job')
            def pipeline_warning_start = pipelineJob(current_branch + '.pipeline-warning.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/pipeline_warning_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibPipelineWarning.pipeline_warning_start(pipeline_warning_start, project, branchfile, masterSettings, current_branch as String)
        }

        if (branch_info.custom_tests) {
            out.println('      Processing custom-tests.start')
            def customTestsStart = pipelineJob(current_branch + '.custom-tests.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/custom_tests_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCustomTests.customTestsStart(customTestsStart, project, branchfile, masterSettings, current_branch as String)
        }

        if (branch_info.shift_drone_builds_external_location) {
            out.println('      Processing shift_drone_builds_external_location')
            def purge_shift_external_supplementfiles = job('external-job.purge-shift-supplement-files') {
                logRotator(daysToKeep = 7, numToKeep = 50)
                description('Runs script to purge supplement files in shift upload')
                label('util')
                environmentVariables {
                    env('Source', branch_info.shift_drone_builds_external_location)
                }
                wrappers {
                    timestamps()
                    timeout {
                        absolute(30) // in minutes
                        abortBuild()
                        writeDescription('Build aborted due to timeout after {0} minutes')
                    }
                }
                triggers {
                    cron('H/5 * * * 1-6\nH/5 6-23 * * 7')
                }
                steps {
                    batchFile('powershell.exe -NonInteractive ci\\resources\\content_purgeItems.ps1')
                }
            }
            freestyle_jobs.add(purge_shift_external_supplementfiles)
            LibScm.git_ci(purge_shift_external_supplementfiles)
        }

        if (branch_info.prebuild_info != null) {
            out.println('      Processing prebuild_info')
            def prebuild_job = job(current_branch + '.prebuild') {}
            freestyle_jobs.add(prebuild_job)
            LibOutsourcer.prebuild_job(prebuild_job, project, branch_info)
            LibScm.sync_prebuild(prebuild_job, project, branch_info, '${code_changelist}')
            LibJobDsl.kill_processes(prebuild_job, branch_info)
            LibJobDsl.initialP4revert(prebuild_job, project, branch_info, true, false)
            LibJobDsl.addVaultSecrets(prebuild_job, branch_info)
            LibJobDsl.archive_non_build_logs(prebuild_job, branch_info)
            LibJobDsl.postclean_silverback(prebuild_job, project, branch_info)
            if (branch_info.prebuild_info.outsource_validation) {
                def outsourcevalidation_job = job(current_branch + '.outsourcevalidation') {}
                freestyle_jobs.add(outsourcevalidation_job)
                LibOutsourcer.outsource_validation(outsourcevalidation_job, project, branch_info)
                LibScm.sync_prebuild(outsourcevalidation_job, project, branch_info, '${code_changelist}')
                LibJobDsl.kill_processes(outsourcevalidation_job, branch_info)
                LibJobDsl.initialP4revert(outsourcevalidation_job, project, branch_info, true, false)
                LibJobDsl.addVaultSecrets(outsourcevalidation_job, branch_info)
                LibJobDsl.archive_non_build_logs(outsourcevalidation_job, branch_info)
                LibJobDsl.postclean_silverback(outsourcevalidation_job, project, branch_info)
            }
        }

        if (branch_info.outsource_package_info != null) {
            out.println('      Processing outsourcePackageBuild')
            def outsourcePackageBuild = job(current_branch + '.outsource-package.build') {}
            freestyle_jobs.add(outsourcePackageBuild)
            LibOutsourcer.outsourcePackageBuild(outsourcePackageBuild, project, branch_info)
            LibScm.sync_outsource_package(outsourcePackageBuild, project, branch_info, '${code_changelist}')
            LibJobDsl.kill_processes(outsourcePackageBuild, branch_info)
            LibJobDsl.initialP4revert(outsourcePackageBuild, project, branch_info, true, false)
            LibJobDsl.addVaultSecrets(outsourcePackageBuild, branch_info)
            LibJobDsl.archive_non_build_logs(outsourcePackageBuild, branch_info)
            LibJobDsl.postclean_silverback(outsourcePackageBuild, project, branch_info)
        }

        if (branch_info.produce_build_status == true) {
            out.println('      Processing produce_build_status')
            def produce_build_status = job(current_branch + '.produce-build-status') {}
            freestyle_jobs.add(produce_build_status)
            LibStatus.produce_build_status_job(produce_build_status, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(produce_build_status, project, branch_info)
            LibJobDsl.kill_processes(produce_build_status, branch_info)
            LibJobDsl.initialP4revert(produce_build_status, project, branch_info)
            LibJobDsl.addVaultSecrets(produce_build_status, branch_info)
            LibJobDsl.archive_non_build_logs(produce_build_status, branch_info)
            LibJobDsl.postclean_silverback(produce_build_status, project, branch_info)
        }

        if (branch_info.webexport_branch == true) {
            out.println('      Processing webexport_branch')
            def webexport_job = job(current_branch + '.' + branch_info.dataset + '.webexport.win64') {}
            freestyle_jobs.add(webexport_job)
            LibWebexport.webexport_job(webexport_job, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(webexport_job, project, branch_info, '${data_changelist}', '${code_changelist}')
            LibJobDsl.with {
                kill_processes(webexport_job, branch_info)
                initialP4revert(webexport_job, project, branch_info)
                addVaultSecrets(webexport_job, branch_info)
                archive_pipelinelog(webexport_job, branch_info.dataset, pipelineLogCodeBranch)
                archive_non_build_logs(webexport_job, branch_info)
                postclean_silverback(webexport_job, project, branch_info)
            }
        }

        if (branch_info.upgrade_data_job == true) {
            out.println('      Processing upgrade_data_job')
            def upgrade_data_job = job(current_branch + '.' + branch_info.dataset + '.upgrade.data') {}
            freestyle_jobs.add(upgrade_data_job)
            LibData.upgrade_data(upgrade_data_job, project, branch_info)
            LibScm.sync_code_and_data(upgrade_data_job, project, branch_info, '', '${code_changelist}')
            LibJobDsl.with {
                kill_processes(upgrade_data_job, branch_info)
                initialP4revert(upgrade_data_job, project, branch_info)
                addVaultSecrets(upgrade_data_job, branch_info)
                archive_pipelinelog(upgrade_data_job, branch_info.dataset, pipelineLogCodeBranch)
                archive_non_build_logs(upgrade_data_job, branch_info)
                postclean_silverback(upgrade_data_job, project, branch_info)
            }
        }

        if (branch_info.single_stream_smoke == true) {
            out.println('      Processing single_stream_smoke')
            def register_smoke_job = job(current_branch + '.register.smoke') {}
            freestyle_jobs.add(register_smoke_job)
            LibBilbo.bilbo_register_smoke(register_smoke_job, project, branchfile, masterSettings, current_branch as String)
            LibJobDsl.initialP4revert(register_smoke_job, project, branch_info, true, false)
            LibJobDsl.addVaultSecrets(register_smoke_job, branch_info)
            LibJobDsl.postclean_silverback(register_smoke_job, project, branch_info)
            LibSlack.slack_default(register_smoke_job, '#cobra-outage-bilbo', project.short_name as String, false)

            def set_integration_changelist_job = pipelineJob(current_branch + '.set_integration_changelist') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/integration_changelist_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibIntegration.set_integration_changelist_job(set_integration_changelist_job, branch_info)
        }

        if (branch_info.dry_run_data != true) {
            out.println('      Processing dry_run_data')
            def bilbo_drone_job_name = current_branch + '.bilbo.register'
            bilbo_drone_job_name += branch_info.move_location_parallel ? '.local' : '-' + branch_info.dataset + '-dronebuild'
            List drone_outsourcers = branch_info.drone_outsourcers ?: []
            def bilbo_drone_job = job(bilbo_drone_job_name) {}
            freestyle_jobs.add(bilbo_drone_job)
            LibBilbo.bilbo_drone_job(bilbo_drone_job, project, branchfile, masterSettings, current_branch as String)
            LibJobDsl.kill_processes(bilbo_drone_job, branch_info)
            LibJobDsl.initialP4revert(bilbo_drone_job, project, branch_info)
            LibJobDsl.addVaultSecrets(bilbo_drone_job, branch_info)
            LibJobDsl.archive_non_build_logs(bilbo_drone_job, branch_info)
            LibJobDsl.postclean_silverback(bilbo_drone_job, project, branch_info)
            if (branch_info.move_location_parallel != true) {
                LibSlack.slack_default(bilbo_drone_job, '#cobra-outage-bilbo', project.short_name as String, false)
            }
            if (branch_info.offsite_drone_builds == true) {
                LibJobDsl.curl_drone_builds(bilbo_drone_job, branch_info)
            }
            if (branch_info.bilbo_store_offsite || branch_info.use_zipped_drone_builds) {
                LibJobDsl.store_offsite_zip(bilbo_drone_job, branch_info)
            }
            if (branch_info.offsite_drone_basic_builds == true) {
                LibJobDsl.store_offsite_zip(bilbo_drone_job, branch_info, true)
            }
            if (branch_info.offsite_basic_drone_zip_builds == true) {
                LibJobDsl.store_offsite_zip(bilbo_drone_job, branch_info, false, true)
            }
            if (drone_outsourcers) {
                LibJobDsl.store_offsite_zip(bilbo_drone_job, branch_info, false, false, drone_outsourcers)
            }
            if (branch_info.move_location_parallel) {
                def bilbo_drone_start = pipelineJob(current_branch + '.bilbo.register-' + branch_info.dataset + '-dronebuild') {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/all/bilbo_move_location_start.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibBilbo.bilbo_drone_job_move_location_parallel_dronebuild_start(bilbo_drone_start, project, branchfile, masterSettings, current_branch as String)

                branch_info.new_locations.each { String location, value ->
                    def bilbo_drone_location_start = pipelineJob(current_branch + '.bilbo.move.' + location + '.start') {
                        definition {
                            cps {
                                script(readFileFromWorkspace('src/scripts/schedulers/all/bilbo_move_location.groovy'))
                                sandbox(true)
                            }
                        }
                    }
                    LibBilbo.bilbo_drone_job_move_location_parallel_start(bilbo_drone_location_start, project, branchfile, masterSettings, current_branch as String, location)
                    def platforms = bilbo_move_matrix ?: code_matrix
                    for (platform in platforms) {
                        for (config in platform.configs) {
                            String configName = config instanceof Map ? config.name : config
                            def bilboDroneJobMove = job(current_branch + '.bilbo.move.' + platform.name + '.' + configName + '.' + location) {}
                            freestyle_jobs.add(bilboDroneJobMove)
                            LibBilbo.bilbo_drone_job_move_location_parallel_copy(bilboDroneJobMove, project, branchfile, masterSettings,
                                current_branch as String, platform.name as String, configName, location)
                            LibJobDsl.kill_processes(bilboDroneJobMove, branch_info)
                            LibJobDsl.initialP4revert(bilboDroneJobMove, project, branch_info)
                            LibJobDsl.addVaultSecrets(bilboDroneJobMove, branch_info)
                            LibJobDsl.archive_non_build_logs(bilboDroneJobMove, branch_info)
                            LibJobDsl.postclean_silverback(bilboDroneJobMove, project, branch_info)
                        }
                    }

                    def bilbo_drone_job_remote = job(current_branch + '.bilbo.register.remote.' + location) {}
                    freestyle_jobs.add(bilbo_drone_job_remote)
                    LibBilbo.bilbo_drone_job_move_location_parallel_remote(bilbo_drone_job_remote, project, branchfile, masterSettings, current_branch as String, location)
                    LibJobDsl.kill_processes(bilbo_drone_job_remote, branch_info)
                    LibJobDsl.initialP4revert(bilbo_drone_job_remote, project, branch_info)
                    LibJobDsl.addVaultSecrets(bilbo_drone_job_remote, branch_info)
                    LibJobDsl.archive_non_build_logs(bilbo_drone_job_remote, branch_info)
                    LibJobDsl.postclean_silverback(bilbo_drone_job_remote, project, branch_info)
                }
            }
        }

        if (branch_info.sparta_branch == true) {
            out.println('      Processing sparta_branch')
            def sparta_bundle_job = job(current_branch + '.sparta.bundle') {}
            freestyle_jobs.add(sparta_bundle_job)
            LibSparta.sparta_bundle_job(sparta_bundle_job, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(sparta_bundle_job, project, branch_info, '${data_changelist}', '${code_changelist}')
            LibJobDsl.with {
                kill_processes(sparta_bundle_job, branch_info)
                initialP4revert(sparta_bundle_job, project, branch_info)
                addVaultSecrets(sparta_bundle_job, branch_info)
                archive_pipelinelog(sparta_bundle_job, '*', pipelineLogCodeBranch)
                archive_non_build_logs(sparta_bundle_job, branch_info)
                postclean_silverback(sparta_bundle_job, project, branch_info)
            }
        }

        if (branch_info.marvin_trigger_upload_and_test) {
            out.println('      Processing marvin_trigger_upload_and_test')
            pipelineJob("${current_branch}.marvin.initiate") {
                description('Marvin Trigger job on external master (Initiate test)')
                logRotator(7, 100)
                parameters {
                    stringParam {
                        name('code_changelist')
                        defaultValue('')
                        description('Code changelist')
                        trim(true)
                    }
                    stringParam {
                        name('data_changelist')
                        defaultValue('')
                        description('Data changelist')
                        trim(true)
                    }
                }
                environmentVariables {
                    envs(
                        stream: branch_info.data_branch,
                        url: project.external_job.test_url,
                        TEST_CREDENTIALS: project.external_job.test_credentials,
                    )
                }
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/trigger-marvin-external-jobs.groovy'))
                        sandbox(true)
                    }
                }
            }
        }

        if (branch_info.navmesh == true) {
            out.println('      Processing navmesh')
            def navmesh_job = job(current_branch + '.navmesh') {}
            freestyle_jobs.add(navmesh_job)
            LibNavmesh.navmesh_job(navmesh_job, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(navmesh_job, project, branch_info, '${data_changelist}', '${code_changelist}')
            LibJobDsl.with {
                kill_processes(navmesh_job, branch_info)
                initialP4revert(navmesh_job, project, branch_info)
                addVaultSecrets(navmesh_job, branch_info)
                archive_pipelinelog(navmesh_job, branch_info.dataset, pipelineLogCodeBranch)
                archive_non_build_logs(navmesh_job, branch_info)
                postclean_silverback(navmesh_job, project, branch_info)
            }
        }

        if (branch_info.datasnooper == true) {
            out.println('      Processing datasnooper')
            def datasnooper_job = job(current_branch + '.datasnooper') {}
            freestyle_jobs.add(datasnooper_job)
            LibDatasnooper.datasnooper_job(datasnooper_job, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(datasnooper_job, project, branch_info, '${data_changelist}', '${code_changelist}')
            LibJobDsl.with {
                kill_processes(datasnooper_job, branch_info)
                initialP4revert(datasnooper_job, project, branch_info)
                addVaultSecrets(datasnooper_job, branch_info)
                archive_pipelinelog(datasnooper_job, branch_info.dataset, pipelineLogCodeBranch)
                archive_non_build_logs(datasnooper_job, branch_info)
                postclean_silverback(datasnooper_job, project, branch_info)
            }
        }

        if (branch_info.pipeline_warning_job == true) {
            out.println('      Processing pipeline_warning_job')
            def pipeline_warning_job = job(current_branch + '.pipeline-warning') {}
            freestyle_jobs.add(pipeline_warning_job)
            LibScm.sync_pipeline_warning(pipeline_warning_job, project, branch_info, '${data_changelist}', '${code_changelist}')
            LibPipelineWarning.pipeline_warning_job(pipeline_warning_job, project, branchfile, masterSettings, current_branch as String)
            LibJobDsl.with {
                kill_processes(pipeline_warning_job, branch_info)
                initialP4revert(pipeline_warning_job, project, branch_info)
                addVaultSecrets(pipeline_warning_job, branch_info)
                archive_pipelinelog(pipeline_warning_job, branch_info.dataset, pipelineLogCodeBranch)
                archive_non_build_logs(pipeline_warning_job, branch_info)
                postclean_silverback(pipeline_warning_job, project, branch_info)
            }
        }

        if (branch_info.store_regular_baseline_builds == true) {
            out.println('      Processing store_regular_baseline_builds')
            def store_regular_baseline_start = pipelineJob(current_branch + '.store_regular_baseline.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/store_regular_baseline_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibStoreBaselines.store_regular_baseline_start(store_regular_baseline_start, project, branch_info)

            def store_regular_baseline_builds = job(current_branch + '.store_regular_baseline_builds') {}
            freestyle_jobs.add(store_regular_baseline_builds)
            LibStoreBaselines.store_regular_baseline_builds(store_regular_baseline_builds, project, branchfile, masterSettings, current_branch)
            LibScm.sync_code(store_regular_baseline_builds, project, branch_info, '${code_changelist}')
            LibJobDsl.kill_processes(store_regular_baseline_builds, branch_info)
            LibJobDsl.initialP4revert(store_regular_baseline_builds, project, branch_info)
            LibJobDsl.addVaultSecrets(store_regular_baseline_builds, branch_info)
            LibJobDsl.archive_non_build_logs(store_regular_baseline_builds, branch_info)
        }

        for (shift_upload_job in shift_upload_matrix) {
            String job_name = "${current_branch}.shift.${shift_upload_job.shifter_type}.upload"
            out.println("      Processing ${job_name} job")
            def shift_job = job(job_name)
            freestyle_jobs.add(shift_job)
            Boolean code_only = !shift_upload_job.args.contains('data_changelist')
            LibShift.shift_upload(shift_job, project, branchfile, masterSettings, current_branch, shift_upload_job.shifter_type, code_only)
            LibJobDsl.kill_processes(shift_job, branch_info)
            LibJobDsl.initialP4revert(shift_job, project, branch_info)
            LibJobDsl.addVaultSecrets(shift_job, branch_info)
            LibJobDsl.archive_non_build_logs(shift_job, branch_info)
            LibJobDsl.postclean_silverback(shift_job, project, branch_info)
        }

        // Conditional shifter jobs
        for (shift_upload_job in shift_upload_matrix) {
            def pipelineName = "${current_branch}.shift.${shift_upload_job.shifter_type}.start"
            out.println("      Processing ${pipelineName}")
            def freestyle_job = pipelineJob(pipelineName) {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/shift_type_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }

            LibShift.shift_type_start(
                freestyle_job,
                shift_upload_job.shifter_type,
                current_branch
            )

            freestyle_jobs.add(freestyle_job)
        }

        List shiftSubscriptionMatrix = branch_info.shift_subscription_matrix ?: []
        for (shiftSubscriptionInfo in shiftSubscriptionMatrix) {
            Map branchInfoSync = [
                code_branch   : branch_info.code_branch,
                code_folder   : branch_info.code_folder,
                p4_code_creds : shiftSubscriptionInfo.p4_code_creds,
                p4_code_server: shiftSubscriptionInfo.p4_code_server,
                p4_data_server: shiftSubscriptionInfo.p4_data_server,
                workspace_root: branch_info.workspace_root,
            ]
            String jobName = "${current_branch}.process-shift-subscription-downloads.${shiftSubscriptionInfo.build_type}.in.${shiftSubscriptionInfo.dest_location}"
            out.println("      Processing ${jobName} job")
            def shiftSubscriptionJob = job(jobName)
            freestyle_jobs.add(shiftSubscriptionJob)
            LibShift.processShiftSubscriptionDownloads(shiftSubscriptionJob, project, branchfile, masterSettings, current_branch as String, shiftSubscriptionInfo as Map)
            LibScm.sync_code(shiftSubscriptionJob, project, branchInfoSync, '${code_changelist}')
            LibJobDsl.kill_processes(shiftSubscriptionJob, branch_info)
            LibJobDsl.initialP4revert(shiftSubscriptionJob, project, branchInfoSync)
            LibJobDsl.addVaultSecrets(shiftSubscriptionJob, branch_info)
            LibJobDsl.archive_non_build_logs(shiftSubscriptionJob, branch_info)
            LibJobDsl.postclean_silverback(shiftSubscriptionJob, project, branch_info)
        }

        // Parses which remote masters to send changelist value to
        if (branch_info.remote_masters_to_receive_code) { // create 'set' job
            out.println('      Processing remote_masters_to_receive_code')
            def code_lastknowngood = job(current_branch + '.code.lastknowngood') {}
            freestyle_jobs.add(code_lastknowngood)
            LibRemoteTriggers.code_lastknowngood_set(code_lastknowngood, branch_info)
            branchfile.preflight_settings.slack_channel_preflight?.channels.each { channel ->
                LibSlack.slack_default(code_lastknowngood, channel, project.short_name, true)
            }
        }
        if (branch_info.remote_masters_to_receive_data) { // create 'set' job
            out.println('      Processing remote_masters_to_receive_data')
            def data_lastknowngood = job(current_branch + '.data.lastknowngood') {}
            freestyle_jobs.add(data_lastknowngood)
            LibRemoteTriggers.data_lastknowngood_set(data_lastknowngood, branch_info)
            branchfile.preflight_settings.slack_channel_preflight?.channels.each { channel ->
                LibSlack.slack_default(data_lastknowngood, channel, project.short_name, true)
            }
        }
        if (branch_info.remote_masters_to_receive_changelists != null) {
            out.println('      Processing remote_masters_to_receive_changelists')
            def code_data_lastknowngood = job(current_branch + '.lastknowngood_remote') {}
            freestyle_jobs.add(code_data_lastknowngood)
            LibRemoteTriggers.code_data_lastknowngood_set(code_data_lastknowngood, branch_info)
        }

        // Registers a lastknowngood job at the remote master in question
        // Need to be careful when create this job, because we have both 'get' and 'set' set the same job name
        // if ended on the same jenkins master, this will overwrite what's in the 'set' job, see above logic
        if (branch_info.remote_master_code_branch) {  // not in use, preflight 'get' job is set in preflight_seed.groovy
            out.println('      Processing remote_master_code_branch')
            def code_lastknowngood = job(branch_info.remote_master_code_branch + '.code.lastknowngood') {}
            freestyle_jobs.add(code_lastknowngood)
            LibRemoteTriggers.code_lastknowngood_get(code_lastknowngood, branch_info)
            branchfile.preflight_settings.slack_channel_preflight?.channels.each { channel ->
                LibSlack.slack_default(code_lastknowngood, channel, project.short_name, true)
            }
        }
        // Need to be careful when create this job, because we have both 'get' and 'set' set the same job name
        // if ended on the same jenkins master, this will overwrite what's in the 'set' job, see above logic
        if (branch_info.remote_master_data_branch) {  // not in use, preflight 'get' job is set in preflight_seed.groovy
            out.println('      Processing remote_master_data_branch')
            def data_lastknowngood = job(branch_info.remote_master_data_branch + '.data.lastknowngood') {}
            freestyle_jobs.add(data_lastknowngood)
            LibRemoteTriggers.data_lastknowngood_get(data_lastknowngood, branch_info)
            branchfile.preflight_settings.slack_channel_preflight?.channels.each { channel ->
                LibSlack.slack_default(data_lastknowngood, channel, project.short_name, true)
            }
        }
        if (branch_info.remote_master_branch != null) {
            out.println('      Processing remote_master_branch')
            def code_data_lastknowngood = job(branch_info.remote_master_branch + '.lastknowngood_remote') {}
            freestyle_jobs.add(code_data_lastknowngood)
            LibRemoteTriggers.code_data_lastknowngood_get(code_data_lastknowngood, branch_info)
        }
        if (branch_info.trigger_jobs_from_remote_master != null) {
            out.println('      Processing trigger_jobs_from_remote_master')
            def code_data_lastknowngood = pipelineJob(branch_info.trigger_jobs_from_remote_master.remote_branch + '.lastknowngood_remote_pipeline') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/remote_triggering_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibRemoteTriggers.code_data_lastknowngood_pipeline(code_data_lastknowngood, project, branch_info)
        }

        // Create downstream job to update p4 counter value after start job(code||data||frosty) is finished
        if (branch_info.enable_lkg_p4_counters != null && branch_info.enable_lkg_p4_counters.toBoolean() == true) {
            out.println('      Processing enable_lkg_p4_counters')
            def code_p4counterupdater = job(current_branch + '.code.p4counterupdater') {}
            freestyle_jobs.add(code_p4counterupdater)
            LibCode.code_p4counter_updater(code_p4counterupdater, project, branch_info + branchfile.preflight_settings)
            LibJobDsl.addVaultSecrets(code_p4counterupdater, branch_info)
            LibJobDsl.archive_non_build_logs(code_p4counterupdater, branch_info)

            def data_p4counterupdater = job(current_branch + '.data.p4counterupdater') {}
            freestyle_jobs.add(data_p4counterupdater)
            LibData.data_p4counter_updater(data_p4counterupdater, project, branch_info + branchfile.preflight_settings)
            LibJobDsl.addVaultSecrets(data_p4counterupdater, branch_info)
            LibJobDsl.archive_non_build_logs(data_p4counterupdater, branch_info)

            def data_p4cleancounterupdater = job(current_branch + '.data.p4cleancounterupdater') {}
            freestyle_jobs.add(data_p4cleancounterupdater)
            LibData.data_p4counter_updater(data_p4cleancounterupdater, project, branch_info + branchfile.preflight_settings)
            LibJobDsl.addVaultSecrets(data_p4cleancounterupdater, branch_info)
            LibJobDsl.archive_non_build_logs(data_p4cleancounterupdater, branch_info)

            def frosty_p4counterupdater = job(current_branch + '.frosty.p4counterupdater') {}
            freestyle_jobs.add(frosty_p4counterupdater)
            LibFrosty.frosty_p4counter_updater(frosty_p4counterupdater, project, branch_info + branchfile.preflight_settings)
            LibJobDsl.addVaultSecrets(frosty_p4counterupdater, branch_info)
            LibJobDsl.archive_non_build_logs(frosty_p4counterupdater, branch_info)
        }

        /**
         currently this only runs for KIN on kin-dev branch and it should be a one job per one project (no multiple-branches case)
         if we want to expand this job later on to other projects, we can create different jenkinsfile <branch>_maptool.groovy file to be loaded
         this is the new maptool jobs
         **/
        if (branch_info.enable_maptool != null && branch_info.enable_maptool.toBoolean() == true) {
            out.println('      Processing enable_maptool')
            def maptool_start = pipelineJob(current_branch + '.maptool') { // e.g kin-dev.maptool as a pipeline job
                definition {
                    cps {
                        script(readFileFromWorkspace("src/scripts/schedulers/all/${current_branch}_maptool.groovy"))
                        sandbox(true)
                    }
                }
            }
            LibMapTool.maptool_start(maptool_start, project, branchfile, masterSettings, current_branch as String)
        }

        def separate_symbol_store_upload = LibCommonNonCps.get_setting_value(branch_info, [], 'separate_symbol_store_upload', true, project)
        if (separate_symbol_store_upload && !code_matrix.isEmpty()) {
            out.println('      Processing separate_symbol_store_upload')
            def symbolStoreUpload = job(branch_info.branch_name + '.symbolStoreUpload') {}
            freestyle_jobs.add(symbolStoreUpload)
            LibCode.symbolStoreUpload(symbolStoreUpload, project, branch_info)
            LibJobDsl.addVaultSecrets(symbolStoreUpload, branch_info)
            LibJobDsl.archive_non_build_logs(symbolStoreUpload, branch_info)
            LibSlack.slack_default(symbolStoreUpload, '#cobra-outage-symbolstoreupload', project.short_name, false)
            LibJobDsl.initialP4revert(symbolStoreUpload, project, branch_info, true, false)
        }

        if (branch_info.custom_tests) {
            out.println('      Processing custom-tests.build')
            def customTestsJob = job(branch_info.branch_name + '.custom-tests.build' as String) {}
            freestyle_jobs.add(customTestsJob)
            LibCustomTests.customTestsJob(customTestsJob, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(customTestsJob, project, branch_info, '', '${code_changelist}')
            LibJobDsl.with {
                kill_processes(customTestsJob, branch_info)
                initialP4revert(customTestsJob, project, branch_info, true, false)
                addVaultSecrets(customTestsJob, branch_info)
                archive_non_build_logs(customTestsJob, branch_info)
                postclean_silverback(customTestsJob, project, branch_info)
                claim_builds(customTestsJob, branch_info)
            }
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, current_branch, branchfile.freestyle_job_trigger_matrix)
    }
}
