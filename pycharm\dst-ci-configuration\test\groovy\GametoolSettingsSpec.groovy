import com.ea.lib.jobsettings.GametoolSettings
import spock.lang.Specification

class GametoolSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root      : 'workspace-root',
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'label',
        ]
        static Map general_settings = [
            frostbite_licensee: 'kingston',
            gametool_settings : [
                trigger                : 'a cron trigger',
                non_virtual_code_branch: 'kin-dev',
                non_virtual_code_folder: 'kin',
                gametools              : [
                    (LibPerforceSpec.GAMETOOL_ICEPICK)                    : [
                        timeout_hours : 6,
                        config        : 'final',
                        framework_args: ['first-arg', 'second-arg'],
                    ],
                    (LibPerforceSpec.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [
                        timeout_hours : 7,
                        config        : 'release',
                        framework_args: ['fdu-arg'],
                    ],
                    (LibPerforceSpec.GAMETOOL_FROSTYISOTOOL)              : [
                        timeout_hours: 7,
                    ],
                    (LibPerforceSpec.GAMETOOL_DRONE)                      : [
                        timeout_hours: 7,
                    ],
                    (LibPerforceSpec.GAMETOOL_FRAMEWORK)                  : [
                        timeout_hours: 7,
                    ],
                    (LibPerforceSpec.GAMETOOL_FBENV)                  : [
                        timeout_hours: 7,
                    ],
                ],
            ],
        ]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', code_folder: 'dev']]
    }

    class ProjectFile {
        static String name = 'Kingston'
        static String p4_code_server = 'url:port'
        static String p4_code_client_env = 'jenkins-codestream'
        static String p4_user_single_slash = 'domain\\user'
        static Map p4_fb_settings = [
            p4_port: 1667
        ]
    }

    void "test that we get expected job settings in initializeStart"() {
        when:
        GametoolSettings settings = new GametoolSettings()
        settings.initializeStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Sync branch code and deploy a binary build.'
            cronTrigger == BranchFile.general_settings.gametool_settings.trigger
            nonVirtualCodeBranch == BranchFile.general_settings.gametool_settings.non_virtual_code_branch
            nonVirtualCodeFolder == BranchFile.general_settings.gametool_settings.non_virtual_code_folder
            codeBranch == 'code-branch'
            codeFolder == 'dev'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeIcepick"() {
        when:
        GametoolSettings settings = new GametoolSettings()
        String gametool = LibPerforceSpec.GAMETOOL_ICEPICK
        settings.initializeIcepick(BranchFile, MasterFile, ProjectFile, 'branch', gametool)
        then:
        with(settings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.general_settings.gametool_settings.gametools."$gametool".timeout_hours * 60
            extraArgs == ' --licensee kingston --framework-args first-arg --framework-args second-arg'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
            description == 'Compiles and submits Icepick'
            fbLoginDetails.p4_port == 1667
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} gametool/gametool icepick --code-changelist %CODE_CHANGELIST% " +
                "--clean %CLEAN_LOCAL%${extraArgs} --config" +
                " ${BranchFile.general_settings.gametool_settings.gametools."$gametool".config} --submit %SUBMIT%"
        }
    }

    void "test that we get expected job settings in initializeFrostbiteDatabaseUpgrader"() {
        when:
        GametoolSettings settings = new GametoolSettings()
        String gametool = LibPerforceSpec.GAMETOOL_FROSTBITE_DATABASE_UPGRADER
        settings.initializeFrostbiteDatabaseUpgrader(BranchFile, MasterFile, ProjectFile, 'branch', gametool)
        then:
        with(settings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.general_settings.gametool_settings.gametools."$gametool".timeout_hours * 60
            extraArgs == ' --framework-args fdu-arg'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
            description == 'Compiles and submits Frostbite Database Upgrader'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} gametool/frostbite_database_upgrader --code-changelist" +
                " %CODE_CHANGELIST% --clean %CLEAN_LOCAL%${extraArgs} --config" +
                " ${BranchFile.general_settings.gametool_settings.gametools."$gametool".config} --p4-port ${ProjectFile.p4_code_server}" +
                " --p4-client ${ProjectFile.p4_code_client_env} --p4-user ${ProjectFile.p4_user_single_slash} --submit %SUBMIT%"
        }
    }

    void "test that we get expected job settings in initializeFrostyisotool"() {
        when:
        GametoolSettings settings = new GametoolSettings()
        String gametool = LibPerforceSpec.GAMETOOL_FROSTYISOTOOL
        settings.initializeFrostyisotool(BranchFile, MasterFile, ProjectFile, 'branch', gametool)
        then:
        with(settings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.general_settings.gametool_settings.gametools."$gametool".timeout_hours * 60
            extraArgs == ' --licensee kingston'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
            description == 'Compiles and submits FrostyIsoTool.'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} gametool/frostyisotool --code-changelist %CODE_CHANGELIST%" +
                " --clean %CLEAN_LOCAL% --submit %SUBMIT% --p4-client ${ProjectFile.p4_code_client_env} --p4-port" +
                " ${ProjectFile.p4_code_server} ${extraArgs}"
        }
    }

    void "test that we get expected job settings in initializeDrone"() {
        when:
        GametoolSettings settings = new GametoolSettings()
        String gametool = LibPerforceSpec.GAMETOOL_DRONE
        settings.initializeDrone(BranchFile, MasterFile, ProjectFile, 'branch', gametool)
        then:
        with(settings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.general_settings.gametool_settings.gametools."$gametool".timeout_hours * 60
            extraArgs == ' --licensee kingston'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
            description == 'Compiles and submits Drone.'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} gametool/drone --code-changelist %CODE_CHANGELIST%" +
                " --clean %CLEAN_LOCAL% --p4-port ${ProjectFile.p4_code_server} --p4-client ${ProjectFile.p4_code_client_env}" +
                " --submit %SUBMIT% ${extraArgs}"
        }
    }
}
