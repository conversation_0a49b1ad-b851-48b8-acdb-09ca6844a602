We have this printout in the jenkins log:
00:10:46 2025-07-03 23:10:46 elipy2 [INFO]: <PERSON><PERSON><PERSON> builds to be deleted:
00:10:46 2025-07-03 23:10:46 elipy2 [INFO]:   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-release\24254078\CH1-release\24254078 (age: 5.6 days)
00:10:46 2025-07-03 23:10:46 elipy2 [INFO]:   \\eauk-file.eu.ad.ea.com\Glacier\Autobuilds/frosty\BattlefieldGame/CH1-release\24270490\CH1-release\24270490 (age: 3.4 days)
00:10:46 2025-07-03 23:10:46 elipy2 [INFO]:   ... and 6 more orphan builds

Please fix so:
- print all orphan builds, not just ... and 6 more
- the same format for path use only \, not using /

After that:
- Investigate why we still have 28 orphan builds but the C:\Users\<USER>\vscode\output\total_build_under_path.ps1 "\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event" "https://bct-bilbo-eck.cobra.dre.ea.com" "criterion_bilbo" return only 21

All git monitored files which was updated should be run with black/lint and have green unittest