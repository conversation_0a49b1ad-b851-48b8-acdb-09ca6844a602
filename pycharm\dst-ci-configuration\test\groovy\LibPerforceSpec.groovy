import com.ea.lib.LibPerforce
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import support.Steps

class LibPerforceSpec extends Specification {

    final static String GAMETOOL_ICEPICK = 'gametool.icepick'
    final static String GAMETOOL_FROSTBITE_DATABASE_UPGRADER = 'gametool.frostbiteDatabaseUpgrader'
    final static String GAMETOOL_FROSTYISOTOOL = 'gametool.frostyisotool'
    final static String GAMETOOL_DRONE = 'gametool.drone'
    final static String GAMETOOL_FRAMEWORK = 'gametool.framework'
    final static String GAMETOOL_FBENV = 'gametool.fbenv'

    @Shared LibPerforce libPerforce
    @Shared Steps steps

    void setupSpec() {
        Steps.metaClass.p4CheckoutManual = { String viewName, String workspaceName, String p4Credentials -> void }
        Steps.metaClass.p4 = { Map ->
            [
                run: { command, arg ->
                    [
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/bin/Polly.dll']                                           : null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/source/Icepick.Core/TestRunner/TestRunJob.cs']            : null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/source/Icepick.Service/Icepick.Service.cs']               : null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/source/Icepick.TestApplication/IcepickTestApplication.cs']: null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Build/test_nant/tests/CMUnitTests/win_resources/CMUnitTests.build']          : null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Build/test_nant/tests/CMUnitTests/warninglevels/CMUnitTests.build']          : null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Code/Utils/FrostbiteDatabaseUpgrader/src/Frostbite.DataRefactor/DataSet.cs'] : null,
                        [depotFile: '//dicestudio/battlefield/stage/bf-stage/TnT/Code/Utils/FrostbiteDatabaseUpgrader/src/Frostbite.DataRefactor/IDataSet.cs']: null,
                    ]
                }
            ]
        }
        Steps.metaClass.echo = { String message -> void }
    }

    void setup() {
        GroovySpy(Steps, global: true)
        steps = Mock(Steps)
        libPerforce = new LibPerforce(steps, 'san',
            [GAMETOOL_ICEPICK, GAMETOOL_FROSTBITE_DATABASE_UPGRADER], 'stage', 'bf-stage',
            '//dicestudio/battlefield', 'san-creds', 'gametool.start', false)

        libPerforce.triggerPaths = [
            (GAMETOOL_ICEPICK)                    : [
                filter: [
                    'TnT/Automation/Icepick/...',
                    'TnT/Code/Tools/Shared/Frostbite.Math/...',
                ],
                ignore: [
                    'TnT/Automation/Icepick/bin/...',
                ],
            ],
            (GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [
                filter: [
                    'TnT/Code/Utils/FrostbiteDatabaseUpgrader/AuthorityOracle/...',
                    'TnT/Code/Utils/FrostbiteDatabaseUpgrader/*',
                ]
            ],
            (GAMETOOL_FRAMEWORK): [
                filter: [
                    'TnT/Build/Framework/...',
                ]
            ],
            (GAMETOOL_FBENV): [
                filter: [
                    'TnT/Build/fbenv/...',
                ]
            ],
        ]
    }

    void 'setPollScmTriggers does something'() {
        String expectedViewName = '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/... //jenkins-san-master-gametool.start-setPollScmTriggers/TnT/Automation/Icepick/...\n' +
            '//dicestudio/battlefield/stage/bf-stage/TnT/Code/Tools/Shared/Frostbite.Math/... //jenkins-san-master-gametool.start-setPollScmTriggers/TnT/Code/Tools/Shared/Frostbite.Math/...\n' +
            '//dicestudio/battlefield/stage/bf-stage/TnT/Code/Utils/FrostbiteDatabaseUpgrader/AuthorityOracle/... //jenkins-san-master-gametool.start-setPollScmTriggers/TnT/Code/Utils/FrostbiteDatabaseUpgrader/AuthorityOracle/...\n' +
            '//dicestudio/battlefield/stage/bf-stage/TnT/Code/Utils/FrostbiteDatabaseUpgrader/* //jenkins-san-master-gametool.start-setPollScmTriggers/TnT/Code/Utils/FrostbiteDatabaseUpgrader/*\n' +
            '-//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/bin/... //jenkins-san-master-gametool.start-setPollScmTriggers/TnT/Automation/Icepick/bin/...'
        when:
        libPerforce.setPollScmTriggers()
        then:
        1 * steps.p4CheckoutManual(expectedViewName, 'jenkins-san-master-gametool.start-setPollScmTriggers', 'san-creds')
    }

    void 'getModifiedTools returns correct tool'() {
        when:
        List<String> tools = libPerforce.getModifiedTools('123')
        then:
        tools == [GAMETOOL_ICEPICK]
    }

    @Unroll
    void 'containsFilter returns #expectedResult with filter #filter and depotFile #depotFile'() {
        when:
        boolean result = LibPerforce.containsFilter(filter, depotFile)
        then:
        result == expectedResult
        where:
        filter                       | depotFile                                                                                                       | expectedResult
        'TnT/Automation/Icepick/...' | '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/source/Icepick.Core/TestRunner/TestRunJob.cs'   | true
        'TnT/Automation/Icepick/...' | '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/TestRunJob.cs'                                  | true
        'TnT/Automation/Icepick/...' | '//dicestudio/battlefield/stage/bf-stage/TnT/Build/test_nant/tests/CMUnitTests/win_resources/CMUnitTests.build' | false
        'TnT/Automation/Icepick/*'   | '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/source/Icepick.Core/TestRunner/TestRunJob.cs'   | false
        'TnT/Automation/Icepick/*'   | '//dicestudio/battlefield/stage/bf-stage/TnT/Automation/Icepick/TestRunJob.cs'                                  | true
        'TnT/Automation/Icepick/*'   | '//dicestudio/battlefield/stage/bf-stage/TnT/Build/test_nant/tests/CMUnitTests/win_resources/CMUnitTests.build' | false
        'TnT/AutoIntegrate.json'     | '//dicestudio/battlefield/stage/bf-stage/TnT/AutoIntegrate.json'                                                | true
        'TnT/AutoIntegrate.json'     | '//dicestudio/battlefield/stage/bf-stage/TnT/Build/AutoIntegrate.json'                                          | false
    }

}
