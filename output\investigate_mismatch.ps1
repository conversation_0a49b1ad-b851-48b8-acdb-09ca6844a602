# Investigation script for build deletion mismatch
# This script will query <PERSON><PERSON><PERSON> for the mismatched builds to understand their status

param(
    [string]$BilboEndpoint = "https://bct-bilbo-eck.cobra.dre.ea.com",
    [string]$BilboIndex = "criterion_bilbo"
)

Write-Host "=" * 80
Write-Host "Investigating Build Deletion Mismatch"
Write-Host "=" * 80

# Builds that <PERSON> wants to delete but script doesn't
$JenkinsOnlyBuilds = @(
    "24216947", "24221370", "24230552", "24237548", 
    "24254078", "24283046", "24286233", "24288006"
)

# Build that script wants to delete but <PERSON> doesn't
$ScriptOnlyBuilds = @("24310479")

# Function to query Bilbo for a specific build
function Query-BilboBuild {
    param(
        [string]$BuildNumber,
        [string]$Endpoint,
        [string]$Index
    )
    
    $query = @{
        "query" = @{
            "bool" = @{
                "must" = @(
                    @{
                        "term" = @{
                            "changelist" = $BuildNumber
                        }
                    },
                    @{
                        "term" = @{
                            "stream" = "CH1-event"
                        }
                    }
                )
            }
        }
        "size" = 1
        "_source" = @("changelist", "stream", "deleted", "creation_time", "verified_for_preflight", "release_candidate")
    } | ConvertTo-Json -Depth 10

    try {
        $response = Invoke-RestMethod -Uri "$Endpoint/$Index/_search" -Method POST -Body $query -ContentType "application/json"
        return $response
    }
    catch {
        Write-Warning "Error querying build $BuildNumber`: $_"
        return $null
    }
}

# Function to analyze build status
function Analyze-BuildStatus {
    param(
        [string]$BuildNumber,
        [object]$BilboResponse
    )
    
    if ($null -eq $BilboResponse -or $BilboResponse.hits.total.value -eq 0) {
        return @{
            "BuildNumber" = $BuildNumber
            "Status" = "NOT_FOUND"
            "Deleted" = "UNKNOWN"
            "CreationTime" = "UNKNOWN"
            "ReleaseCandidate" = "UNKNOWN"
            "VerifiedForPreflight" = "UNKNOWN"
        }
    }
    
    $hit = $BilboResponse.hits.hits[0]._source
    
    return @{
        "BuildNumber" = $BuildNumber
        "Status" = "FOUND"
        "Deleted" = if ($hit.deleted) { "YES" } else { "NO" }
        "CreationTime" = $hit.creation_time
        "ReleaseCandidate" = if ($hit.release_candidate) { "YES" } else { "NO" }
        "VerifiedForPreflight" = if ($hit.verified_for_preflight) { "YES" } else { "NO" }
    }
}

# Function to calculate build age
function Get-BuildAge {
    param([string]$CreationTime)
    
    if ($CreationTime -eq "UNKNOWN" -or [string]::IsNullOrEmpty($CreationTime)) {
        return "UNKNOWN"
    }
    
    try {
        $creationDate = [DateTime]::Parse($CreationTime)
        $age = (Get-Date) - $creationDate
        return "$([math]::Round($age.TotalDays, 1)) days"
    }
    catch {
        return "PARSE_ERROR"
    }
}

Write-Host "`nInvestigating builds that Jenkins wants to delete but script doesn't..."
Write-Host "-" * 70

$JenkinsOnlyResults = @()
foreach ($build in $JenkinsOnlyBuilds) {
    Write-Host "Querying build $build..." -NoNewline
    $response = Query-BilboBuild -BuildNumber $build -Endpoint $BilboEndpoint -Index $BilboIndex
    $analysis = Analyze-BuildStatus -BuildNumber $build -BilboResponse $response
    $analysis["Age"] = Get-BuildAge -CreationTime $analysis["CreationTime"]
    $JenkinsOnlyResults += $analysis
    Write-Host " Done"
}

Write-Host "`nInvestigating builds that script wants to delete but Jenkins doesn't..."
Write-Host "-" * 70

$ScriptOnlyResults = @()
foreach ($build in $ScriptOnlyBuilds) {
    Write-Host "Querying build $build..." -NoNewline
    $response = Query-BilboBuild -BuildNumber $build -Endpoint $BilboEndpoint -Index $BilboIndex
    $analysis = Analyze-BuildStatus -BuildNumber $build -BilboResponse $response
    $analysis["Age"] = Get-BuildAge -CreationTime $analysis["CreationTime"]
    $ScriptOnlyResults += $analysis
    Write-Host " Done"
}

# Display results
Write-Host "`n" + "=" * 80
Write-Host "RESULTS: Builds Jenkins wants to delete but script doesn't"
Write-Host "=" * 80

Write-Host ("{0,-12} {1,-8} {2,-10} {3,-20} {4,-10} {5,-15}" -f "Build", "Found", "Deleted", "Creation Time", "Age", "Release Candidate")
Write-Host "-" * 80

foreach ($result in $JenkinsOnlyResults) {
    $creationTimeShort = if ($result["CreationTime"].Length -gt 19) { $result["CreationTime"].Substring(0, 19) } else { $result["CreationTime"] }
    Write-Host ("{0,-12} {1,-8} {2,-10} {3,-20} {4,-10} {5,-15}" -f 
        $result["BuildNumber"], 
        $result["Status"], 
        $result["Deleted"], 
        $creationTimeShort,
        $result["Age"],
        $result["ReleaseCandidate"]
    )
}

Write-Host "`n" + "=" * 80
Write-Host "RESULTS: Builds script wants to delete but Jenkins doesn't"
Write-Host "=" * 80

Write-Host ("{0,-12} {1,-8} {2,-10} {3,-20} {4,-10} {5,-15}" -f "Build", "Found", "Deleted", "Creation Time", "Age", "Release Candidate")
Write-Host "-" * 80

foreach ($result in $ScriptOnlyResults) {
    $creationTimeShort = if ($result["CreationTime"].Length -gt 19) { $result["CreationTime"].Substring(0, 19) } else { $result["CreationTime"] }
    Write-Host ("{0,-12} {1,-8} {2,-10} {3,-20} {4,-10} {5,-15}" -f 
        $result["BuildNumber"], 
        $result["Status"], 
        $result["Deleted"], 
        $creationTimeShort,
        $result["Age"],
        $result["ReleaseCandidate"]
    )
}

# Analysis summary
Write-Host "`n" + "=" * 80
Write-Host "ANALYSIS SUMMARY"
Write-Host "=" * 80

# Count builds by status for Jenkins-only builds
$jenkinsDeletedCount = ($JenkinsOnlyResults | Where-Object { $_["Deleted"] -eq "YES" }).Count
$jenkinsNotDeletedCount = ($JenkinsOnlyResults | Where-Object { $_["Deleted"] -eq "NO" }).Count
$jenkinsNotFoundCount = ($JenkinsOnlyResults | Where-Object { $_["Status"] -eq "NOT_FOUND" }).Count

Write-Host "Jenkins-only builds (8 total):"
Write-Host "  - Marked as deleted in Bilbo: $jenkinsDeletedCount"
Write-Host "  - NOT marked as deleted in Bilbo: $jenkinsNotDeletedCount"
Write-Host "  - Not found in Bilbo: $jenkinsNotFoundCount"

# Count builds by status for Script-only builds
$scriptDeletedCount = ($ScriptOnlyResults | Where-Object { $_["Deleted"] -eq "YES" }).Count
$scriptNotDeletedCount = ($ScriptOnlyResults | Where-Object { $_["Deleted"] -eq "NO" }).Count
$scriptNotFoundCount = ($ScriptOnlyResults | Where-Object { $_["Status"] -eq "NOT_FOUND" }).Count

Write-Host "`nScript-only builds (1 total):"
Write-Host "  - Marked as deleted in Bilbo: $scriptDeletedCount"
Write-Host "  - NOT marked as deleted in Bilbo: $scriptNotDeletedCount"
Write-Host "  - Not found in Bilbo: $scriptNotFoundCount"

# Check for potential issues
Write-Host "`nPOTENTIAL ISSUES IDENTIFIED:"

if ($jenkinsNotDeletedCount -gt 0) {
    Write-Host "  ⚠️  Jenkins is trying to delete $jenkinsNotDeletedCount builds that are NOT marked as deleted in Bilbo"
}

if ($scriptDeletedCount -gt 0) {
    Write-Host "  ⚠️  Script identified $scriptDeletedCount builds as deleted that Jenkins didn't want to delete"
}

$recentBuilds = $JenkinsOnlyResults | Where-Object { 
    $_["Age"] -ne "UNKNOWN" -and $_["Age"] -ne "PARSE_ERROR" -and 
    [double]($_["Age"].Split(" ")[0]) -lt 2 
}

if ($recentBuilds.Count -gt 0) {
    Write-Host "  ⚠️  Jenkins is trying to delete $($recentBuilds.Count) builds that are less than 2 days old"
}

Write-Host "`nInvestigation complete. Check the results above for inconsistencies."
