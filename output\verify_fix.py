import subprocess
import sys
import os

# Make sure we're in the right directory
os.chdir(r'c:\Users\<USER>\vscode\pycharm\elipy2')

print("Running test: test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception")
result1 = subprocess.run([
    sys.executable, '-m', 'pytest', 
    'elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception',
    '-v'
], capture_output=True, text=True)

print(f"Test 1 exit code: {result1.returncode}")
if result1.returncode == 0:
    print("✓ Test 1 PASSED")
else:
    print("✗ Test 1 FAILED")
    print("STDOUT:", result1.stdout[-300:])
    print("STDERR:", result1.stderr[-200:])

print("\nRunning test: test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception")
result2 = subprocess.run([
    sys.executable, '-m', 'pytest', 
    'elipy2/tests/test_expire.py::TestExpire::test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception',
    '-v'
], capture_output=True, text=True)

print(f"Test 2 exit code: {result2.returncode}")
if result2.returncode == 0:
    print("✓ Test 2 PASSED")
else:
    print("✗ Test 2 FAILED")
    print("STDOUT:", result2.stdout[-300:])
    print("STDERR:", result2.stderr[-200:])

if result1.returncode == 0 and result2.returncode == 0:
    print("\n🎉 ALL TESTS PASSED! The fix is working correctly.")
else:
    print("\n❌ Some tests still failing. Need further investigation.")
