#!/usr/bin/env python3
"""
Test script to verify the ch1-event fix logic using local simulation
"""

def test_ch1_event_fix():
    """Test the logic fix for ch1-event branch discovery"""
    
    print("=== CH1-Event Fix Logic Test ===")
    
    # Simulate the existing logic (bilbo-first with fallback)
    print("\n1. EXISTING LOGIC SIMULATION:")
    
    # Simulate bilbo returning some branches but missing ch1-event
    bilbo_branches = {"ch1-content-dev", "ch1-stage", "trunk-code-dev"}
    disk_branches = {"ch1-content-dev", "ch1-stage", "trunk-code-dev", "ch1-event", "ch1-bflabs-release"}
    
    print(f"   Bilbo branches: {sorted(bilbo_branches)}")
    print(f"   Disk branches: {sorted(disk_branches)}")
    
    # Existing logic: only use disk if significantly more branches
    if len(disk_branches) > len(bilbo_branches):
        existing_result = bilbo_branches.union(disk_branches)
        print(f"   Fallback triggered: using combined branches")
    else:
        existing_result = bilbo_branches
        print(f"   Fallback NOT triggered: using only bilbo branches")
    
    print(f"   Final branches (existing): {sorted(existing_result)}")
    existing_finds_ch1_event = "ch1-event" in existing_result
    print(f"   Finds ch1-event: {'✓' if existing_finds_ch1_event else '✗'}")
    
    print("\n2. NEW LOGIC SIMULATION:")
    
    # New logic: disk-first approach
    new_result = set()
    
    # Simulate disk-based discovery for frosty path
    print(f"   Disk-based discovery: {sorted(disk_branches)}")
    new_result.update(disk_branches)
    
    # Add any additional bilbo branches not found on disk
    additional_bilbo = bilbo_branches - new_result
    if additional_bilbo:
        print(f"   Additional from bilbo: {sorted(additional_bilbo)}")
        new_result.update(additional_bilbo)
    else:
        print(f"   No additional branches from bilbo")
    
    print(f"   Final branches (new): {sorted(new_result)}")
    new_finds_ch1_event = "ch1-event" in new_result
    print(f"   Finds ch1-event: {'✓' if new_finds_ch1_event else '✗'}")
    
    print("\n3. COMPARISON:")
    print(f"   Existing logic finds ch1-event: {'✓' if existing_finds_ch1_event else '✗'}")
    print(f"   New logic finds ch1-event: {'✓' if new_finds_ch1_event else '✗'}")
    
    if new_finds_ch1_event and not existing_finds_ch1_event:
        print(f"   ✓ SUCCESS: New logic fixes the issue!")
        return True
    elif existing_finds_ch1_event:
        print(f"   ? Both logics work, issue might be elsewhere")
        return False
    else:
        print(f"   ✗ Neither logic finds ch1-event")
        return False


def test_different_scenarios():
    """Test various scenarios where bilbo data might be incomplete"""
    
    print("\n=== DIFFERENT SCENARIOS TEST ===")
    
    scenarios = [
        {
            "name": "Bilbo empty",
            "bilbo": set(),
            "disk": {"ch1-event", "ch1-content-dev", "trunk-code-dev"}
        },
        {
            "name": "Bilbo partial (missing ch1-event)",
            "bilbo": {"ch1-content-dev", "trunk-code-dev"},
            "disk": {"ch1-event", "ch1-content-dev", "trunk-code-dev"}
        },
        {
            "name": "Bilbo complete",
            "bilbo": {"ch1-event", "ch1-content-dev", "trunk-code-dev"},
            "disk": {"ch1-event", "ch1-content-dev", "trunk-code-dev"}
        }
    ]
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        bilbo_branches = scenario["bilbo"]
        disk_branches = scenario["disk"]
        
        # Existing logic
        if len(disk_branches) > len(bilbo_branches):
            existing_result = bilbo_branches.union(disk_branches)
        else:
            existing_result = bilbo_branches
        existing_finds = "ch1-event" in existing_result
        
        # New logic (always uses disk first)
        new_result = disk_branches.union(bilbo_branches)
        new_finds = "ch1-event" in new_result
        
        print(f"   Existing finds ch1-event: {'✓' if existing_finds else '✗'}")
        print(f"   New finds ch1-event: {'✓' if new_finds else '✗'}")


if __name__ == "__main__":
    success = test_ch1_event_fix()
    test_different_scenarios()
    
    print(f"\n=== FINAL RESULT ===")
    if success:
        print("✓ The new disk-first logic should fix the ch1-event issue!")
    else:
        print("? Further investigation needed.")
