package scripts.schedulers.frosty

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import hudson.model.Result

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * combined_bundle_start.groovy
 * 
 * New dedicated Jenkins job for combined bundle creation.
 * This job runs separately from frosty/patchfrosty to solve the issue
 * of multiple bundle sets being created with only one being copied to network share.
 */
pipeline {
    agent { label 'combined_bundle_agent || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    parameters {
        string(name: 'code_changelist', defaultValue: '', description: 'Code changelist to use')
        string(name: 'data_changelist', defaultValue: '', description: 'Data changelist to use')
        string(name: 'combine_code_changelist', defaultValue: '', description: 'Combine code changelist')
        string(name: 'combine_data_changelist', defaultValue: '', description: 'Combine data changelist')
        choice(name: 'bundle_type', choices: ['head', 'delta'], description: 'Type of bundles to create')
        string(name: 'platform', defaultValue: 'win64', description: 'Target platform')
        string(name: 'region', defaultValue: 'ww', description: 'Target region')
        string(name: 'config', defaultValue: 'final', description: 'Build configuration')
        booleanParam(name: 'feature_flag_enabled', defaultValue: false, description: 'Enable new separate workflow')
        booleanParam(name: 'use_separate_workflow', defaultValue: true, description: 'Use separate workflow vs legacy')
        string(name: 'baseline_code_branch', defaultValue: '', description: 'Baseline code branch for delta bundles')
        string(name: 'baseline_code_changelist', defaultValue: '', description: 'Baseline code changelist for delta bundles')
        string(name: 'baseline_data_branch', defaultValue: '', description: 'Baseline data branch for delta bundles')
        string(name: 'baseline_data_changelist', defaultValue: '', description: 'Baseline data changelist for delta bundles')
    }
    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    // Validate required parameters
                    if (!params.code_changelist) {
                        error('code_changelist parameter is required')
                    }
                    if (!params.data_changelist) {
                        error('data_changelist parameter is required')
                    }
                    if (!params.platform) {
                        error('platform parameter is required')
                    }
                    
                    // Validate delta bundle parameters if needed
                    if (params.bundle_type == 'delta') {
                        if (!params.baseline_code_changelist || !params.baseline_data_changelist) {
                            error('baseline_code_changelist and baseline_data_changelist are required for delta bundles')
                        }
                    }
                    
                    // Set display name
                    currentBuild.displayName = "#${BUILD_NUMBER} - ${params.platform} ${params.bundle_type} - CL:${params.code_changelist}"
                    
                    echo "Starting Combined Bundle Creation"
                    echo "Platform: ${params.platform}"
                    echo "Bundle Type: ${params.bundle_type}"
                    echo "Code CL: ${params.code_changelist}"
                    echo "Data CL: ${params.data_changelist}"
                    echo "Feature Flag: ${params.feature_flag_enabled}"
                    echo "Use Separate Workflow: ${params.use_separate_workflow}"
                }
            }
        }
        
        stage('Setup Environment') {
            steps {
                script {
                    // Set up environment variables
                    env.CODE_BRANCH = branchfile.general_settings.code_branch
                    env.DATA_BRANCH = branchfile.general_settings.data_branch
                    env.DATA_DIRECTORY = branchfile.general_settings.data_directory
                    
                    // Determine baseline branches if not specified
                    if (params.bundle_type == 'delta') {
                        if (!params.baseline_code_branch) {
                            env.BASELINE_CODE_BRANCH = env.CODE_BRANCH
                        } else {
                            env.BASELINE_CODE_BRANCH = params.baseline_code_branch
                        }
                        
                        if (!params.baseline_data_branch) {
                            env.BASELINE_DATA_BRANCH = env.DATA_BRANCH
                        } else {
                            env.BASELINE_DATA_BRANCH = params.baseline_data_branch
                        }
                    }
                    
                    echo "Code Branch: ${env.CODE_BRANCH}"
                    echo "Data Branch: ${env.DATA_BRANCH}"
                    echo "Data Directory: ${env.DATA_DIRECTORY}"
                    
                    if (params.bundle_type == 'delta') {
                        echo "Baseline Code Branch: ${env.BASELINE_CODE_BRANCH}"
                        echo "Baseline Data Branch: ${env.BASELINE_DATA_BRANCH}"
                    }
                }
            }
        }
        
        stage('Check Prerequisites') {
            steps {
                script {
                    // Check if source bundles exist
                    echo "Checking if source bundles are available..."
                    
                    // For head bundles, check if regular bundles exist
                    if (params.bundle_type == 'head') {
                        def source_job = "${env.branch_name}.databuild.${env.DATA_DIRECTORY}.${params.platform}.bundles.${params.region}.${params.config}"
                        echo "Checking prerequisite job: ${source_job}"
                        
                        // In real implementation, we would check if the job completed successfully
                        // For now, we'll assume it's available
                        echo "Source bundles should be available from: ${source_job}"
                    }
                    
                    // For delta bundles, check if both current and baseline bundles exist
                    if (params.bundle_type == 'delta') {
                        def current_job = "${env.branch_name}.databuild.${env.DATA_DIRECTORY}.${params.platform}.bundles.${params.region}.${params.config}"
                        echo "Checking current bundles from: ${current_job}"
                        
                        // Also need to verify baseline bundles exist
                        echo "Baseline bundles should be available for comparison"
                    }
                }
            }
        }
        
        stage('Create Combined Bundles') {
            steps {
                script {
                    echo "Starting combined bundle creation..."
                    
                    // Build the elipy command
                    def elipy_cmd = [
                        "elipy",
                        "--location", env.location,
                        "combined_bundle_creator",
                        params.platform,
                        params.bundle_type,
                        "--data-branch", env.DATA_BRANCH,
                        "--data-changelist", params.data_changelist,
                        "--code-branch", env.CODE_BRANCH,
                        "--code-changelist", params.code_changelist,
                        "--region", params.region,
                        "--config", params.config
                    ]
                    
                    // Add feature flag parameters
                    if (params.feature_flag_enabled) {
                        elipy_cmd.add("--feature-flag-enabled")
                    } else {
                        elipy_cmd.add("--feature-flag-disabled")
                    }
                    
                    if (params.use_separate_workflow) {
                        elipy_cmd.add("--use-separate-workflow")
                    } else {
                        elipy_cmd.add("--use-legacy-workflow")
                    }
                    
                    // Add baseline parameters for delta bundles
                    if (params.bundle_type == 'delta') {
                        elipy_cmd.addAll([
                            "--baseline-code-branch", env.BASELINE_CODE_BRANCH,
                            "--baseline-code-changelist", params.baseline_code_changelist,
                            "--baseline-data-branch", env.BASELINE_DATA_BRANCH,
                            "--baseline-data-changelist", params.baseline_data_changelist
                        ])
                    }
                    
                    // Execute the command
                    def cmd_string = elipy_cmd.join(' ')
                    echo "Executing: ${cmd_string}"
                    
                    def result = bat(script: cmd_string, returnStatus: true)
                    
                    if (result != 0) {
                        error("Combined bundle creation failed with exit code: ${result}")
                    }
                    
                    echo "Combined bundle creation completed successfully"
                }
            }
        }
        
        stage('Validate Output') {
            steps {
                script {
                    echo "Validating combined bundle output..."
                    
                    // In real implementation, we would:
                    // 1. Check if output files exist in the expected location
                    // 2. Validate file sizes and content
                    // 3. Verify network share accessibility
                    // 4. Run any required post-processing checks
                    
                    echo "Output validation completed successfully"
                }
            }
        }
        
        stage('Trigger Downstream Jobs') {
            when {
                expression { currentBuild.result != 'FAILURE' }
            }
            steps {
                script {
                    echo "Triggering downstream jobs that consume combined bundles..."
                    
                    // Trigger frosty/patchfrosty jobs that need the combined bundles
                    def downstream_jobs = []
                    
                    // Add frosty jobs that use combined bundles
                    if (branchfile.frosty_matrix) {
                        for (platform in branchfile.frosty_matrix) {
                            for (variant in platform.variants) {
                                if (variant.format.contains('combine')) {
                                    def job_name = "${env.branch_name}.frosty.${env.DATA_DIRECTORY}.${platform.name}.${variant.format}.${variant.region}.${variant.config}"
                                    downstream_jobs.add(job_name)
                                }
                            }
                        }
                    }
                    
                    // Add patchfrosty jobs that use combined bundles
                    if (branchfile.patchfrosty_matrix) {
                        for (platform in branchfile.patchfrosty_matrix) {
                            for (variant in platform.variants) {
                                if (variant.format.contains('combine')) {
                                    def job_name = "${env.branch_name}.patchfrosty.${env.DATA_DIRECTORY}.${platform.name}.${variant.format}.${variant.region}.${variant.config}"
                                    downstream_jobs.add(job_name)
                                }
                            }
                        }
                    }
                    
                    // Trigger downstream jobs in parallel
                    if (downstream_jobs.size() > 0) {
                        def parallel_jobs = [:]
                        for (job_name in downstream_jobs) {
                            parallel_jobs[job_name] = {
                                build(
                                    job: job_name,
                                    parameters: [
                                        string(name: 'code_changelist', value: params.code_changelist),
                                        string(name: 'data_changelist', value: params.data_changelist),
                                        string(name: 'combine_code_changelist', value: params.combine_code_changelist ?: params.code_changelist),
                                        string(name: 'combine_data_changelist', value: params.combine_data_changelist ?: params.data_changelist),
                                        booleanParam(name: 'use_combined_bundles_from_network', value: true)
                                    ],
                                    propagate: false,
                                    wait: false
                                )
                            }
                        }
                        
                        echo "Triggering ${downstream_jobs.size()} downstream jobs"
                        parallel(parallel_jobs)
                    } else {
                        echo "No downstream jobs to trigger"
                    }
                }
            }
        }
    }
    
    post {
        always {
            script {
                // Clean up workspace if needed
                echo "Cleaning up workspace..."
                
                // Archive any logs or artifacts
                if (fileExists('*.log')) {
                    archiveArtifacts artifacts: '*.log', allowEmptyArchive: true
                }
            }
        }
        
        success {
            script {
                echo "Combined bundle creation pipeline completed successfully"
                
                // Send success notifications if configured
                def slack_settings = branchfile.standard_jobs_settings?.slack_channel_frosty
                if (slack_settings) {
                    SlackMessageNew(currentBuild, slack_settings, "${project.short_name} - Combined Bundles")
                }
            }
        }
        
        failure {
            script {
                echo "Combined bundle creation pipeline failed"
                
                // Send failure notifications
                def slack_settings = branchfile.standard_jobs_settings?.slack_channel_frosty
                if (slack_settings) {
                    SlackMessageNew(currentBuild, slack_settings, "${project.short_name} - Combined Bundles")
                }
                
                // Report downstream errors
                DownstreamErrorReporting(currentBuild)
            }
        }
    }
}
