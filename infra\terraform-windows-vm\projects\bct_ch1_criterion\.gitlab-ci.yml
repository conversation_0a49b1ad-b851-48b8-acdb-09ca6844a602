#**********************************************
#             BCT CH1 CRITERION
#**********************************************
.default-bct-ch1-criterion-variables:
  extends: .secrets-bct_ch1_criterion
  variables:
    APPLY_PARALLELISM: "5"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ch1_criterion"
    WORKING_DIR: "projects/bct_ch1_criterion"
    VC_HOST: oh-vcenter1.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ch1-criterion.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ch1_criterion.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-ch1-criterion:
  extends: ['.default-bct-ch1-criterion-variables', '.prepare_config']

validate-bct-ch1-criterion:
  extends: ['.default-bct-ch1-criterion-variables', '.validation_steps']

plan-bct-ch1-criterion:
  needs:
    - job: validate-bct-ch1-criterion
    - job: prepare-json-config-bct-ch1-criterion
  extends: ['.default-bct-ch1-criterion-variables', '.plan_steps']

apply-bct-ch1-criterion:
  needs:
    - job: plan-bct-ch1-criterion
    - job: prepare-json-config-bct-ch1-criterion
  extends: ['.default-bct-ch1-criterion-variables', '.apply_steps']

attache-bct-ch1-criterion:
  needs:
    - job: apply-bct-ch1-criterion
    - job: prepare-json-config-bct-ch1-criterion
  extends: ['.default-bct-ch1-criterion-variables', '.attache_vmdk_step']

sync-bct-ch1-criterion:
  needs:
    - job: attache-bct-ch1-criterion
    - job: prepare-json-config-bct-ch1-criterion
  extends: ['.default-bct-ch1-criterion-variables', '.sync_vmdk_step']

ansible-bct-ch1-criterion:
  needs:
    - job: sync-bct-ch1-criterion
    - job: prepare-json-config-bct-ch1-criterion
  extends: ['.default-bct-ch1-criterion-variables', '.ansible_common_secrets', '.run_ansible_step']
