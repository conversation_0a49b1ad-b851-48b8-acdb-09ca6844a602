#**********************************************
#         bct_ado_ea_battlefield PIPE
#**********************************************
.default-bct-ado-ea-battlefield-variables:
  extends: .secrets-bct_ado_ea_battlefield
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ado_ea_battlefield"
    WORKING_DIR: "projects/bct_ado_ea_battlefield"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct.json"
    ansible_main_module: bct_ea_battlefield_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ado_ea_battlefield.json"
    ANSIBLE_BRANCH: master

prepare-json-config-bct-ado-ea-battlefield:
  extends: ['.default-bct-ado-ea-battlefield-variables', '.prepare_config']

validate-bct-ado-ea-battlefield:
  extends: ['.default-bct-ado-ea-battlefield-variables', '.validation_steps']

plan-bct-ado-ea-battlefield:
  needs:
    - job: validate-bct-ado-ea-battlefield
    - job: prepare-json-config-bct-ado-ea-battlefield
  extends: ['.default-bct-ado-ea-battlefield-variables', '.plan_steps']

apply-bct-ado-ea-battlefield:
  needs:
    - job: plan-bct-ado-ea-battlefield
    - job: prepare-json-config-bct-ado-ea-battlefield
  extends: ['.default-bct-ado-ea-battlefield-variables', '.apply_steps']

attache-bct-ado-ea-battlefield:
  needs:
    - job: apply-bct-ado-ea-battlefield
    - job: prepare-json-config-bct-ado-ea-battlefield
  extends: ['.default-bct-ado-ea-battlefield-variables', '.attache_vmdk_step']

sync-bct-ado-ea-battlefield:
  needs:
    - job: apply-bct-ado-ea-battlefield
    - job: attache-bct-ado-ea-battlefield
    - job: prepare-json-config-bct-ado-ea-battlefield
  extends: ['.default-bct-ado-ea-battlefield-variables', '.sync_vmdk_step']

ansible-bct-ado-ea-battlefield:
  needs:
    - job: apply-bct-ado-ea-battlefield
    - job: sync-bct-ado-ea-battlefield
    - job: prepare-json-config-bct-ado-ea-battlefield
  extends: ['.default-bct-ado-ea-battlefield-variables', '.ansible_common_secrets', '.run_ansible_step']
