#**********************************************
#               bct_autotest_eala PIPE                 *
#**********************************************
.default-bct_autotest_eala-variables:
  extends: .secrets-bct_autotest_eala
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_autotest_eala"
    WORKING_DIR: "projects/bct_autotest_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-bct_autotest_eala.json"
    ansible_main_module: bct_eala_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct_autotest_eala:
  extends: ['.default-bct_autotest_eala-variables', '.prepare_config']

validate-bct_autotest_eala:
  extends: ['.default-bct_autotest_eala-variables', '.validation_steps']

plan-bct_autotest_eala:
  needs:
    - job: validate-bct_autotest_eala
    - job: prepare-json-config-bct_autotest_eala
  extends: ['.default-bct_autotest_eala-variables','.plan_steps']

apply-bct_autotest_eala:
  needs:
    - job: plan-bct_autotest_eala
    - job: prepare-json-config-bct_autotest_eala
  extends: ['.default-bct_autotest_eala-variables','.apply_steps']

attache-bct_autotest_eala:
  needs:
    - job: apply-bct_autotest_eala
    - job: prepare-json-config-bct_autotest_eala
  extends: ['.default-bct_autotest_eala-variables','.attache_vmdk_step']

sync-bct_autotest_eala:
  needs:
    - job: apply-bct_autotest_eala
    - job: attache-bct_autotest_eala
    - job: prepare-json-config-bct_autotest_eala
  extends: ['.default-bct_autotest_eala-variables','.sync_vmdk_step']

ansible-bct_autotest_eala:
  needs:
    - job: apply-bct_autotest_eala
    - job: sync-bct_autotest_eala
    - job: prepare-json-config-bct_autotest_eala
  extends: ['.default-bct_autotest_eala-variables', '.ansible_common_secrets', '.run_ansible_step']
