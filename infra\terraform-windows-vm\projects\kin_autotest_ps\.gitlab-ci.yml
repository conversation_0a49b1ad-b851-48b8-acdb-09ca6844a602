#**********************************************
#               kin_autotest PIPE
#**********************************************
.default-kin-autotest-ps-variables:
  extends: .secrets-kin_autotest_ps
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "kin_autotest_ps"
    WORKING_DIR: "projects/kin_autotest_ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-kin-autotest-ps.json"
    ansible_main_module: kingston_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "kingston_ps.json"

prepare-json-config-kin-autotest-ps:
  extends: ['.default-kin-autotest-ps-variables', '.prepare_config']

validate-kin-autotest-ps:
  extends: ['.default-kin-autotest-ps-variables', '.validation_steps']

plan-kin-autotest-ps:
  needs:
    - job: validate-kin-autotest-ps
    - job: prepare-json-config-kin-autotest-ps
  extends: ['.default-kin-autotest-ps-variables','.plan_steps']

apply-kin-autotest-ps:
  needs:
    - job: plan-kin-autotest-ps
    - job: prepare-json-config-kin-autotest-ps
  extends: ['.default-kin-autotest-ps-variables','.apply_steps']

attache-kin-autotest-ps:
  needs:
    - job: apply-kin-autotest-ps
    - job: prepare-json-config-kin-autotest-ps
  extends: ['.default-kin-autotest-ps-variables','.attache_vmdk_step']

sync-kin-autotest-ps:
  needs:
    - job: apply-kin-autotest-ps
    - job: attache-kin-autotest-ps
    - job: prepare-json-config-kin-autotest-ps
  extends: ['.default-kin-autotest-ps-variables','.sync_vmdk_step']

ansible-kin-autotest-ps:
  needs:
    - job: apply-kin-autotest-ps
    - job: sync-kin-autotest-ps
    - job: prepare-json-config-kin-autotest-ps
  extends: ['.default-kin-autotest-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
