{"version": "0.2.0", "configurations": [{"name": "Debug Current Python File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {}, "args": []}, {"name": "Debug pytest - Current File", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["${file}", "-v", "-s"], "console": "integratedTerminal", "justMyCode": false, "cwd": "${workspaceFolder}"}, {"name": "Debug pytest - All Tests", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "-s"], "console": "integratedTerminal", "justMyCode": false, "cwd": "${workspaceFolder}"}, {"name": "Debug pytest - Specific Test", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["${workspaceFolder}/tests/", "-k", "${input:testName}", "-v", "-s"], "console": "integratedTerminal", "justMyCode": false, "cwd": "${workspaceFolder}"}, {"name": "Debug pytest - With Coverage", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["--cov=.", "--cov-report=html", "-v", "-s"], "console": "integratedTerminal", "justMyCode": false, "cwd": "${workspaceFolder}"}], "inputs": [{"id": "testName", "description": "Test name pattern", "default": "test_", "type": "promptString"}]}