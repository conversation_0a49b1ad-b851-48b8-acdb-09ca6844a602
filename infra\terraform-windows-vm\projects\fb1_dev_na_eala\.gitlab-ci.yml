#**********************************************
#               fb1_dev_na_eala PIPE                 *
#**********************************************
.default-fb1-dev-na-eala-variables:
  extends: .secrets-fb1_dev_na_eala
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "fb1_dev_na_eala"
    WORKING_DIR: "projects/fb1_dev_na_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-fb1_dev_na_eala.json"
    ansible_main_module: fb_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1_dev_na_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-fb1-dev-na-eala:
  extends: ['.default-fb1-dev-na-eala-variables', '.prepare_config']

validate-fb1-dev-na-eala:
  extends: ['.default-fb1-dev-na-eala-variables', '.validation_steps']

plan-fb1-dev-na-eala:
  needs:
    - job: validate-fb1-dev-na-eala
    - job: prepare-json-config-fb1-dev-na-eala
  extends: ['.default-fb1-dev-na-eala-variables','.plan_steps']

apply-fb1-dev-na-eala:
  needs:
    - job: plan-fb1-dev-na-eala
    - job: prepare-json-config-fb1-dev-na-eala
  extends: ['.default-fb1-dev-na-eala-variables','.apply_steps']

attache-fb1-dev-na-eala:
  needs:
    - job: apply-fb1-dev-na-eala
    - job: prepare-json-config-fb1-dev-na-eala
  extends: ['.default-fb1-dev-na-eala-variables','.attache_vmdk_step']

sync-fb1-dev-na-eala:
  needs:
    - job: apply-fb1-dev-na-eala
    - job: attache-fb1-dev-na-eala
    - job: prepare-json-config-fb1-dev-na-eala
  extends: ['.default-fb1-dev-na-eala-variables','.sync_vmdk_step']

ansible-fb1-dev-na-eala:
  needs:
    - job: apply-fb1-dev-na-eala
    - job: sync-fb1-dev-na-eala
    - job: prepare-json-config-fb1-dev-na-eala
  extends: ['.default-fb1-dev-na-eala-variables', '.ansible_common_secrets', '.run_ansible_step']
