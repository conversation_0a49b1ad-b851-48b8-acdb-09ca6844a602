#**********************************************
#               KINGSTON PS PIPE              *
#**********************************************
.default-kin-ps-variables:
  extends: .secrets-kingston_ps
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "kingston_ps"
    WORKING_DIR: "projects/kingston_ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-kin-ps.json"
    ansible_main_module: kingston_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "kingston_ps.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-kin-ps:
  extends: ['.default-kin-ps-variables', '.prepare_config']

validate-kin-ps:
  extends: ['.default-kin-ps-variables', '.validation_steps']

plan-kin-ps:
  needs:
    - job: validate-kin-ps
    - job: prepare-json-config-kin-ps
  extends: ['.default-kin-ps-variables','.plan_steps']

apply-kin-ps:
  needs:
    - job: plan-kin-ps
    - job: prepare-json-config-kin-ps
  extends: ['.default-kin-ps-variables','.apply_steps']

attache-kin-ps:
  needs:
    - job: apply-kin-ps
    - job: prepare-json-config-kin-ps
  extends: ['.default-kin-ps-variables','.attache_vmdk_step']

sync-kin-ps:
  needs:
    - job: apply-kin-ps
    - job: attache-kin-ps
    - job: prepare-json-config-kin-ps
  extends: ['.default-kin-ps-variables','.sync_vmdk_step']

ansible-kin-ps:
  needs:
    - job: apply-kin-ps
    - job: sync-kin-ps
    - job: prepare-json-config-kin-ps
  extends: ['.default-kin-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
