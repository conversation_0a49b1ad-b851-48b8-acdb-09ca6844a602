#**********************************************
#                  bct_ch1_dev_ps02 PIPE
#**********************************************
.default-bct-ch1-dev-ps02-variables:
  extends: .secrets-bct_ch1_dev_ps02
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ch1_dev_ps02"
    WORKING_DIR: "projects/bct_ch1_dev_ps02"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ch1-dev-ps02.json"
    ansible_main_module: bct_ch1_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ch1_PS02.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-ch1-dev-ps02:
  extends: ['.default-bct-ch1-dev-ps02-variables', '.prepare_config']

validate-bct-ch1-dev-ps02:
  extends: ['.default-bct-ch1-dev-ps02-variables', '.validation_steps']

plan-bct-ch1-dev-ps02:
  needs:
    - job: validate-bct-ch1-dev-ps02
    - job: prepare-json-config-bct-ch1-dev-ps02
  extends: ['.default-bct-ch1-dev-ps02-variables', '.plan_steps']

apply-bct-ch1-dev-ps02:
  needs:
    - job: plan-bct-ch1-dev-ps02
    - job: prepare-json-config-bct-ch1-dev-ps02
  extends: ['.default-bct-ch1-dev-ps02-variables', '.apply_steps']

attache-bct-ch1-dev-ps02:
  needs:
    - job: apply-bct-ch1-dev-ps02
    - job: prepare-json-config-bct-ch1-dev-ps02
  extends: ['.default-bct-ch1-dev-ps02-variables', '.attache_vmdk_step']

sync-bct-ch1-dev-ps02:
  needs:
    - job: apply-bct-ch1-dev-ps02
    - job: attache-bct-ch1-dev-ps02
    - job: prepare-json-config-bct-ch1-dev-ps02
  extends: ['.default-bct-ch1-dev-ps02-variables', '.sync_vmdk_step']

ansible-bct-ch1-dev-ps02:
  needs:
    - job: apply-bct-ch1-dev-ps02
    - job: sync-bct-ch1-dev-ps02
    - job: prepare-json-config-bct-ch1-dev-ps02
  extends: ['.default-bct-ch1-dev-ps02-variables', '.ansible_common_secrets', '.run_ansible_step']
