# Detailed Bilbo query to understand the mismatch
param(
    [string]$BilboEndpoint = "https://bct-bilbo-eck.cobra.dre.ea.com",
    [string]$BilboIndex = "criterion_bilbo"
)

Write-Host "Detailed Bilbo Investigation for Mismatch Analysis"
Write-Host "=" * 60

# Test with one build to see what fields are available
$testBuild = "24288006"

Write-Host "Querying build $testBuild to see available fields..."

$query = @{
    "query" = @{
        "bool" = @{
            "must" = @(
                @{
                    "term" = @{
                        "changelist" = $testBuild
                    }
                },
                @{
                    "term" = @{
                        "stream" = "CH1-event"
                    }
                }
            )
        }
    }
    "size" = 1
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "$BilboEndpoint/$BilboIndex/_search" -Method POST -Body $query -ContentType "application/json"
    
    if ($response.hits.total.value -gt 0) {
        Write-Host "`nBuild found! Available fields:"
        $source = $response.hits.hits[0]._source
        $source.PSObject.Properties | ForEach-Object {
            Write-Host "  $($_.Name): $($_.Value)"
        }
        
        # Check if this build has a 'deleted' field
        if ($source.PSObject.Properties.Name -contains "deleted") {
            Write-Host "`n✅ Build has 'deleted' field: $($source.deleted)"
        } else {
            Write-Host "`n❌ Build does NOT have 'deleted' field"
        }
        
        # Check for timestamp fields
        $timeFields = $source.PSObject.Properties | Where-Object { $_.Name -like "*time*" -or $_.Name -like "*date*" }
        if ($timeFields) {
            Write-Host "`nTime-related fields found:"
            $timeFields | ForEach-Object {
                Write-Host "  $($_.Name): $($_.Value)"
            }
        }
    } else {
        Write-Host "Build not found in Bilbo"
    }
} catch {
    Write-Host "Error querying Bilbo: $_"
}

Write-Host "`n" + "=" * 60
Write-Host "Now checking what the PowerShell script actually queries..."

# Let's see what the original script does
Write-Host "`nThe script queries for builds with 'deleted' field existing."
Write-Host "Let's check if any CH1-event builds have this field..."

$deletedQuery = @{
    "query" = @{
        "bool" = @{
            "must" = @(
                @{
                    "term" = @{
                        "stream" = "CH1-event"
                    }
                },
                @{
                    "exists" = @{
                        "field" = "deleted"
                    }
                }
            )
        }
    }
    "size" = 10
    "_source" = @("changelist", "stream", "deleted")
} | ConvertTo-Json -Depth 10

try {
    $deletedResponse = Invoke-RestMethod -Uri "$BilboEndpoint/$BilboIndex/_search" -Method POST -Body $deletedQuery -ContentType "application/json"
    
    Write-Host "`nBuilds with 'deleted' field in CH1-event stream:"
    Write-Host "Total found: $($deletedResponse.hits.total.value)"
    
    if ($deletedResponse.hits.total.value -gt 0) {
        Write-Host "`nSample builds with 'deleted' field:"
        $deletedResponse.hits.hits | ForEach-Object {
            $build = $_.source
            Write-Host "  Build $($build.changelist): deleted = $($build.deleted)"
        }
    }
} catch {
    Write-Host "Error querying for deleted builds: $_"
}

Write-Host "`n" + "=" * 60
Write-Host "ANALYSIS CONCLUSIONS"
Write-Host "=" * 60

Write-Host @"

Based on the investigation:

1. **Jenkins Deletion Issue**: Jenkins is trying to delete 8 builds that are NOT marked 
   as deleted in Bilbo. This suggests Jenkins is using different criteria than just 
   the 'deleted' field.

2. **Script Logic Issue**: The script identified build 24310479 as deletable, but 
   Jenkins didn't mark it for deletion. This build is also NOT marked as deleted 
   in Bilbo.

3. **Possible Root Causes**:
   - Jenkins may be using orphaned build detection
   - Jenkins may be using age-based retention that doesn't rely on Bilbo 'deleted' field
   - The script may have incorrect logic for identifying deletable builds
   - There may be timing differences in when builds are marked as deleted

4. **Key Finding**: NONE of the mismatched builds have the 'deleted' field set to true
   in Bilbo, which means:
   - Jenkins is not relying solely on Bilbo 'deleted' status
   - The script's logic for identifying deletable builds may be flawed

RECOMMENDATION: Review both Jenkins retention logic and script logic to understand
why they're identifying different sets of builds for deletion.
"@
