package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_SP_stage {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map code_settings = [
        extra_code_args              : [' --framework-args -traceprop:package.Core.CombinedImageNamePrefix'],
        fake_ooa_wrapped_symbol      : false,
        run_code_unittests           : false,
        skip_code_build_if_no_changes: true,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        smoke_cl_after_success       : false,
        statebuild_code              : true, // TODO: Change to false when we have dedicated machines.
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        deployment_data_branch       : true,
        deployment_data_reference_job: 'CH1-SP-stage.data.start',
        enable_daily_data_clean      : true,
        enable_lkg_cleaning          : true,
        poolbuild_data               : true, // TODO: Remove (i.e. use default false) when we have dedicated machines.
        skip_standalone_patchdata    : true,
        slack_channel_patchdata      : [
            channels                  : ['#bct-build-notify', '#bf-ch1-release-notify'],
            skip_for_multiple_failures: true,
        ],
        // statebuild_data              : false, // TODO: Activate when we have dedicated machines.
        timeout_hours_data           : 6,
    ]
    static Map frosty_settings = [
        frosty_reference_job: 'CH1-SP-stage.deployment-data.start',
        poolbuild_frosty    : true, // TODO: Remove (i.e. use default false) when we have dedicated machines.
        // statebuild_frosty              : false, // TODO: Activate when we have dedicated machines.
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                              : 'CombinedShippingSPLevels',
        baseline_set                       : false,
        bilbo_store_offsite                : true,
        clean_data_validation_pipeline_args: ' --disable-caches true',
        combine_bundles                    : [
            is_target_branch: false,
        ],
        enable_clean_build_validation      : false,
        enable_lkg_p4_counters             : true,
        extra_data_args                    : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args                  : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        import_avalanche_autotest          : false,
        linux_docker_images                : false,
        move_location_parallel             : true,
        new_locations                      : [
            Guildford   : [
                elipy_call_new_location: project.elipy_call_criterion + ' --use-fbenv-core',
            ],
            Montreal    : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        offsite_basic_drone_zip_builds     : true,
        offsite_drone_basic_builds         : true,
        quickscope_db                      : 'kinpipeline',
        quickscope_import                  : true,
        reshift_offsitedrone               : true,
        server_asset                       : 'Game/Setup/Build/DevMPLevels',
        single_stream_smoke                : true,
        skip_icepick_settings_file         : true,
        strip_symbols                      : false,
        timeout_hours_clean_data_validation: 20,
        upgrade_data_job                   : true,
        use_deprecated_blox_packages       : true,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'ps5', configs: ['final', 'retail']],
        [name: 'xbsx', configs: ['final', 'retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.bfdata.upgrade.data', args: ['code_changelist'], trigger_only_on_new_code: true],
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.patchdata.start', args: []],
        [name: '.register.smoke', args: ['code_changelist']],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
    ]
    static List patchdata_downstream_matrix = [
        [name: 'CH1-stage.frosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
