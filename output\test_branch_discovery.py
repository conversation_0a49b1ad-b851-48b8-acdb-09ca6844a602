#!/usr/bin/env python3
"""
Test script to debug branch discovery logic for the deleter.py issue.
This script simulates the branch discovery logic to understand why ch1-event is not being found.
"""

import os
import sys

def _get_disk_branches_for_path(path):
    """
    Get branch names from disk based on known path patterns:
    - path/code/branch/CL
    - path/frosty/BattlefieldGame/branch/CL1/branch/CL2
    - path/webexport/version/branch/CL1_CL2
    """
    branches = set()

    try:
        path_lower = path.lower()
        print(f"Scanning path: {path}")
        print(f"Path lower: {path_lower}")

        if "frosty" in path_lower:
            print("Detected frosty path - scanning direct subdirectories as branches")
            # For frosty paths: scan direct subdirectories as branches
            # Structure: frosty/BattlefieldGame/branch/CL1/branch/CL2
            try:
                items = os.listdir(path)
                print(f"Found {len(items)} items in directory: {items}")
            except (OSError, PermissionError) as exc:
                print(f"Error listing directory {path}: {exc}")
                return branches
                
            for item in items:
                item_path = os.path.join(path, item)
                print(f"Checking item: {item} at path: {item_path}")
                
                if os.path.isdir(item_path):
                    print(f"  {item} is a directory")
                    # Check if this directory contains CL subdirectories (indicating it's a branch)
                    try:
                        subitems = os.listdir(item_path)
                        print(f"  Found {len(subitems)} subitems: {subitems}")
                        
                        # Look for numeric directories (CLs) to confirm this is a branch directory
                        cl_dirs = []
                        for subitem in subitems:
                            subitem_path = os.path.join(item_path, subitem)
                            if os.path.isdir(subitem_path) and subitem.isdigit() and len(subitem) >= 7:
                                cl_dirs.append(subitem)
                        
                        has_cl_dirs = len(cl_dirs) > 0
                        print(f"  CL directories found: {cl_dirs}")
                        print(f"  Has CL dirs: {has_cl_dirs}")
                        
                        if has_cl_dirs:
                            branches.add(item)
                            print(f"  Added {item} as a branch")
                        else:
                            print(f"  {item} not added - no CL directories found")
                    except (OSError, PermissionError) as exc:
                        print(f"  Error reading directory {item_path}: {exc}")
                        # If we can't read the directory, still consider it a potential branch
                        branches.add(item)
                        print(f"  Added {item} as a branch (fallback due to permission error)")
                else:
                    print(f"  {item} is not a directory")

        elif "code" in path_lower:
            print("Detected code path")
            # For code paths: direct subdirectories are branches
            # Structure: code/branch/CL
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    branches.add(item)

        elif "webexport" in path_lower:
            print("Detected webexport path")
            # For webexport paths: need to go one level deeper
            # Structure: webexport/version/branch/CL1_CL2
            for version_dir in os.listdir(path):
                version_path = os.path.join(path, version_dir)
                if os.path.isdir(version_path):
                    try:
                        for branch_dir in os.listdir(version_path):
                            branch_path = os.path.join(version_path, branch_dir)
                            if os.path.isdir(branch_path):
                                branches.add(branch_dir)
                    except (OSError, PermissionError):
                        continue

        else:
            print("Unknown path type - assuming direct subdirectories are branches")
            # For unknown path types: assume direct subdirectories are branches
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    branches.add(item)

    except (OSError, PermissionError) as exc:
        print(f"Error scanning disk for branches under {path}: {exc}")

    return branches


def test_branch_discovery():
    """Test the branch discovery logic"""
    # Test path from the Jenkins log
    test_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    print("=" * 80)
    print("Testing branch discovery logic")
    print("=" * 80)
    
    branches = _get_disk_branches_for_path(test_path)
    
    print("\n" + "=" * 80)
    print("RESULTS:")
    print("=" * 80)
    print(f"Discovered branches: {sorted(branches)}")
    print(f"Total branches found: {len(branches)}")
    
    # Check if ch1-event is in the results
    if "ch1-event" in branches:
        print("✓ ch1-event branch was discovered")
    else:
        print("✗ ch1-event branch was NOT discovered")
        
    # Check other expected branches
    expected_branches = ["ch1-event-release", "ch1-qol", "ch1-sp-release", "ch1-release"]
    for branch in expected_branches:
        if branch in branches:
            print(f"✓ {branch} branch was discovered")
        else:
            print(f"✗ {branch} branch was NOT discovered")


if __name__ == "__main__":
    test_branch_discovery()
