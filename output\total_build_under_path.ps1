param(
    [Parameter(Mandatory=$true, Position=0)]
    [string]$Path,
    
    [Parameter(Mandatory=$true, Position=1)]
    [string]$ElasticSearchUrl,
    
    [Parameter(Mandatory=$true, Position=2)]
    [string]$IndexName,
    
    [Parameter(Mandatory=$false)]
    [int]$Depth = 2,
    
    [Parameter(Mandatory=$false)]
    [switch]$Delete
)

$path = $Path
$elasticSearchUrl = $ElasticSearchUrl
$indexName = $IndexName
$depth = $Depth
$deleteFlag = $Delete
$level1Count = 0
$level2PlusCount = 0
$deletedCount = 0
$notDeletedCount = 0
$notFoundCount = 0
$actuallyDeletedCount = 0

if ($deleteFlag) {
    Write-Output "Script started with -Delete flag at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
}

# Function to delete folder using robocopy method (like expire.py)
function Remove-FolderWithRobocopy {
    param(
        [string]$FolderPath
    )
    
    $emptyFolder = "C:\empty_folder"
    
    # Create empty folder if it doesn't exist
    if (-not (Test-Path $emptyFolder)) {
        try {
            New-Item -ItemType Directory -Path $emptyFolder -Force | Out-Null
        }
        catch {
            # Directory might already exist due to race conditions
        }
    }
    
    try {
        Write-Host "Deleting build: $FolderPath" -ForegroundColor Yellow
        
        # Use robocopy to purge the folder (similar to expire.py)
        $robocopyArgs = @(
            $emptyFolder,
            $FolderPath,
            "/PURGE",
            "/MT:200",
            "/NP",
            "/W:3",
            "/R:3",
            "/NFL",
            "/NDL", 
            "/NJH",
            "/NJS",
            "/NS", 
            "/NC",
            "/NP",
            "/LOG:NUL"
        )
        
        & robocopy @robocopyArgs | Out-Null
        
        # Remove the empty folder left behind
        try {
            Remove-Item -Path $FolderPath -Force -ErrorAction Stop
        }
        catch [System.IO.DirectoryNotFoundException] {
            # Expected - folder already doesn't exist
        }
        catch {
            Write-Warning "Expected folder at path $FolderPath to be empty but it was not: $_"
        }
        
        return $true
    }
    catch {
        Write-Error "Failed to delete folder $FolderPath : $_"
        return $false
    }
}

# Function to query ElasticSearch for build deletion status
function Get-BuildDeletionStatus {
    param(
        [string]$fullPath,
        [string]$esUrl,
        [string]$index
    )
    
    try {
        $searchUrl = "$esUrl/$index/_search"
        
        # Use match query on source field since it's working reliably
        $body = @{
            query = @{
                match = @{
                    "source" = $fullPath
                }
            }
            size = 1
        } | ConvertTo-Json -Depth 3
        
        $response = Invoke-RestMethod -Uri $searchUrl -Method Post -Body $body -ContentType "application/json" -ErrorAction Stop
        
        if ($response.hits.total -gt 0) {
            $hit = $response.hits.hits[0]
            $deletedField = $hit._source.deleted
            # Check if deleted field exists and is not null/empty
            $isDeleted = ($null -ne $deletedField -and $deletedField -ne "")
            
            return @{
                Found = $true
                Deleted = $isDeleted
                DeletedField = $deletedField
                Source = $hit._source
            }
        } else {
            return @{
                Found = $false
                Deleted = $null
                DeletedField = $null
                Source = $null
            }
        }
    }
    catch {
        Write-Warning "Error querying ElasticSearch for path $fullPath : $($_.Exception.Message)"
        return @{
            Found = $false
            Deleted = $null
            DeletedField = $null
            Source = $null
            Error = $_.Exception.Message
        }
    }
}

try {
    Get-ChildItem -Path $path -Directory -Recurse -Depth $depth -ErrorAction Stop | 
        ForEach-Object { 
            if ($_.Name -match '^\d+$') {  # Check if directory name is only digits
                # Calculate the level by counting path separators relative to base path
                $relativePath = $_.FullName.Substring($path.Length).TrimStart('\')
                $level = ($relativePath -split '\\').Count
                
                # Use the full path for ElasticSearch query (only for level 2+ directories)
                if ($level -eq 1) {
                    $level1Count++
                    # Don't output level 1 directories (these should be removed)
                    # Don't query ElasticSearch for level 1 directories
                } else {
                    $level2PlusCount++
                      $fullPathForES = $_.FullName
                    $esResult = Get-BuildDeletionStatus -fullPath $fullPathForES -esUrl $elasticSearchUrl -index $indexName
                    
                    # Determine status for reporting
                    $status = "UNKNOWN"
                    if ($esResult.Found) {
                        if ($esResult.Deleted -eq $true) {
                            $status = "DELETED"
                            $deletedCount++
                            
                            # If delete flag is set, actually delete the folder
                            if ($deleteFlag) {
                                $deleteSuccess = Remove-FolderWithRobocopy -FolderPath $_.FullName
                                if ($deleteSuccess) {
                                    $actuallyDeletedCount++
                                    $status = "DELETED_FROM_DISK"
                                } else {
                                    $status = "DELETED_FAILED"
                                }
                                Write-Output "Deletion finished for $($_.FullName) at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
                            }
                        } else {
                            $status = "NOT_DELETED"
                            $notDeletedCount++
                        }
                    } else {
                        $status = "NOT_FOUND_IN_ES"
                        $notFoundCount++
                    }
                    
                    # Show deletion timestamp if available
                    $deletionInfo = if ($esResult.DeletedField) { " (Deleted: $($esResult.DeletedField))" } else { "" }
                    Write-Output "$($_.FullName) | ES Status: $status$deletionInfo"
                    
                    # Debug ES response if there's an error
                    if ($esResult.Error) {
                        Write-Host "ES Error: $($esResult.Error)" -ForegroundColor Red
                    }
                }
            }
        }
    
    Write-Output ""
    Write-Output "=== SUMMARY ==="
    Write-Output "Level 1 directories (to be removed): $level1Count"
    Write-Output "Level 2+ directories (nested builds): $level2PlusCount"
    Write-Output "Total number of directories: $($level1Count + $level2PlusCount)"
    Write-Output ""
    Write-Output "=== ELASTICSEARCH STATUS ==="
    Write-Output "Builds marked as DELETED in ES: $deletedCount"
    Write-Output "Builds marked as NOT_DELETED in ES: $notDeletedCount"
    Write-Output "Builds NOT_FOUND in ES: $notFoundCount"
    
    if ($deleteFlag) {
        Write-Output ""
        Write-Output "=== DELETION SUMMARY ==="
        Write-Output "Builds actually deleted from disk: $actuallyDeletedCount"
        Write-Output "Failed deletions: $($deletedCount - $actuallyDeletedCount)"
    }
}
catch {
    Write-Output "Error accessing path: $_"
}
