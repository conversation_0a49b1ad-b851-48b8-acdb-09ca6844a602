package scripts.schedulers.data

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import jenkins.model.Jenkins

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * patchdata_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                    P4PreviewData(project, 'stream', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                }
            }
        }
        stage('Trigger patchdata jobs') {
            steps {
                script {
                    def code_changelist = ''
                    def data_changelist = ''
                    def last_good_code = ''
                    def last_good_data = ''

                    def use_dynamic_disc_baselines = branchfile.standard_jobs_settings.use_dynamic_disc_baselines ?: false

                    if (env.data_triggers_patchdata.toBoolean() == true) {
                        def data_job = env.branch_name + '.data.start'
                        last_good_code = Jenkins.get().getItem(data_job).lastStableBuild?.getEnvironment(TaskListener.NULL)?.code_changelist
                        last_good_data = Jenkins.get().getItem(data_job).lastStableBuild?.getEnvironment(TaskListener.NULL)?.data_changelist
                        code_changelist = params.code_changelist ?: last_good_code
                        data_changelist = params.data_changelist ?: last_good_data
                    } else {
                        last_good_code = LibJenkins.getLastStableCodeChangelist(env.data_reference_job)
                        code_changelist = params.code_changelist ?: last_good_code
                        data_changelist = params.data_changelist ?: env.P4_CHANGELIST
                        if (env.frostbite_syncer_setup.toBoolean() == true) {
                            data_changelist = params.data_changelist ?: last_good_code
                        }
                    }

                    def clean_data = (
                        LibCommonCps.isRecentCleanBuildByTime(currentBuild, 1).toBoolean() == false &&
                            env.enable_daily_data_clean.toBoolean() == true
                    ) ? 'True' : params.clean_data

                    if (code_changelist == null) {
                        echo 'Missing code changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def patchdata_matrix = branchfile.patchdata_matrix

                    def final_result = Result.SUCCESS
                    def continue_build = true
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in patchdata_matrix) {
                            Boolean allow_failure = false
                            def platform_name = platform
                            Boolean standalone_export_platform = true
                            if (platform instanceof Map) {
                                allow_failure = platform.allow_failure ?: false
                                platform_name = platform.name
                                standalone_export_platform = platform.standalone_export_platform != null ? platform.standalone_export_platform : true
                            }

                            def modifiers = [platform_name]
                            def first_patch = LibCommonNonCps.get_setting_value(branchfile.standard_jobs_settings, modifiers, 'first_patch', false)
                            def baseline_set = LibCommonNonCps.get_setting_value(branchfile.standard_jobs_settings, modifiers, 'baseline_set', true)

                            def platform_args = args
                            if (baseline_set) {
                                baseline_file = ReturnBaselineFile(project.name)
                                platform_args += ReturnDiscBaseline(baseline_file, env.patch_branch, platform_name)
                                if (use_dynamic_disc_baselines) {
                                    //Fetch code/data CLs from the store regular baseline job
                                    def fetch_baseline_reference_job = branchfile.standard_jobs_settings.fetch_baseline_reference_job
                                    def last_successful_code_build = LibJenkins.getLastStableCodeChangelist(fetch_baseline_reference_job)
                                    def last_successful_data_build = LibJenkins.getLastStableDataChangelist(fetch_baseline_reference_job)

                                    platform_args += [
                                        string(name: 'disc_code_changelist', value: last_successful_code_build),
                                        string(name: 'disc_data_changelist', value: last_successful_data_build),
                                    ]
                                }
                                if (first_patch == false) {
                                    platform_args += ReturnPatchBaseline(baseline_file, env.patch_branch, platform_name)
                                }
                            }

                            if (!branchfile.standard_jobs_settings.skip_standalone_patchdata && standalone_export_platform) {
                                def job_name = env.branch_name + '.patchdata.' + env.dataset + '.' + platform_name
                                if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                                    if (run > 0 && IsGameFailure(job_name)) {
                                        if (allow_failure == false) {
                                            final_result = Result.FAILURE
                                            // Set pipeline as failed if there are jobs from IsGameFailure category.
                                            continue_build = false
                                        }
                                        break
                                    } else {
                                        jobs[job_name] = {
                                            def downstream_job = build(job: job_name, parameters: platform_args, propagate: false)
                                            if (allow_failure == false) {
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                            }
                                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                            LibJenkins.printRunningJobs(this)
                                        }
                                    }
                                }
                            }
                            def combine_bundles = branchfile.standard_jobs_settings.combine_bundles
                            def combine_platform = true
                            if (platform instanceof Map) {
                                combine_platform = platform.combine_platform != null ? platform.combine_platform : true
                            }
                            if (combine_bundles && combine_platform) {
                                def job_name_combine = env.branch_name + '.patchdata-combine.' + env.dataset + '.' + platform_name
                                if (NeedsRebuildData(job_name_combine, code_changelist, data_changelist)) {
                                    if (run > 0 && IsGameFailure(job_name_combine)) {
                                        if (allow_failure == false) {
                                            final_result = Result.FAILURE
                                            // Set pipeline as failed if there are jobs from IsGameFailure category.
                                            continue_build = false
                                        }
                                        break
                                    } else {
                                        jobs[job_name_combine] = {
                                            def downstream_job = build(job: job_name_combine, parameters: platform_args, propagate: false)
                                            if (allow_failure == false) {
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                            }
                                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                            LibJenkins.printRunningJobs(this)
                                        }
                                    }
                                }
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }
                    currentBuild.result = final_result.toString()

                    if (currentBuild.result.toString() == 'SUCCESS') { // Only trigger after successful builds
                        // Update P4 counters value after data job
                        if (env.enable_lkg_p4_counters.toBoolean() == true) {
                            //TODO: p4 counter -m 'dice'.${env.branch_name}.lkg.data.code ${code_changelist} 'dice'.${env.branch_name}.lkg.data.data ${data_changelist}
                            //e.g: dice.dev-na-battlefieldgame.lkg.data.code <code_changelist> dice.dev-na-battlefieldgame.lkg.data.data <data_changelist>
                            def counter_args = [string(name: 'code_changelist', value: code_changelist)]
                            if (env.no_lkg_patch_data?.toBoolean() == false) {
                                echo 'Trigger downstream job to update p4 counter on data job:'
                                counter_args += [
                                    string(name: 'code_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.data', branchfile, 'code')),
                                    string(name: 'data_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.data', branchfile, 'data')),
                                    string(name: 'data_changelist', value: data_changelist),
                                ]
                            } else {
                                echo 'Trigger downstream job to update p4 counter on data job:'
                                counter_args += [
                                    string(name: 'code_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.patchdata', branchfile, 'code')),
                                    string(name: 'data_countername', value: ''),
                                    string(name: 'data_changelist', value: ''),
                                ]
                            }
                            build(job: env.branch_name + '.data.p4counterupdater', parameters: counter_args, propagate: false)
                        }
                        // Update P4 counters value for clean data job
                        if (env.enable_lkg_cleaning.toBoolean() && clean_data.toBoolean()) {
                            echo 'Trigger downstream job to update clean p4 counter on data job:'
                            def clean_counter_args = [
                                string(name: 'code_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.clean.data', branchfile, 'code')),
                                string(name: 'code_changelist', value: code_changelist),
                                string(name: 'data_countername', value: LibCommonCps.getp4Countername(env.branch_name, 'lkg.clean.data', branchfile, 'data')),
                                string(name: 'data_changelist', value: data_changelist),
                            ]
                            build(job: env.branch_name + '.data.p4cleancounterupdater', parameters: clean_counter_args, propagate: false)
                        }

                        def patchdata_downstream_matrix = branchfile.patchdata_downstream_matrix ?: []

                        echo 'Using patchdata_downstream_matrix to trigger downstream jobs'
                        LibCommonCps.triggerDownstreamJobs(this, patchdata_downstream_matrix, 'patchdata', env.branch_name, branchfile, code_changelist, data_changelist)

                        // Trigger bilbo registration for streams with no data builds but move_location_parallel enabled
                        def data_matrix = branchfile.data_matrix ?: []
                        def move_location_parallel = branchfile.standard_jobs_settings?.move_location_parallel ?: false
                        if (data_matrix.isEmpty() && move_location_parallel && (env.dry_run_patchdata?.toBoolean() ?: false) == false) {
                            echo 'Triggering bilbo registration for stream with no data builds but move_location_parallel enabled'
                            def bilbo_args = [
                                string(name: 'code_changelist', value: code_changelist),
                                string(name: 'data_changelist', value: data_changelist),
                            ]
                            build(job: env.branch_name + '.bilbo.register-' + env.dataset + '-dronebuild', parameters: bilbo_args, propagate: false, wait: false)
                        } else {
                            echo 'DEBUG: Bilbo registration NOT triggered - condition failed'
                            echo 'DEBUG: data_matrix.isEmpty() = ${data_matrix.isEmpty()}'
                            echo 'DEBUG: move_location_parallel = ${move_location_parallel}'
                            echo 'DEBUG: env.dry_run_patchdata = ${env.dry_run_patchdata}'
                            echo 'DEBUG: dry_run_patchdata boolean = ${env.dry_run_patchdata?.toBoolean() ?: false}'
                        }
                    }

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_patchdata
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    // Set this build as 'cleaned', if necessary
                    SetOptionalCleanBuild(currentBuild, clean_data)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
