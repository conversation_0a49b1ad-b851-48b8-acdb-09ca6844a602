#**********************************************
#          BCT PS preflights PIPE          *
#**********************************************
.default-bct-ps-preflights-variables:
  extends: .secrets-bct_ps_preflights
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ps_preflights"
    WORKING_DIR: "projects/bct_ps_preflights"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct-ps-preflights.json"
    ansible_main_module: bct_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_PS.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-bct-ps-preflights:
  extends: ['.default-bct-ps-preflights-variables', '.prepare_config']

validate-bct-ps-preflights:
  extends: ['.default-bct-ps-preflights-variables', '.validation_steps']

plan-bct-ps-preflights:
  needs:
    - job: validate-bct-ps-preflights
    - job: prepare-json-config-bct-ps-preflights
  extends: ['.default-bct-ps-preflights-variables','.plan_steps']

apply-bct-ps-preflights:
  needs:
    - job: plan-bct-ps-preflights
    - job: prepare-json-config-bct-ps-preflights
  extends: ['.default-bct-ps-preflights-variables','.apply_steps']

attache-bct-ps-preflights:
  needs:
    - job: apply-bct-ps-preflights
    - job: prepare-json-config-bct-ps-preflights
  extends: ['.default-bct-ps-preflights-variables','.attache_vmdk_step']

sync-bct-ps-preflights:
  needs:
    - job: apply-bct-ps-preflights
    - job: attache-bct-ps-preflights
    - job: prepare-json-config-bct-ps-preflights
  extends: ['.default-bct-ps-preflights-variables','.sync_vmdk_step']

ansible-bct-ps-preflights:
  needs:
    - job: apply-bct-ps-preflights
    - job: sync-bct-ps-preflights
    - job: prepare-json-config-bct-ps-preflights
  extends: ['.default-bct-ps-preflights-variables', '.ansible_common_secrets', '.run_ansible_step']
