#**********************************************
#                  bct_ado PIPE
#**********************************************
.default-bct-ado-variables:
  extends: .secrets-bct_ado
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "bct_ado"
    WORKING_DIR: "projects/bct_ado"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-bct.json"
    ansible_main_module: bct_ado_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "bct_ado_PS.json"
    ANSIBLE_BRANCH: master

prepare-json-config-bct-ado:
  extends: ['.default-bct-ado-variables', '.prepare_config']

validate-bct-ado:
  extends: ['.default-bct-ado-variables', '.validation_steps']

plan-bct-ado:
  needs:
    - job: validate-bct-ado
    - job: prepare-json-config-bct-ado
  extends: ['.default-bct-ado-variables', '.plan_steps']

apply-bct-ado:
  needs:
    - job: plan-bct-ado
    - job: prepare-json-config-bct-ado
  extends: ['.default-bct-ado-variables', '.apply_steps']

attache-bct-ado:
  needs:
    - job: apply-bct-ado
    - job: prepare-json-config-bct-ado
  extends: ['.default-bct-ado-variables', '.attache_vmdk_step']

sync-bct-ado:
  needs:
    - job: apply-bct-ado
    - job: attache-bct-ado
    - job: prepare-json-config-bct-ado
  extends: ['.default-bct-ado-variables', '.sync_vmdk_step']

ansible-bct-ado:
  needs:
    - job: apply-bct-ado
    - job: sync-bct-ado
    - job: prepare-json-config-bct-ado
  extends: ['.default-bct-ado-variables', '.ansible_common_secrets', '.run_ansible_step']
