package com.ea.project.kin.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.jobsettings.ShiftSettings
import com.ea.lib.model.branchsettings.UnitTestsConfiguration

class Testenv {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_install_call      : 'tnt\\bin\\fbcli\\cli.bat x64 && C:\\dev\\ci\\install-elipy.bat elipy_kingston.yml >> D:\\dev\\logs\\install-elipy.log 2>&1',
        elipy_call              : 'ECHO tnt\\bin\\fbcli\\cli.bat x64 elipy --location dice',
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        gametool_settings       : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
            ],
        ],
        unittests               : new UnitTestsConfiguration(),
        coverity_settings       : [run_coverity: true,],
    ]
    static Map frosty_settings = [
        enable_eac_win64_digital        : true,
        enable_eac_win64_steam          : true,
        enable_eac_win64_combine        : true,
        enable_eac_win64_steam_combine  : true,
        slack_channel_frosty            : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty               : false,
        statebuild_patchfrosty          : false,
        timeout_hours_frosty            : 10,
        timeout_hours_frosty_patchfrosty: 10,
        use_linuxclient                 : true,
        combine_settings_file           : 'project-combine-labs.yaml',
        combine_settings_file_xbsx      : 'project-combine-labs-smart-delivery.yaml',
    ]
    static Map standard_jobs_settings = [
        asset                         : 'retaillevels',
        clean_master_version_check    : true,
        clean_local                   : true,
        combine_bundles               : [
            combine_asset        : 'Game/Setup/Build/LabsClientLevels',
            combine_reference_job: 'CH1-SP-release.patchdata.start',
            is_target_branch     : true,
            source_branch_code   : 'CH1-SP-release',
            source_branch_data   : 'CH1-SP-release',
        ],
        data_reference_job            : 'lab.kin-dev.code.start',
        denuvo_wrapping               : true,
        elipy_shift_config            : false,
        enable_clean_build_validation : true,
        marvin_trigger_upload_and_test: false,
        content_layers                : ['Test_Layer1', 'Test_Layer2'],
        deployment_data_branch        : true,
        frosty_orchestrator_trigger   : '#never',
        p4_user_single_slash          : '%USERDOMAIN%\\%USERNAME%',
        offsite_drone_builds          : false,
        shift_branch                  : true,
        shift_reference_job           : 'lab.kin-dev.frosty.start',
        patch_branch                  : 'kin-dev',
        retry_limit                   : 1,
        server_asset                  : 'serverlevels',
        use_recompression_cache       : false,
        user_credentials              : 'svc_kin01',
        environment_variables         : [
            'ASAN_WIN_CONTINUE_ON_INTERCEPTION_FAILURE': 1,
        ],
        autotest_remote_settings      : [
            criterion: [
                p4_code_creds : 'perforce-battlefield-criterion',
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield-criterion',
                p4_data_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
            ],
            dice     : [
                p4_code_creds : 'perforce-p4buildedge02-fb-kingston01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                p4_data_creds : 'perforce-frostbite02-commons', // 'perforce-tunguska-kingston01',
                p4_data_server: 'p4-tunguska-build01.dice.ad.ea.com:2001',
            ]
        ],
        vault_secrets_branch          : [[
                                             vault_secret_path: 'cobra/automation/test',
                                             target_env_var   : 'BRANCH_SECRET',
                                             vault_secret_key : 'key1'
                                         ]],
    ]
    static Map preflight_settings = [
        concurrent_code         : 2,
        concurrent_data         : 4,
        p4_code_server_preflight: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001',
        p4_code_creds_preflight : 'euwest-p4edge-fb.p4one.ea.com',
        enable_hybrid_agents    : true,
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'win64server', configs: ['final', 'release', 'performance']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = [
        [name: 'win64'],
        [name: 'xbsx'],
        [name: 'server'],
    ]
    static List data_downstream_matrix = [
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'steam', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release'],
                                   [format: 'files', config: 'final', region: 'ww', args: ''],
                                   [format: 'steam_combine', config: 'retail', region: 'labs', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = [
        [name: 'ps5', variants: [[format: 'steam', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release'],
                                 [format: 'steam_combine', config: 'retail', region: 'labs', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'labs', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'labs', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'labs', args: '']]],
    ]
    static List patchfrosty_matrix = [
        [name: 'xbsx', variants: [[format: 'steam', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release'],
                                  [format: 'steam_combine', config: 'retail', region: 'labs', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'labs', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'labs', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'labs', args: '']]],
    ]
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = [
        [name: 'win64', platform: 'win64', assets: ['Runmode02Levels'], extra_label: ''],
        [name: 'xb1', platform: 'xb1', assets: ['Runmode02Levels'], extra_label: ''],
        [name: 'ps5', platform: 'ps5', assets: ['Runmode02Levels'], extra_label: ''],
        [name: 'server', platform: 'server', assets: ['Runmode02Levels'], extra_label: ''],
    ]
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]

    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register-kindata-dronebuild', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
    ]
    static List azure_uploads_matrix = []
}
