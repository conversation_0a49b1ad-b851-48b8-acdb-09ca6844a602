package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_playtest_san {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]

    static Map frosty_settings = [
        enable_eac_win64_digital: true,
        enable_eac_win64_steam  : true,
    ]

    static Map standard_jobs_settings = frosty_settings + [
        asset               : 'PlaytestLevelsSantiago',
        frosty_reference_job: 'CH1-content-dev.data.start',
        extra_frosty_args   : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        poolbuild_frosty    : true,
        server_asset        : 'PlaytestLevelsSantiago',
        shift_branch        : true,
        shift_every_build   : false,
        slack_channel_frosty: [channels: ['#bct-build-notify'], skip_for_multiple_failures: true],
        trigger_type_frosty : 'none',
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'playtest', args: '']]],
        [name: 'win64', variants: [
            [format: 'files', config: 'final', region: 'playtest', args: ' --additional-configs performance '],
            [format: 'steam', config: 'performance', region: 'ww', args: ''],
        ]],
        [name: 'ps5', variants: [[format: 'files', config: 'performance', region: 'playtest', args: '', allow_failure: true]]],
        [name: 'xbsx', variants: [[format: 'files', config: 'performance', region: 'playtest', args: '', allow_failure: true]]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.start', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.playtest', args: ['code_changelist', 'data_changelist']],
        [name: '.win64.upload_to_steam.ww.performance', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'playtest']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
