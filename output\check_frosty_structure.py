#!/usr/bin/env python3
"""
Check the actual structure of frosty builds to understand the 4-level hierarchy
"""

import os

def check_frosty_structure():
    """Check the actual structure of frosty builds"""
    base_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    print("=" * 80)
    print("Checking frosty build structure")
    print("=" * 80)
    
    try:
        # Level 1: Branch directories
        level1_items = os.listdir(base_path)
        print(f"Level 1 (branches): {len(level1_items)} items")
        
        for item in level1_items:
            if item.startswith('.'):
                continue
                
            level1_path = os.path.join(base_path, item)
            if not os.path.isdir(level1_path):
                continue
                
            print(f"\nBranch: {item}")
            print(f"  Path: {level1_path}")
            
            try:
                # Level 2: Should be CL1 directories
                level2_items = os.listdir(level1_path)
                print(f"  Level 2 items: {len(level2_items)}")
                
                # Check first few items to understand structure
                for i, level2_item in enumerate(level2_items[:3]):  # Only check first 3
                    if level2_item.startswith('.'):
                        continue
                        
                    level2_path = os.path.join(level1_path, level2_item)
                    if not os.path.isdir(level2_path):
                        print(f"    {level2_item} (file)")
                        continue
                        
                    print(f"    CL1: {level2_item}")
                    
                    try:
                        # Level 3: Should be branch directories again
                        level3_items = os.listdir(level2_path)
                        print(f"      Level 3 items: {len(level3_items)}")
                        
                        for j, level3_item in enumerate(level3_items[:2]):  # Only check first 2
                            if level3_item.startswith('.'):
                                continue
                                
                            level3_path = os.path.join(level2_path, level3_item)
                            if not os.path.isdir(level3_path):
                                print(f"        {level3_item} (file)")
                                continue
                                
                            print(f"        Branch2: {level3_item}")
                            
                            try:
                                # Level 4: Should be CL2 directories (final builds)
                                level4_items = os.listdir(level3_path)
                                print(f"          Level 4 items: {len(level4_items)}")
                                
                                # Check if these are CL directories
                                cl_dirs = [item for item in level4_items 
                                         if os.path.isdir(os.path.join(level3_path, item)) 
                                         and item.isdigit() and len(item) >= 7]
                                
                                print(f"          CL2 directories: {len(cl_dirs)}")
                                if cl_dirs:
                                    print(f"          Sample CL2s: {cl_dirs[:3]}")
                                    
                                    # This would be the actual build path
                                    sample_build = os.path.join(level3_path, cl_dirs[0])
                                    print(f"          Sample build path: {sample_build}")
                                
                            except (OSError, PermissionError) as exc:
                                print(f"          Error reading level 4: {exc}")
                                
                    except (OSError, PermissionError) as exc:
                        print(f"      Error reading level 3: {exc}")
                        
            except (OSError, PermissionError) as exc:
                print(f"  Error reading level 2: {exc}")
                
            # Only check first 3 branches to avoid too much output
            if level1_items.index(item) >= 2:
                print("\n  ... (stopping after 3 branches)")
                break
                
    except (OSError, PermissionError) as exc:
        print(f"Error reading base path: {exc}")

def check_current_scanning_logic():
    """Check what the current scanning logic finds vs the correct 4-level structure"""
    base_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    print("\n" + "=" * 80)
    print("Comparing current logic vs correct 4-level structure")
    print("=" * 80)
    
    # Current logic: looks for branch/CL structure (2 levels)
    print("Current logic (2-level): branch/CL")
    current_branches = set()
    
    try:
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if not os.path.isdir(item_path) or item.startswith('.'):
                continue
                
            # Check if this directory contains CL subdirectories
            try:
                subitems = os.listdir(item_path)
                has_cl_dirs = any(
                    subitem.isdigit() and len(subitem) >= 7
                    for subitem in subitems
                    if os.path.isdir(os.path.join(item_path, subitem))
                )
                if has_cl_dirs:
                    current_branches.add(item)
                    print(f"  Found branch: {item}")
            except (OSError, PermissionError):
                pass
                
    except (OSError, PermissionError) as exc:
        print(f"Error: {exc}")
    
    print(f"Current logic found: {len(current_branches)} branches")
    
    # Correct logic: looks for branch/CL1/branch/CL2 structure (4 levels)
    print("\nCorrect logic (4-level): branch/CL1/branch/CL2")
    correct_branches = set()
    
    try:
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if not os.path.isdir(item_path) or item.startswith('.'):
                continue
                
            # Check if this follows the 4-level structure
            try:
                level2_items = os.listdir(item_path)
                has_4_level_structure = False
                
                for level2_item in level2_items:
                    if level2_item.startswith('.'):
                        continue
                    level2_path = os.path.join(item_path, level2_item)
                    if not os.path.isdir(level2_path):
                        continue
                        
                    # Check if level2 contains branch directories
                    try:
                        level3_items = os.listdir(level2_path)
                        for level3_item in level3_items:
                            if level3_item.startswith('.'):
                                continue
                            level3_path = os.path.join(level2_path, level3_item)
                            if not os.path.isdir(level3_path):
                                continue
                                
                            # Check if level3 contains CL directories
                            try:
                                level4_items = os.listdir(level3_path)
                                has_cl_dirs = any(
                                    subitem.isdigit() and len(subitem) >= 7
                                    for subitem in level4_items
                                    if os.path.isdir(os.path.join(level3_path, subitem))
                                )
                                if has_cl_dirs:
                                    has_4_level_structure = True
                                    break
                            except (OSError, PermissionError):
                                pass
                        if has_4_level_structure:
                            break
                    except (OSError, PermissionError):
                        pass
                
                if has_4_level_structure:
                    correct_branches.add(item)
                    print(f"  Found branch: {item}")
                    
            except (OSError, PermissionError):
                pass
                
    except (OSError, PermissionError) as exc:
        print(f"Error: {exc}")
    
    print(f"Correct logic found: {len(correct_branches)} branches")
    
    # Compare results
    print(f"\nComparison:")
    print(f"Current branches: {sorted(current_branches)}")
    print(f"Correct branches: {sorted(correct_branches)}")
    
    missing_in_current = correct_branches - current_branches
    extra_in_current = current_branches - correct_branches
    
    if missing_in_current:
        print(f"Missing in current logic: {sorted(missing_in_current)}")
    if extra_in_current:
        print(f"Extra in current logic: {sorted(extra_in_current)}")

if __name__ == "__main__":
    check_frosty_structure()
    check_current_scanning_logic()
