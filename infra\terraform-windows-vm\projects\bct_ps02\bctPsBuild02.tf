#*************************************************************
#  Sets up the initial needs to point to our vSphere server
#*************************************************************
# Point to our datacenter
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# bct_ps, check CONTRIBUTING.md before editing here.
# *************************************************************
# bct_ps - https://bct-dev-jenkins.cobra.dre.ea.com
# *************************************************************
locals {
  module_settings = {
    # EXECUTOR NODES
    "ps_executors_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps02 executor_agent", cpu_core = "5", ram_count = 16384 }

    # STATEBUILD PER BRANCH
    "pool_state_bct_ps_A4_win64_trunk_code_dev_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-code-dev poolbuild_trunk-code-dev win64" }
    "pool_state_bct_ps_A4_win64_trunk_content_dev_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-content-dev poolbuild_trunk-content-dev win64" }
    "pool_state_bct_ps_A4_win64_trunk_to_dev_na_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-to-dev-na poolbuild_trunk-to-dev-na win64" }
    "pool_state_bct_ps_A4_win64_dev_na_to_trunk_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_dev-na-to-trunk poolbuild_dev-na-to-trunk win64" }
    "pool_state_bct_ps_A4_ps5_trunk_code_dev_001"        = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-code-dev poolbuild_trunk-code-dev ps5" }
    "pool_state_bct_ps_A4_ps5_trunk_content_dev_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-content-dev poolbuild_trunk-content-dev ps5" }
    "pool_state_bct_ps_A4_ps5_trunk_to_dev_na_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-to-dev-na poolbuild_trunk-to-dev-na ps5" }
    "pool_state_bct_ps_A4_ps5_dev_na_to_trunk_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_dev-na-to-trunk poolbuild_dev-na-to-trunk ps5" }
    "pool_state_bct_ps_A4_xbsx_trunk_code_dev_001"       = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-code-dev poolbuild_trunk-code-dev xbsx" }
    "pool_state_bct_ps_A4_xbsx_trunk_content_dev_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-content-dev poolbuild_trunk-content-dev xbsx" }
    "pool_state_bct_ps_A4_xbsx_trunk_to_dev_na_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-to-dev-na poolbuild_trunk-to-dev-na xbsx" }
    "pool_state_bct_ps_A4_xbsx_dev_na_to_trunk_001"      = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_dev-na-to-trunk poolbuild_dev-na-to-trunk xbsx" }
    "pool_state_bct_ps_A4_server_trunk_code_dev_001"     = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-code-dev poolbuild_trunk-code-dev server linuxserver" }
    "pool_state_bct_ps_A4_server_trunk_content_dev_001"  = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-content-dev poolbuild_trunk-content-dev server linuxserver" }
    "pool_state_bct_ps_A4_server_trunk_to_dev_na_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-to-dev-na poolbuild_trunk-to-dev-na server linuxserver" }
    "pool_state_bct_ps_A4_server_dev_na_to_trunk_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_dev-na-to-trunk poolbuild_dev-na-to-trunk server linuxserver" }
    "pool_state_bct_ps_A4_linux64_trunk_code_dev_001"    = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-code-dev poolbuild_trunk-code-dev linux64" }
    "pool_state_bct_ps_A4_linux64_trunk_content_dev_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-content-dev poolbuild_trunk-content-dev linux64" }
    "pool_state_bct_ps_A4_linux64_trunk_to_dev_na_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_trunk-to-dev-na poolbuild_trunk-to-dev-na linux64" }
    "pool_state_bct_ps_A4_linux64_dev_na_to_trunk_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-02", vm_count = "1", labels = "ps02 statebuild_dev-na-to-trunk poolbuild_dev-na-to-trunk linux64" }

    # INTEGRATIONS
    "trunk-code-dev-to-trunk-to-dev-na-upgrade1_001"   = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 data-upgrade-trunk-code-dev-to-trunk-to-dev-na-upgrade" }
    "trunk-to-dev-na-to-dev-na-upgrade1_001"           = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 data-upgrade-trunk-to-dev-na-to-dev-na-upgrade" }
    "dev-na-to-trunk-to-dev-na-upgrade1_001"           = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 data-upgrade-dev-na-to-trunk-to-dev-na-upgrade" }
    "trunk-content-dev-to-trunk-code-dev-upgrade1_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-03", vm_count = "1", labels = "ps02 data-upgrade-trunk-content-dev-to-trunk-code-dev-upgrade" }
    "task1-to-trunk-content-dev-upgrade1_001"          = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 data-upgrade-task1-to-trunk-content-dev-upgrade" }
    "trunk-content-dev-to-trunk-code-dev-bg1_001"      = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 trunk-content-dev-to-trunk-code-dev-branch-guardian" }
    "trunk-code-dev-to-trunk-to-dev-na-bg1_001"        = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 trunk-code-dev-to-trunk-to-dev-na-branch-guardian", ram_count = 262144 }
    "task1-to-trunk-content-dev-bg1_001"               = { datastore = "BPS02-A3_DRE-BUILD-VMS-01", vm_count = "1", labels = "ps02 task1-to-trunk-content-dev-branch-guardian" }
  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bct2-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-dev-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DICE-BUILD")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")

  vsphere_template      = var.packer_template
  vsphere_network       = var.bct_network
  vsphere_datacenter    = var.bct_datacenter
  vsphere_folder        = "DICE/dre-terraform-nodes/bct_nodes"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = "dice.ad.ea.com"
  domain_ou             = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
  local_admin_group     = var.local_admin_group
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
