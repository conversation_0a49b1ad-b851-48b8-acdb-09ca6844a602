#**********************************************
#               fb1_test_eala PIPE                 *
#**********************************************
.default-fb1-test-eala-variables:
  extends: .secrets-fb1_test_eala
  variables:
    APPLY_PARALLELISM: "5" #10
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "fb1_test_eala"
    WORKING_DIR: "projects/fb1_test_eala"
    VC_HOST: eala-vcenter.la.ad.ea.com
    NODE_INFO_FILE: "node-info-fb1_test_eala.json"
    ansible_main_module: fb_eala_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "fb1_eala.json"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log

prepare-json-config-fb1-test-eala:
  extends: ['.default-fb1-test-eala-variables', '.prepare_config']
  tags:
    - glaas-shared-k8s

validate-fb1-test-eala:
  extends: ['.default-fb1-test-eala-variables', '.validation_steps']
  tags:
    - glaas-shared-k8s

plan-fb1-test-eala:
  needs:
    - job: validate-fb1-test-eala
    - job: prepare-json-config-fb1-test-eala
  extends: ['.default-fb1-test-eala-variables','.plan_steps']
  tags:
    - glaas-shared-k8s

apply-fb1-test-eala:
  needs:
    - job: plan-fb1-test-eala
    - job: prepare-json-config-fb1-test-eala
  extends: ['.default-fb1-test-eala-variables','.apply_steps']
  tags:
    - glaas-shared-k8s

attache-fb1-test-eala:
  needs:
    - job: apply-fb1-test-eala
    - job: prepare-json-config-fb1-test-eala
  extends: ['.default-fb1-test-eala-variables','.attache_vmdk_step']
  tags:
    - glaas-shared-k8s

sync-fb1-test-eala:
  needs:
    - job: apply-fb1-test-eala
    - job: attache-fb1-test-eala
    - job: prepare-json-config-fb1-test-eala
  extends: ['.default-fb1-test-eala-variables','.sync_vmdk_step']

ansible-fb1-test-eala:
  needs:
    - job: apply-fb1-test-eala
    - job: sync-fb1-test-eala
    - job: prepare-json-config-fb1-test-eala
  extends: ['.default-fb1-test-eala-variables', '.ansible_common_secrets', '.run_ansible_step']
  tags:
    - glaas-shared-k8s
