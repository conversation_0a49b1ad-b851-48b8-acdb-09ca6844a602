#**********************************************
#               kingston_rel_ps PIPE
#**********************************************
.default-kin-rel-ps-variables:
  extends: .secrets-kingston_rel_ps
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "kingston_rel_ps"
    WORKING_DIR: "projects/kingston_rel_ps"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-kingston-rel-ps.json"
    ansible_main_module: kingston_silverbacks
    SILVERBACK_CONFIG_JSON_FILE: "kingston_ps.json"

prepare-json-config-kin-rel-ps:
  extends: ['.default-kin-rel-ps-variables', '.prepare_config']

validate-kin-rel-ps:
  extends: ['.default-kin-rel-ps-variables', '.validation_steps']

plan-kin-rel-ps:
  needs:
    - job: validate-kin-rel-ps
    - job: prepare-json-config-kin-rel-ps
  extends: ['.default-kin-rel-ps-variables','.plan_steps']

apply-kin-rel-ps:
  needs:
    - job: plan-kin-rel-ps
    - job: prepare-json-config-kin-rel-ps
  extends: ['.default-kin-rel-ps-variables','.apply_steps']

attache-kin-rel-ps:
  needs:
    - job: apply-kin-rel-ps
    - job: prepare-json-config-kin-rel-ps
  extends: ['.default-kin-rel-ps-variables','.attache_vmdk_step']

sync-kin-rel-ps:
  needs:
    - job: apply-kin-rel-ps
    - job: attache-kin-rel-ps
    - job: prepare-json-config-kin-rel-ps
  extends: ['.default-kin-rel-ps-variables','.sync_vmdk_step']

ansible-kin-rel-ps:
  needs:
    - job: apply-kin-rel-ps
    - job: sync-kin-rel-ps
    - job: prepare-json-config-kin-rel-ps
  extends: ['.default-kin-rel-ps-variables', '.ansible_common_secrets', '.run_ansible_step']
