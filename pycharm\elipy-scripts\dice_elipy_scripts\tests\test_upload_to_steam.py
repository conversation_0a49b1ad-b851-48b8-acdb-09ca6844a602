"""
test_upload_to_steam.py

Unit testing for upload_to_steam.py
"""
import pytest
from unittest import TestCase, mock
from click.testing import Cli<PERSON>unner

with mock.patch(
    "dice_elipy_scripts.utils.decorators.throw_if_files_found", lambda: lambda x: x
), mock.patch("dice_elipy_scripts.utils.sentry_utils.add_sentry_tags", lambda x: x):
    from dice_elipy_scripts.upload_to_steam import cli, upload_to_steam, get_package_type


class TestUploadToSteam(TestCase):
    """
    Test cases for the upload_to_steam.py script
    """

    VALUE_CODE_BRANCH = "codebranch"
    VALUE_CODE_CHANGELIST = "123"
    VALUE_CONFIG = "final"
    VALUE_DATA_BRANCH = "databranch"
    VALUE_DATA_CHANGELIST = "456"
    VALUE_REGION = "ww"
    VALUE_PLATFORM = "win64"
    VALUE_CODE_COMBINE_BRANCH = "combine_branch"
    VALUE_CODE_COMBINE_CHANGELIST = "789"
    VALUE_DATA_COMBINE_BRANCH = "data_combine_branch"
    VALUE_DATA_COMBINE_CHANGELIST = "012"

    MOCK_SECRETS = {"secret1": {"steam_account": "test_user", "steam_password": "test_password"}}

    @classmethod
    def setUpClass(cls):
        cls.runner = CliRunner()

    @mock.patch("elipy2.telemetry.upload_metrics", mock.MagicMock())
    @mock.patch("glob.glob")
    @mock.patch("os.path.exists")
    @mock.patch("elipy2.filer_paths.get_frosty_build_path")
    def test_cli_file_not_found(self, mock_get_path, mock_exists, mock_glob):
        """
        Test the file not found
        """
        mock_get_path.return_value = "/test/path"
        mock_exists.return_value = False
        mock_glob.return_value = ["app_build_test.vdf"]

        args = [
            "--code-branch",
            self.VALUE_CODE_BRANCH,
            "--code-changelist",
            self.VALUE_CODE_CHANGELIST,
            "--data-branch",
            self.VALUE_DATA_BRANCH,
            "--data-changelist",
            self.VALUE_DATA_CHANGELIST,
        ]

        result = self.runner.invoke(cli, args)
        self.assertNotEqual(result.exit_code, 0)
        # self.assertIsInstance(result.exception, FileNotFoundError)

    @mock.patch("elipy2.secrets.get_secrets")
    @mock.patch("os.system")
    def test_upload_to_steam_success(self, mock_system, mock_secrets):
        """
        Test upload success
        """
        mock_secrets.return_value = self.MOCK_SECRETS
        mock_system.return_value = 0

        # Test successful upload
        steam_script_path = "test_script.vdf"
        steam_cmd = "steamcmd.bat"

        upload_to_steam(steam_script_path, steam_cmd)

        expected_cmd = [
            steam_cmd,
            "+login",
            "test_user",
            '"test_password"',
            "+run_app_build",
            steam_script_path,
            "+quit",
        ]

        mock_system.assert_called_once_with(" ".join(expected_cmd))

    @mock.patch("elipy2.secrets.get_secrets")
    @mock.patch("os.system")
    def test_upload_to_steam_system_error(self, mock_system, mock_secrets):
        """
        Test upload error
        """
        mock_secrets.return_value = self.MOCK_SECRETS
        mock_system.side_effect = Exception("Command failed")

        # Test system command failure
        steam_script_path = "test_script.vdf"
        steam_cmd = "steamcmd.bat"

        upload_to_steam(steam_script_path, steam_cmd)
        mock_system.assert_called_once()

    @mock.patch("elipy2.secrets.get_secrets")
    def test_upload_to_steam_no_secrets(self, mock_secrets):
        """
        Test empty secrets
        """
        mock_secrets.return_value = {}

        steam_script_path = "test_script.vdf"
        steam_cmd = "steamcmd.bat"

        with self.assertRaises(StopIteration):
            upload_to_steam(steam_script_path, steam_cmd)

    @mock.patch("elipy2.secrets.get_secrets")
    def test_upload_to_steam_invalid_secrets(self, mock_secrets):
        """
        Test invalid secrets structure
        """
        mock_secrets.return_value = {"secret1": {}}

        steam_script_path = "test_script.vdf"
        steam_cmd = "steamcmd.bat"

        with self.assertRaises(KeyError):
            upload_to_steam(steam_script_path, steam_cmd)


class TestGetPackageType:
    @pytest.mark.parametrize(
        "upload_patch,code_combine_branch,expected",
        [
            (False, "None", "steam"),
            (True, "None", "steam_patch"),
            (False, "feature_branch", "steam_combine"),
            (True, "feature_branch", "steam_patch_combine"),
        ],
    )
    def test_get_package_type(self, upload_patch, code_combine_branch, expected):
        assert get_package_type(upload_patch, code_combine_branch) == expected


class TestUploadPatchBuildOptions:
    @pytest.mark.parametrize(
        "cli_args,expected_patch,expected_combine_branch",
        [
            (
                ["--upload-patch", "--code-combine-branch", "None"],
                True,
                "None",
            ),  # get_package_type(upload_patch=True, code_combine_branch="None")
            (
                ["--code-combine-branch", "None"],
                False,
                "None",
            ),  # upload_patch defaults to False, so get_package_type(False, "None")
            (
                ["--upload-patch", "--code-combine-branch", "feature"],
                True,
                "feature",
            ),  # is patch and use code_combine_branch param
            (
                ["--code-combine-branch", "feature"],
                False,
                "feature",
            ),  # not patch and use code_combine_branch param
        ],
    )
    @mock.patch("elipy2.filer_paths.get_frosty_build_path", mock.MagicMock(return_value="/tmp"))
    @mock.patch("os.path.exists", mock.MagicMock(return_value=True))
    @mock.patch("os.path.join", mock.MagicMock(return_value="/tmp/app_build_test.vdf"))
    @mock.patch("glob.glob", mock.MagicMock(return_value=["app_build_test.vdf"]))
    @mock.patch("elipy2.LOGGER.info", mock.MagicMock())
    @mock.patch("dice_elipy_scripts.upload_to_steam.upload_to_steam", mock.MagicMock())
    @mock.patch("dice_elipy_scripts.upload_to_steam.get_package_type")
    def test_upload_patch_build_options(
        self, mock_get_package_type, cli_args, expected_patch, expected_combine_branch
    ):
        runner = CliRunner()
        mock_get_package_type.return_value = "steam_patch" if expected_patch else "steam"
        base_args = [
            "--code-branch",
            "cb",
            "--code-changelist",
            "1",
            "--data-branch",
            "db",
            "--data-changelist",
            "2",
        ]
        args = base_args + cli_args
        result = runner.invoke(cli, args)
        assert result.exit_code == 0
        mock_get_package_type.assert_called_with(expected_patch, expected_combine_branch)
