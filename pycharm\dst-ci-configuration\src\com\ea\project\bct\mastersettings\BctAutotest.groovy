package com.ea.project.bct.mastersettings

import com.ea.project.bct.Bct

class BctAutotest {
    static Class project = Bct
    static Map branches = [:]
    static Map preflight_branches = [:]
    static Map autotest_branches = [
        'trunk-code-dev'     : [
            code_folder               : 'mainline',
            code_branch               : 'trunk-code-dev',
            data_folder               : 'mainline',
            data_branch               : 'trunk-code-dev',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            koala_autotest            : true,
            set_integration_info      : [
                remote_jenkins: 'bct-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'trunk-code-dev.autotest-to-integration.code',
                test_category : 'lkg_checkmate',
            ],
        ],
        'trunk-content-dev'  : [
            code_folder               : 'mainline',
            code_branch               : 'trunk-content-dev',
            data_folder               : 'mainline',
            data_branch               : 'trunk-content-dev',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            set_integration_info      : [
                remote_jenkins: 'bct-ch1-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'trunk-content-dev.autotest-to-integration.code',
                test_category : 'lkg_checkmate',
            ],
        ],
        'dev-na-to-trunk'    : [
            code_folder               : 'stage',
            code_branch               : 'dev-na-to-trunk',
            data_folder               : 'stage',
            data_branch               : 'dev-na-to-trunk',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
        ],
        'dev-na-to-trunk-sub'    : [
            code_folder               : 'stage',
            code_branch               : 'dev-na-to-trunk-sub',
            data_folder               : 'stage',
            data_branch               : 'dev-na-to-trunk-sub',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
        ],
        'trunk-to-dev-na'    : [
            code_folder               : 'stage',
            code_branch               : 'trunk-to-dev-na',
            data_folder               : 'stage',
            data_branch               : 'trunk-to-dev-na',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            set_integration_info      : [
               remote_jenkins: 'bct-dev-jenkins.cobra.dre.ea.com',
               remote_job    : 'trunk-to-dev-na.autotest-to-integration.code',
               test_category : 'lkg_auto',
            ],
        ],
        'task1'              : [
            code_folder               : 'tasks',
            code_branch               : 'task1',
            data_folder               : 'tasks',
            data_branch               : 'task1',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
            set_integration_info      : [
                remote_jenkins: 'bct-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'task1.autotest-to-integration.code',
                test_category : 'lkg_checkmate',
            ],
        ],
        'trunk-code-dev-sanitizers': [
            code_folder               : 'mainline',
            code_branch               : 'trunk-code-dev-sanitizers',
            data_folder               : 'mainline',
            data_branch               : 'trunk-code-dev-sanitizers',
            job_label_statebuild_bilbo: 'statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
            build_frosted             : true,
        ],
    ]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'trunk-code-dev': [code_folder: 'mainline', code_branch: 'trunk-code-dev', data_folder: 'mainline', data_branch: 'trunk-code-dev'],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
