#!/usr/bin/env python3
"""
Test specifically the CH1-event branch to understand why it's not being processed
"""

import os

def test_ch1_event_scanning():
    """Test scanning specifically for CH1-event branch"""
    base_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    ch1_event_path = os.path.join(base_path, "CH1-event")
    
    print("=" * 80)
    print("Testing CH1-event branch specifically")
    print("=" * 80)
    print(f"CH1-event path: {ch1_event_path}")
    
    if not os.path.exists(ch1_event_path):
        print("❌ CH1-event directory does not exist!")
        return
    
    print("✅ CH1-event directory exists")
    
    try:
        # Level 2: CL1 directories
        level2_items = os.listdir(ch1_event_path)
        print(f"Level 2 items: {len(level2_items)}")
        
        builds_found = 0
        
        # Check first 3 CL1 directories
        for i, cl1_item in enumerate(level2_items[:3]):
            if cl1_item.startswith('.'):
                continue
                
            cl1_path = os.path.join(ch1_event_path, cl1_item)
            if not os.path.isdir(cl1_path):
                continue
                
            print(f"\nCL1: {cl1_item}")
            
            try:
                # Level 3: Branch directories
                level3_items = os.listdir(cl1_path)
                print(f"  Level 3 items: {level3_items}")
                
                for branch2_item in level3_items:
                    if branch2_item.startswith('.'):
                        continue
                        
                    branch2_path = os.path.join(cl1_path, branch2_item)
                    if not os.path.isdir(branch2_path):
                        continue
                        
                    print(f"    Branch2: {branch2_item}")
                    
                    try:
                        # Level 4: CL2 directories (final builds)
                        level4_items = os.listdir(branch2_path)
                        print(f"      Level 4 items: {level4_items}")
                        
                        for cl2_item in level4_items:
                            cl2_path = os.path.join(branch2_path, cl2_item)
                            if os.path.isdir(cl2_path) and cl2_item.isdigit() and len(cl2_item) >= 7:
                                builds_found += 1
                                print(f"        ✅ Build: {cl2_path}")
                                
                    except (OSError, PermissionError) as exc:
                        print(f"      ❌ Error reading level 4: {exc}")
                        
            except (OSError, PermissionError) as exc:
                print(f"  ❌ Error reading level 3: {exc}")
                
        print(f"\nTotal builds found in CH1-event: {builds_found}")
        
        if builds_found > 0:
            print("✅ CH1-event has valid builds that should be processed")
        else:
            print("❌ CH1-event has no valid builds")
            
    except (OSError, PermissionError) as exc:
        print(f"❌ Error reading CH1-event directory: {exc}")

def compare_with_working_branch():
    """Compare CH1-event with a working branch like CH1-qol"""
    base_path = r"\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame"
    
    print("\n" + "=" * 80)
    print("Comparing CH1-event with CH1-qol (working branch)")
    print("=" * 80)
    
    branches = ["CH1-event", "CH1-qol"]
    
    for branch in branches:
        branch_path = os.path.join(base_path, branch)
        print(f"\nBranch: {branch}")
        print(f"Path: {branch_path}")
        
        if not os.path.exists(branch_path):
            print("  ❌ Directory does not exist")
            continue
            
        try:
            level2_items = os.listdir(branch_path)
            print(f"  Level 2 items: {len(level2_items)}")
            
            # Check structure of first item
            for item in level2_items:
                if item.startswith('.'):
                    continue
                    
                item_path = os.path.join(branch_path, item)
                if not os.path.isdir(item_path):
                    continue
                    
                print(f"    First CL1: {item}")
                
                try:
                    level3_items = os.listdir(item_path)
                    print(f"      Level 3 items: {level3_items}")
                    
                    for level3_item in level3_items:
                        if level3_item.startswith('.'):
                            continue
                            
                        level3_path = os.path.join(item_path, level3_item)
                        if not os.path.isdir(level3_path):
                            continue
                            
                        print(f"        Branch2: {level3_item}")
                        
                        try:
                            level4_items = os.listdir(level3_path)
                            cl_dirs = [x for x in level4_items 
                                     if os.path.isdir(os.path.join(level3_path, x)) 
                                     and x.isdigit() and len(x) >= 7]
                            print(f"          CL2 directories: {len(cl_dirs)}")
                            if cl_dirs:
                                print(f"          Sample: {cl_dirs[0]}")
                                
                        except (OSError, PermissionError) as exc:
                            print(f"          ❌ Error: {exc}")
                        
                        break  # Only check first branch2
                        
                except (OSError, PermissionError) as exc:
                    print(f"      ❌ Error: {exc}")
                    
                break  # Only check first CL1
                
        except (OSError, PermissionError) as exc:
            print(f"  ❌ Error: {exc}")

if __name__ == "__main__":
    test_ch1_event_scanning()
    compare_with_working_branch()
