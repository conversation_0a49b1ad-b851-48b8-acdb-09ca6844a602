#**********************************************
#               VMDK RUNNERS PIPE
#**********************************************
.default-dice-vmdk-runners-variables:
  extends: .secrets-dice_vmdk_runners
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "dice_vmdk_runners"
    WORKING_DIR: "projects/gitlab_runners/dice_vmdk_runners"
    NODE_INFO_FILE: "node-info-dice-vmdk-runners.json"
    VC_HOST: vc.dice.ad.ea.com
    VAULT_RATE_LIMIT: 50
    VAULT_ADDR: "https://ess.ea.com"
    VAULT_NAMESPACE: "cds-dre-prod"
    VAULT_AUTH_ROLE: "gl-dre-cobra-silverback"
    VAULT_AUTH_LOGIN_PATH: "auth/jwt/gitlab/login"
    SECRET_TOP: "secrets/kv/cobra/automation/gitlab"
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log
    ansible_main_module: vmdk_runners_silverback
    SILVERBACK_CONFIG_JSON_FILE: "vmdk_runners.json"

prepare-json-config-dice-vmdk-runners:
  extends: ['.default-dice-vmdk-runners-variables', '.prepare_config']

validate-dice-vmdk-runners:
  extends: ['.default-dice-vmdk-runners-variables', '.validation_steps']

plan-dice-vmdk-runners:
  extends: ['.default-dice-vmdk-runners-variables', '.plan_steps_runner']

apply-dice-vmdk-runners:
  needs:
    - job: plan-dice-vmdk-runners
  extends: ['.default-dice-vmdk-runners-variables', '.apply_steps']

ansible-dice-vmdk-runners:
  needs:
    - job: apply-dice-vmdk-runners
    - job: prepare-json-config-dice-vmdk-runners
  extends: ['.default-dice-vmdk-runners-variables', '.ansible_common_secrets', '.run_ansible_step']
