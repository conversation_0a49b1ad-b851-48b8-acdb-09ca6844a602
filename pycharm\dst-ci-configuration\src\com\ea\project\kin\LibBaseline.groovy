package com.ea.project.kin

import com.ea.exceptions.CobraException

class LibBaseline {
    static final Baseline[] disc_matrix = [
        //Submitted: https://frostbite.ea.com/display/PL/Kingston+-+IRT+Build+Submissions
        new Baseline('kin-dev-irt', 'ps5', '11121464', 'kin-dev-irt', '4375284', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'ps4', '11289591', 'kin-dev-irt', '4399387', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'xb1', '11121464', 'kin-dev-irt', '4375284', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'xbsx', '11121464', 'kin-dev-irt', '4375284', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'win64', '14415046', 'kin-dev-irt', '4764499', 'kin-dev-irt'),

        //Mainlines
        new Baseline('kin-live', 'win64', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-live', 'ps4', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-live', 'xb1', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-live', 'ps5', '12409338', 'kin-release', '4595077', 'kin-release'),
        new Baseline('kin-live', 'xbsx', '12409338', 'kin-release', '4592495', 'kin-release'),

        new Baseline('kin-release', 'win64', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-release', 'ps4', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-release', 'xb1', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-release', 'ps5', '12409338', 'kin-release', '4595077', 'kin-release'),
        new Baseline('kin-release', 'xbsx', '12409338', 'kin-release', '4592495', 'kin-release'),

        new Baseline('kin-stage', 'win64', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-stage', 'ps4', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-stage', 'xb1', '12409338', 'kin-release', '4592495', 'kin-release'),
        new Baseline('kin-stage', 'ps5', '12409338', 'kin-release', '4595077', 'kin-release'),
        new Baseline('kin-stage', 'xbsx', '12409338', 'kin-release', '4592495', 'kin-release'),

        //Open beta
        new Baseline('kin-open-beta-patch', 'ps5', '12267892', 'kin-open-beta', '4560856', 'kin-open-beta'),
        new Baseline('kin-open-beta-patch', 'ps4', '12267892', 'kin-open-beta', '4560856', 'kin-open-beta'),
        new Baseline('kin-open-beta-patch', 'xb1', '12267892', 'kin-open-beta', '4560856', 'kin-open-beta'),
        new Baseline('kin-open-beta-patch', 'xbsx', '12267892', 'kin-open-beta', '4560856', 'kin-open-beta'),
        new Baseline('kin-open-beta-patch', 'win64', '12267892', 'kin-open-beta', '4560856', 'kin-open-beta'),
    ]
    static final Baseline[] patch_matrix = [
        new Baseline('kin-dev-irt', 'xb1', '14415046', 'kin-dev-irt', '4764499', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'xbsx', '14415046', 'kin-dev-irt', '4764499', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'ps4', '14415046', 'kin-dev-irt', '4764499', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'ps5', '14415046', 'kin-dev-irt', '4764499', 'kin-dev-irt'),
        new Baseline('kin-dev-irt', 'win64', '14415046', 'kin-dev-irt', '4764499', 'kin-dev-irt'),

        //Mainlines
        new Baseline('kin-stage', 'win64', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-stage', 'ps4', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-stage', 'xb1', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-stage', 'ps5', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-stage', 'xbsx', '23933087', 'kin-release', '5156642', 'kin-release'),

        new Baseline('kin-release', 'win64', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-release', 'ps4', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-release', 'xb1', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-release', 'ps5', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-release', 'xbsx', '23933087', 'kin-release', '5156642', 'kin-release'),

        new Baseline('kin-live', 'win64', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-live', 'ps4', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-live', 'xb1', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-live', 'ps5', '23933087', 'kin-release', '5156642', 'kin-release'),
        new Baseline('kin-live', 'xbsx', '23933087', 'kin-release', '5156642', 'kin-release'),
    ]

    static Baseline get_disc_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(disc_matrix, name, platform)
    }

    static Baseline get_patch_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(patch_matrix, name, platform)
    }

    private static Baseline get_baseline_from_matrix(Baseline[] matrix, String name, String platform) {
        for (baseline in matrix) {
            if (baseline.name == name && baseline.platform == platform) {
                return baseline
            }
        }
        throw new CobraException("No baseline configuration matches name: ${name} as well as platform: ${platform}!")
    }
}

class Baseline {
    /*
    This is the master baseline values. All other baselines should reference its values per default.
    */
    static String default_code_changelist = '4288529'
    static String default_code_branch = 'kin-dev'
    static String default_data_changelist = '10030845'
    static String default_data_branch = 'kin-dev'
    String name, platform, code_changelist, code_branch, data_changelist, data_branch

    // EXCEPTIONAL Constructor
    Baseline(name, platform, code_changelist, code_branch, data_changelist, data_branch) {
        this.name = name
        this.platform = platform
        this.code_changelist = code_changelist
        this.code_branch = code_branch
        this.data_changelist = data_changelist
        this.data_branch = data_branch
    }
    // EXCEPTIONAL Constructor
    Baseline(name, platform, branches, code_changelist, data_changelist) {
        this(name, platform, code_changelist, branches, data_changelist, branches)
    }
    // DEFAULT Constructor
    Baseline(name, platform) {
        this(name, platform, default_code_changelist, default_code_branch, default_data_changelist, default_data_branch)
    }
}
