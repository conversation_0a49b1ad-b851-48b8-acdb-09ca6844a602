package com.ea.project.bct.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.jobs.LibCustomScript
import com.ea.lib.jobsettings.ShiftSettings
import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.bct.Bct

class Trunk_code_dev {
    static Class project = Bct
    static Map general_settings = [
        dataset                                : project.dataset,
        elipy_call                             : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call                     : project.elipy_install_call,
        frostbite_licensee                     : project.frostbite_licensee,
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : project.azure_elipy_call,
        azure_elipy_install_call               : project.azure_elipy_install_call,
        azure_workspace_root                   : project.azure_workspace_root,
        webexport_script_path                  : 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py',
        azure_fileshare                        : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'glacier_azure_fileshare',
            target_build_share         : 'bfglacier',
        ],
        job_label_statebuild                   : 'statebuild',
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [
                    config        : 'release',
                    framework_args: ['-G:frostbite.use-prebuilt-native-binaries=true'],
                ],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
                (LibPerforce.GAMETOOL_FROSTYISOTOOL)              : [],
                (LibPerforce.GAMETOOL_DRONE)                      : [],
                (LibPerforce.GAMETOOL_FRAMEWORK)                  : [],
            ],
        ],
        coverity_settings                      : [
            credentials            : 'monkey.bct',
            ess_secrets_credential : 'bct-secrets-secret-id',
            ess_secrets_key        : 'BCT_SECRETS_SECRET_ID',
            run_coverity           : true,
            trigger                : '@daily',
            job_label              : 'bct_coverity',
            artifactory_source_path: 'bct-infrax-generic-fed/tools/coverity/cov-analysis-win64-2024.12.0.zip',
            p4_code_creds          : 'bct-la-p4',
            p4_code_server         : 'dicela-p4edge-fb.la.ad.ea.com:2001'
        ],
        autotest_remote_settings               : [
            eala: [
                credentials           : 'monkey.bct',
                ess_secrets_credential: 'bct-secrets-secret-id',
                ess_secrets_key       : 'BCT_SECRETS_SECRET_ID',
                p4_code_creds         : 'bct-la-p4',
                p4_code_server        : 'dicela-p4edge-fb.la.ad.ea.com:2001'
            ],
            criterion: [
                p4_code_creds : 'perforce-battlefield-criterion',
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield-criterion',
                p4_data_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
            ],
            dice: [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: '@daily',
            referenceJob: '.data.start',
            label: 'pipeline_determinism',
        ),
        custom_script                          : [
            (LibCustomScript.PORTAL_MAKE_SDK.jobName): new CustomScriptConfiguration(
                scriptPath: LibCustomScript.PORTAL_MAKE_SDK.scriptPath,
                executable: LibCustomScript.PORTAL_MAKE_SDK.executable,
                executableArgs: LibCustomScript.PORTAL_MAKE_SDK.executableArgs,
                defaultScriptArgs: LibCustomScript.PORTAL_MAKE_SDK.defaultScriptArgs,
                label: 'statebuild',
                jobName: LibCustomScript.PORTAL_MAKE_SDK.jobName,
            ),
        ],
    ]
    static Map code_settings = [
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        deployment_data_branch : true,
        enable_lkg_cleaning    : true,
        poolbuild_data         : true,
        slack_channel_data     : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data        : false,
        statebuild_webexport   : false,
        webexport_allow_failure: true,
        webexport_branch       : true,
    ]
    static Map frosty_settings = [
        frosty_reference_job: 'trunk-code-dev.deployment-data.start',
        poolbuild_frosty    : true,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                         : 'DevLevels',
        custom_tests                  : [
            custom_configs: [
                'trunk-code-dev.json',
                'trunk-code-dev-ade.json',
            ],
        ],
        retry_limit_data              : 1,
        deployment_data_reference_job : 'trunk-code-dev.data.start',
        enable_lkg_p4_counters        : true,
        enable_eac                    : true,
        extra_data_args               : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args             : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_icepick_args            : '--heartbeat-timeout 600',
        remote_masters_to_receive_code: [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        remote_masters_to_receive_data: [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        prebuild_info                 : [
            config              : 'release',
            input_param_path    : 'Code\\DICE\\BattlefieldGame\\BattlefieldGame-outsource-input-param.xml',
            platforms_sln       : ['tool', 'frosted'],
            platform_prebuild   : ['win64-dll'],
            platform_validation : ['tool', 'frosted'],
            prebuild_path       : '//fblicensee/battlefield/trunk-code-dev-outsource-prebuilts',
            skip_platforms      : ['gdk', 'NX', 'ps4', 'ps5', 'xdk'],
            outsource_validation: true,
            extra_args          : [],
        ],
        server_asset                  : 'Game/Setup/Build/DevMPLevels',
        shift_branch                  : true,
        shift_reference_job           : 'trunk-code-dev.frosty.start',
        shift_subscription_matrix     : [
            [
                dest_location : 'RippleEffect',
                build_type    : 'offsite_basic_drone',
                job_label     : 'bct_eala && shift && processing',
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                p4_data_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                src_location  : 'DiceStockholm',
                trigger_type  : 'none',
            ]
        ],
        single_stream_smoke           : true,
        skip_icepick_settings_file    : true,
        smoke_downstream_job          : 'trunk-code-dev.integrate-upgrade-to.trunk-content-dev.start,trunk-code-dev.integrate-upgrade-to.trunk-to-dev-na.start',
        strip_symbols                 : false,
        timeout_hours_data            : 6,
        move_location_parallel        : true,
        new_locations                 : [
            earo        : [
                elipy_call_new_location: project.elipy_call_earo + ' --use-fbenv-core',
            ],
            Montreal    : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        cadet_activate_toolset        : true,
        cadet_p4_fb_settings          : [
            p4_creds: 'perforce-battlefield01',
            p4_port : 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
        ],
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [
        concurrent_code               : 4,
        enable_custom_cl              : true,
        codepreflight_reference_job   : 'trunk-code-dev.code.lastknowngood',
        datapreflight_reference_job   : 'trunk-code-dev.data.lastknowngood',
        extra_codepreflight_args      : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml  --framework-args -D:eaconfig.optimization.ltcg=off",
        force_rebuild_preflight       : true,
        slack_channel_preflight       : [channels: ['#cobra-build-preflight']],
        statebuild_codepreflight      : false,
        statebuild_datapreflight      : false,
        use_icepick_test              : true,
        pre_preflight                 : true,
        run_on_azure                  : false,
        keep_agent                    : false, // Prevent agent to be removed on failed azure builds
        enable_hybrid_agents          : true,
        trigger_ado                   : true,
        ado_organization              : 'ea-battlefield',
        ado_project                   : 'BCT-Staging',
        ado_code_preflight_pipeline_id: '6',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true], [name: 'deprecation-test', allow_failure: true, compile_unit_tests: true]]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_stressbulkbuild_matrix = [
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
        [name: '.code.lastknowngood', args: ['code_changelist']],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
        [name: 'linux64'],
        [name: 'validate-frosted', deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.frosty.start', args: []],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
        [name: '.deployment-data.start', args: []],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_checkmate.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_auto.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_bootanddeploy.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_bootanddeploy_tool.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_qv.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_qv_win.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                   [format: 'steam', config: 'final', region: 'ww', args: ''],
                                   [format: 'files', config: 'performance', region: 'ww', args: ''],
                                   [format: 'files', config: 'release', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['final']],
        [name: 'xbsx', configs: ['performance']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
    ]
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = [
        [name: '.spin.linux64.files.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register-bfdata-dronebuild', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
        [upstream_job: '.bilbo.register.local', downstream_job: 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.frostedtests.all.start', args: [], creds: 'perforce-battlefield01'],
    ]
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64'],
        [platform: 'win64', job_name: 'debugdb', additional_script_args: '-add_cook_args=\"-forceDebugTarget\"'],
        [platform: 'ps5'],
        [platform: 'xbsx'],
    ]
}
