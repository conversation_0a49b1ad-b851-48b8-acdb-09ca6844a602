"""
local_paths.py

Module to handle paths for local locations.
"""
from __future__ import absolute_import
import os
import re
from typing import Optional
from elipy2 import core, frostbite_core, LOGGER, SETTINGS
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import fbenv_layer


def get_game_binary_path(platform, config, use_fbenv=None):
    """
    Returns absolute path to executable for a platform-config combination.

    Returns:
        Absolute path to executable for a platform-config combination.
    """

    if use_fbenv is None:
        use_fbenv = frostbite_core.minimum_fb_version(year=2019, version_nr=1)
    if use_fbenv:
        plt = fbenv_layer.normalize_platform(platform=platform)
        return fbenv_layer.get_game_binary(platform=plt, local=True, config=config)
    else:
        return os.path.join(
            get_tnt_local_path(),
            "Bin",
            get_platform_path(platform, config),
            get_executable_name_pattern(platform, config),
        )


def get_executable_name_pattern(platform, config):
    """
    Gives game executable file name for win64 platforms.
    """
    platform = platform.lower()
    pattern = fbenv_layer.get_exe_name() + ".Main_Win64_" + config + "{}.exe"
    full = pattern.format("")
    trial = pattern.format("_Trial")
    name_map = {
        "win64game": full,
        "win64": full,
        "win64-trial": trial,
        "win64trial": trial,
    }

    if platform not in name_map:
        raise ELIPYException("Unknown platform {0}".format(platform))

    return name_map[platform]


def _get_platform_map():
    exe_main_name = fbenv_layer.get_exe_name().lower()
    return {
        "tool": ["tools"],
        "pipeline": ["tools", "pipeline"],
        "frosted": ["tools", "frosted"],
        "ant": ["tools", "ant"],
        "win64game": [exe_main_name, "win64"],
        "win64": [exe_main_name, "win64"],
        "win64-dll": [exe_main_name, "win64-dll"],
        "win64-trial": [exe_main_name, "win64-trial"],
        "win64trial": [exe_main_name, "win64-trial"],
        "ps4": [exe_main_name, "ps4"],
        "ps5": [exe_main_name, "ps5"],
        "xb1": [exe_main_name, "xb1"],
        "linux64": [exe_main_name, "linux64"],
        "linux": [exe_main_name, "linux64"],
        "linux64server": [exe_main_name, "linux64-server"],
        "linuxserver": [exe_main_name, "linux64-server"],
        "win64server": [exe_main_name, "win64-server"],
        "server": [exe_main_name, "win64-server"],
        "gamelauncher": [exe_main_name, "gamelauncher"],
    }


def get_local_build_path(platform=None, config=None, use_fbenv=None):
    """
    Returns local binary path
    """
    if use_fbenv is None:
        use_fbenv = frostbite_core.minimum_fb_version(year=2019, version_nr=1)
    if use_fbenv and platform.lower() not in ["ant", "tool"]:
        plt = fbenv_layer.normalize_platform(platform=platform)
        if platform.lower() == "pipeline":
            return fbenv_layer.get_pipeline_directory(config=config)
        if platform.lower() == "frosted":
            return fbenv_layer.get_frosted_directory(config=config)
        return fbenv_layer.get_game_directory(platform=plt, local=True, config=config)
    else:
        return os.path.join(
            frostbite_core.get_tnt_root(),
            "Local",
            "Bin",
            get_platform_path(platform=platform, config=config),
        )


def get_platform_path(
    platform, config=None, is_dll=False, use_fbenv=None
):  # pylint: disable=too-many-return-statements
    """
    Translates the platform+config into the actual location on disk for code builds.
    """
    if use_fbenv is None:
        use_fbenv = frostbite_core.minimum_fb_version(year=2019, version_nr=1)
    # Vidir: fixing ANT in fb 2019, hack.
    if use_fbenv and platform.lower() != "ant":
        if platform.lower() == "win64-trial":
            platform = "win64trial"

        if platform.lower() == "tool":
            return os.path.join("Win64-dll")
        if platform.lower() == "pipeline":
            return os.path.join("Win64-dll", config)
        if platform.lower() == "frosted":
            return os.path.join("Win64-dll", config)
        else:
            platform = fbenv_layer.normalize_platform(platform=platform)
            platform_data = fbenv_layer.get_platform_data(platform=platform)
            if platform_data is None:
                raise ELIPYException("Invalid platform '{}'".format(platform))

            platform_folder_name = platform_data["folder_name"]

            if config is None:
                if platform == "win64":
                    return platform_folder_name
                else:
                    return os.path.join(fbenv_layer.get_exe_name(), platform_folder_name)
            else:
                if platform == "win64":
                    return os.path.join(platform_folder_name, config)
                else:
                    return os.path.join(fbenv_layer.get_exe_name(), platform_folder_name, config)
    else:
        platform_map = _get_platform_map()
        if platform.lower() not in platform_map:
            raise ELIPYException("Unknown platform {0}".format(platform))

        path = str(os.sep).join(platform_map[platform.lower()])

        LOGGER.debug("Got platform path {0}".format(path))
        if is_dll:
            path += "-dll"
        if config is None:
            return path
        else:
            return os.path.join(path, config)


def get_local_artifact_path(artifact, config=None):
    """
    Returns local binary path
    """
    if config:
        return os.path.join(frostbite_core.get_tnt_root(), "Local", "Bin", artifact, config)
    else:
        return os.path.join(frostbite_core.get_tnt_root(), "Local", "Bin", artifact)


def get_local_bundles_path(deployed_bundles_dir_name: Optional[str] = "deployed_bundles") -> str:
    """
    Return local path for deployed bundles
    """
    return os.path.join(frostbite_core.get_tnt_root(), "local", deployed_bundles_dir_name, "data")


def get_local_scripts_path():
    """
    Returns the local scripts path.

    Returns:
        scripts path as defined by the local build environment.

    Raises:
        CoreException: Unable to find the scripts path.
    """
    scripts_path = os.path.join(os.path.dirname(__file__), "scripts")
    return scripts_path


def get_local_frosty_path():
    """
    Returns local binary path.
    """
    return os.path.join(frostbite_core.get_tnt_root(), "Local", "Frosty", "Output")


def get_tnt_local_path():
    """
    Returns local binary path.
    """
    return os.path.join(frostbite_core.get_tnt_root(), "Local")


def get_tnt_localpackages_path():
    """
    Returns LocalPackages path.
    """
    return os.path.join(frostbite_core.get_tnt_root(), "LocalPackages")


def get_tnt_packages_path():
    """
    Returns TnT/Packages path.
    """
    return os.path.join(frostbite_core.get_tnt_root(), "Packages")


def get_game_drive_path():
    """
    Get the drive path that the game root is located under
    """
    game_root = frostbite_core.get_game_root()
    raw_drive = re.split(r"[\\/]+", game_root)[0]
    drive_path = raw_drive + "/"
    LOGGER.debug("Drive Path '{}'".format(drive_path))
    return drive_path


def get_packages_path():
    """
    Returns Packages path.
    """
    return os.path.join(get_game_drive_path(), "packages")


def get_packagesdev_path():
    """
    Returns packagesdev path.
    """
    return os.path.join(get_game_drive_path(), "packagesdev")


def get_pip_cache_path():
    """
    Returns .pip-cache path.
    """
    return os.path.join(get_game_drive_path(), ".pip-cache")


def get_logs_path():
    """
    Returns logs path.
    """
    return os.path.join(frostbite_core.get_game_root(), "logs")


def get_dataset_state_path(data_dir):
    """
    Returns dataSet .state path.
    """
    return os.path.join(frostbite_core.get_game_root(), data_dir, ".state")


def get_local_licensee_path(project_name):
    """
    Returns root path, taking into account difference in project licensee folder name if any.
    """
    return os.path.join(
        frostbite_core.get_tnt_root(), core.get_licensee_code_folder(), project_name
    )


def get_avalanchecli_exe_path():
    """
    Returns:
        The path to avalanchecli.
    Raises:
        ELIPYException if exe is not found.
    """
    exe_path = os.path.join(frostbite_core.get_tnt_root(), "bin", "avalanchecli.exe")
    if os.path.exists(exe_path):
        return exe_path
    else:
        raise ELIPYException("{0} not found on disk.".format(exe_path))


def get_ant_local_dir():
    """
    Returns the path to the directory with ANT local in it.
    This is _not_ defined in the environment, and is
    up to the ANT toolchain to decide, so this is a best-guess.
    """
    try:
        return os.path.join(frostbite_core.get_game_data_dir(), SETTINGS.get("ant_local_dir"))
    except Exception:
        return os.path.join(frostbite_core.get_game_data_dir(), "Raw", "ANT_Project")


def get_ant_local():
    """
    Returns the path to ANT Local. This is _not_ defined in the environment, and is
    up to the ANT toolchain to decide, so this is a best-guess.
    """
    return os.path.join(get_ant_local_dir(), "Local")


def get_antifreeze_dir():
    """
    Returns the path to Antifreeze.
    """
    return os.path.join(frostbite_core.get_game_data_dir(), "Antifreeze")


def get_local_avalanche_export_path(branch, data_changelist, code_changelist, platform):
    """
    Returns a local path to the Avalanche export folder.
    """
    return os.path.join(
        frostbite_core.get_tnt_root(),
        "Local",
        "import.{0}.{1}.{2}.{3}".format(branch, platform, data_changelist, code_changelist),
    )


def get_fdu_folder():
    """
    Returns a local path to the frostbite database upgrader folder.
    """
    return os.path.join(frostbite_core.get_tnt_root(), "Code", "Utils", "FrostbiteDatabaseUpgrader")


def get_local_expressiondebug_path():
    """
    Returns a local path to the expression debug data for "fb cook" to dump.
    """
    return os.path.join(frostbite_core.get_tnt_root(), "Local", "ExpressionDebugData")
