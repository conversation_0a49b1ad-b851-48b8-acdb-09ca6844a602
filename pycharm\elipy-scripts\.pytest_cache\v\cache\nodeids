["dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_cli_basic_parameters_validation", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_create_head_bundles_path_handling", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_custom_network_share_path", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_delta_bundle_parameters_validation", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_delta_bundle_with_baseline_parameters", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_error_handling_missing_source_bundles", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_feature_flag_disabled_uses_legacy", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_feature_flag_enabled_uses_separate_workflow", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_legacy_workflow_delegation", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_platform_specific_processing_coverage", "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_process_bundles_for_combination", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_check_and_drop_records_not_records_returned", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_handles_no_config[-False]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_handles_no_config[None-False]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_handles_no_config_empty_string", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_handles_no_config_none", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention[False-False]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention[True-True]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention_false", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention_true", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_passes_if_no_config_found", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_uses_full_path_not_relative", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_excludes", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_includes", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_no_bilbo", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_use_onefs_api", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_delete_empty_folders", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_exclude_retention_categories", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_exclude", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_exclude_raises", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_include", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_include_raises", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_code_paths", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_exc_path", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_excalibur_path_bug", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_full_frosty_paths", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_roboto_path", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_short_frosty_licensee_paths", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_dry_run[False-1]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_dry_run[True-0]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_dry_run_behavior_false", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_dry_run_behavior_true", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly[account_name-share_name-path/to/fileshare/dir-1-fake.azure.domain.net-expected_deleted_dirs0]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly[account_name-share_name-path/to/fileshare/dir-6-fake.azure.domain.net-expected_deleted_dirs2]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly[account_name-share_name-path/to/fileshare/dir-8-fake.azure.domain.net-expected_deleted_dirs1]", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly_keep_1", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly_keep_6", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly_keep_8", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_no_path_retention", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_settings_file_iterates_over_multiple_shares_in_storage_account"]