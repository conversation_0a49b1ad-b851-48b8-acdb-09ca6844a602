# Corrected investigation script using the same query method as the original script
param(
    [string]$BilboEndpoint = "https://bct-bilbo-eck.cobra.dre.ea.com",
    [string]$BilboIndex = "criterion_bilbo"
)

Write-Host "=" * 80
Write-Host "Corrected Mismatch Investigation"
Write-Host "Using same query method as original script (source field)"
Write-Host "=" * 80

# Function to query Bilbo using source field (same as original script)
function Get-BuildDeletionStatus {
    param(
        [string]$fullPath,
        [string]$esUrl,
        [string]$index
    )
    
    try {
        $searchUrl = "$esUrl/$index/_search"
        
        # Use match query on source field (same as original script)
        $body = @{
            query = @{
                match = @{
                    "source" = $fullPath
                }
            }
            size = 1
        } | ConvertTo-Json -Depth 3
        
        $response = Invoke-RestMethod -Uri $searchUrl -Method Post -Body $body -ContentType "application/json" -ErrorAction Stop
        
        if ($response.hits.total -gt 0) {
            $hit = $response.hits.hits[0]
            $deletedField = $hit._source.deleted
            # Check if deleted field exists and is not null/empty
            $isDeleted = ($null -ne $deletedField -and $deletedField -ne "")
            
            return @{
                Found = $true
                Deleted = $isDeleted
                DeletedField = $deletedField
                Source = $hit._source
                BuildNumber = $hit._source.changelist
                Stream = $hit._source.stream
                CreationTime = $hit._source.creation_time
            }
        } else {
            return @{
                Found = $false
                Deleted = $null
                DeletedField = $null
                Source = $null
                BuildNumber = $null
                Stream = $null
                CreationTime = $null
            }
        }
    }
    catch {
        Write-Warning "Error querying ElasticSearch for path $fullPath : $($_.Exception.Message)"
        return @{
            Found = $false
            Deleted = $null
            DeletedField = $null
            Source = $null
            Error = $_.Exception.Message
            BuildNumber = $null
            Stream = $null
            CreationTime = $null
        }
    }
}

# Builds that Jenkins wants to delete but script doesn't
$JenkinsOnlyBuilds = @(
    "24216947", "24221370", "24230552", "24237548", 
    "24254078", "24283046", "24286233", "24288006"
)

# Build that script wants to delete but Jenkins doesn't
$ScriptOnlyBuilds = @("24310479")

Write-Host "`nInvestigating builds that Jenkins wants to delete but script doesn't..."
Write-Host "-" * 70

$JenkinsOnlyResults = @()
foreach ($build in $JenkinsOnlyBuilds) {
    $fullPath = "\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\$build\CH1-event\$build"
    Write-Host "Querying build $build..." -NoNewline
    $result = Get-BuildDeletionStatus -fullPath $fullPath -esUrl $BilboEndpoint -index $BilboIndex
    $result["BuildNumber"] = $build
    $JenkinsOnlyResults += $result
    Write-Host " Done"
}

Write-Host "`nInvestigating builds that script wants to delete but Jenkins doesn't..."
Write-Host "-" * 70

$ScriptOnlyResults = @()
foreach ($build in $ScriptOnlyBuilds) {
    $fullPath = "\\eauk-file.eu.ad.ea.com\Glacier\Autobuilds\frosty\BattlefieldGame\CH1-event\$build\CH1-event\$build"
    Write-Host "Querying build $build..." -NoNewline
    $result = Get-BuildDeletionStatus -fullPath $fullPath -esUrl $BilboEndpoint -index $BilboIndex
    $result["BuildNumber"] = $build
    $ScriptOnlyResults += $result
    Write-Host " Done"
}

# Display results
Write-Host "`n" + "=" * 80
Write-Host "RESULTS: Builds Jenkins wants to delete but script doesn't"
Write-Host "=" * 80

Write-Host ("{0,-12} {1,-8} {2,-10} {3,-15} {4,-20}" -f "Build", "Found", "Deleted", "Deleted Field", "Creation Time")
Write-Host "-" * 80

foreach ($result in $JenkinsOnlyResults) {
    $creationTimeShort = if ($result["CreationTime"] -and $result["CreationTime"].Length -gt 19) { 
        $result["CreationTime"].Substring(0, 19) 
    } else { 
        $result["CreationTime"] 
    }
    
    Write-Host ("{0,-12} {1,-8} {2,-10} {3,-15} {4,-20}" -f 
        $result["BuildNumber"], 
        $result["Found"], 
        $result["Deleted"], 
        $result["DeletedField"],
        $creationTimeShort
    )
}

Write-Host "`n" + "=" * 80
Write-Host "RESULTS: Builds script wants to delete but Jenkins doesn't"
Write-Host "=" * 80

Write-Host ("{0,-12} {1,-8} {2,-10} {3,-15} {4,-20}" -f "Build", "Found", "Deleted", "Deleted Field", "Creation Time")
Write-Host "-" * 80

foreach ($result in $ScriptOnlyResults) {
    $creationTimeShort = if ($result["CreationTime"] -and $result["CreationTime"].Length -gt 19) { 
        $result["CreationTime"].Substring(0, 19) 
    } else { 
        $result["CreationTime"] 
    }
    
    Write-Host ("{0,-12} {1,-8} {2,-10} {3,-15} {4,-20}" -f 
        $result["BuildNumber"], 
        $result["Found"], 
        $result["Deleted"], 
        $result["DeletedField"],
        $creationTimeShort
    )
}

# Analysis summary
Write-Host "`n" + "=" * 80
Write-Host "DETAILED ANALYSIS"
Write-Host "=" * 80

# Count builds by status for Jenkins-only builds
$jenkinsFoundCount = ($JenkinsOnlyResults | Where-Object { $_["Found"] -eq $true }).Count
$jenkinsDeletedCount = ($JenkinsOnlyResults | Where-Object { $_["Deleted"] -eq $true }).Count
$jenkinsNotDeletedCount = ($JenkinsOnlyResults | Where-Object { $_["Found"] -eq $true -and $_["Deleted"] -eq $false }).Count
$jenkinsNotFoundCount = ($JenkinsOnlyResults | Where-Object { $_["Found"] -eq $false }).Count

Write-Host "Jenkins-only builds (8 total):"
Write-Host "  - Found in Bilbo: $jenkinsFoundCount"
Write-Host "  - Marked as deleted in Bilbo: $jenkinsDeletedCount"
Write-Host "  - NOT marked as deleted in Bilbo: $jenkinsNotDeletedCount"
Write-Host "  - Not found in Bilbo: $jenkinsNotFoundCount"

# Count builds by status for Script-only builds
$scriptFoundCount = ($ScriptOnlyResults | Where-Object { $_["Found"] -eq $true }).Count
$scriptDeletedCount = ($ScriptOnlyResults | Where-Object { $_["Deleted"] -eq $true }).Count
$scriptNotDeletedCount = ($ScriptOnlyResults | Where-Object { $_["Found"] -eq $true -and $_["Deleted"] -eq $false }).Count
$scriptNotFoundCount = ($ScriptOnlyResults | Where-Object { $_["Found"] -eq $false }).Count

Write-Host "`nScript-only builds (1 total):"
Write-Host "  - Found in Bilbo: $scriptFoundCount"
Write-Host "  - Marked as deleted in Bilbo: $scriptDeletedCount"
Write-Host "  - NOT marked as deleted in Bilbo: $scriptNotDeletedCount"
Write-Host "  - Not found in Bilbo: $scriptNotFoundCount"

Write-Host "`n" + "=" * 80
Write-Host "KEY FINDINGS & ROOT CAUSE ANALYSIS"
Write-Host "=" * 80

Write-Host @"

1. **Jenkins Deletion Logic Issue**:
   Jenkins marked $($JenkinsOnlyBuilds.Count) builds for deletion, but:
   - $jenkinsDeletedCount are actually marked as deleted in Bilbo
   - $jenkinsNotDeletedCount are NOT marked as deleted in Bilbo
   - $jenkinsNotFoundCount are not found in Bilbo at all

2. **Script Logic Issue**:
   Script identified $($ScriptOnlyBuilds.Count) build(s) for deletion, but:
   - $scriptDeletedCount are actually marked as deleted in Bilbo
   - $scriptNotDeletedCount are NOT marked as deleted in Bilbo
   - $scriptNotFoundCount are not found in Bilbo at all

3. **Root Cause**:
   The mismatch suggests that Jenkins is NOT using the Bilbo 'deleted' field
   as its primary deletion criteria. Instead, Jenkins likely uses:
   - Age-based retention (builds older than X days)
   - Orphaned build detection (builds not in Bilbo)
   - Retention count limits (keep only N newest builds)

4. **Script Issue**:
   The script should only identify builds that are ACTUALLY marked as 
   deleted in Bilbo, but it's identifying builds that are NOT marked
   as deleted. This suggests a logic error in the script.

RECOMMENDATION: 
- Review Jenkins retention logic to understand its actual criteria
- Fix the script to only identify builds truly marked as deleted in Bilbo
- Investigate why Jenkins is deleting builds not marked as deleted in Bilbo
"@
