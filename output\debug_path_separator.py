#!/usr/bin/env python3

import os

# Test what path separator os.path.join uses
build_share = "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca"
sub_path = "code"
branch = "branch1"

# This is what the current code does
actual_path = os.path.join(os.path.join(build_share, sub_path), branch)
print(f"Actual path: {actual_path}")

# This is what the test expects
expected_path = "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1"
print(f"Expected path: {expected_path}")

print(f"Paths match: {actual_path == expected_path}")
