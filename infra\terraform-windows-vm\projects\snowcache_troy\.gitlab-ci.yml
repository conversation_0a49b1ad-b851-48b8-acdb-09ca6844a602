#**********************************************
#                  Snowcache State PIPE
#**********************************************
.default-snowcache-troy-variables:
  extends: .secrets-snowcache_troy
  variables:
    APPLY_PARALLELISM: "10"
    PLAN_PARALLELISM: "15"
    PROJECT_NAME: "snowcache_troy"
    WORKING_DIR: "projects/snowcache_troy"
    VC_HOST: vc.dice.ad.ea.com
    NODE_INFO_FILE: "node-info-Snowcache.json"
    ansible_main_module: avalanche_snowcache
    TF_LOG: "DEBUG"
    TF_LOG_PATH: $CI_PROJECT_DIR/tf_debug.log
    SILVERBACK_CONFIG_JSON_FILE: "snowcache.json"

prepare-json-config-snowcache-troy:
  extends: ['.default-snowcache-troy-variables', '.prepare_config']

validate-snowcache-troy:
  extends: ['.default-snowcache-troy-variables', '.validation_steps']

plan-snowcache-troy:
  needs:
    - job: validate-snowcache-troy
    - job: prepare-json-config-snowcache-troy
  extends: ['.default-snowcache-troy-variables', '.plan_steps']

apply-snowcache-troy:
  needs:
    - job: plan-snowcache-troy
    - job: prepare-json-config-snowcache-troy
  extends: ['.default-snowcache-troy-variables', '.apply_steps']

ansible-snowcache-troy:
  needs:
    - job: apply-snowcache-troy
    - job: prepare-json-config-snowcache-troy
  extends: ['.default-snowcache-troy-variables', '.ansible_common_secrets', '.run_ansible_step']

