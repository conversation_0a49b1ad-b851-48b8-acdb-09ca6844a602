"""
test_p4.py
"""

import os
import pytest
import unittest
from mock import call, MagicMock, mock_open, patch
import elipy2
from elipy2.exceptions import ELIPYException
from elipy2.p4 import P4Utils, StreamInfo


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
class TestP4(unittest.TestCase):
    def setUp(self):
        self.p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")

        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

        self.patcher__p4 = patch("elipy2.p4.P4Utils._p4")
        self.mock__p4 = self.patcher__p4.start()
        self.mock__p4.return_value = [
            {b"data": b"Random data"},
            {
                b"submittedChange": b"1337",
                b"fromFile": b"//awesome/file/path/file.txt",
                b"depotFile": b"//awesome/file/path/file.txt",
                b"action": b"test",
                b"oldAction": b"test",
                b"toFile": b"//awesome/file/path/file.txt",
                b"how": b"test",
            },
            {b"nomatch": b"onThisProperty"},
        ]

    def tearDown(self):
        self.patcher__p4.stop()
        self.patcher_upload_metrics.stop()

    def test__p4(self):
        self.p4._p4(["random", "command"])
        self.mock__p4.assert_called_once_with(["random", "command"])

    def test__switch(self):
        self.p4._switch("//random/stream")
        self.mock__p4.assert_called_once_with(["switch", "//random/stream"])

    def test_switch(self):
        self.p4.switch("//random/stream")
        self.mock__p4.assert_called_once_with(["switch", "//random/stream"])

    def test__p4_return_value(self):
        ret = self.p4._p4(["random", "command"])
        assert ret == self.mock__p4.return_value

    def test_set_environment(self):
        self.p4.set_environment()
        assert "random-perforce.server" == os.environ["P4PORT"]
        assert "random-user" == os.environ["P4USER"]
        assert "random-client" == os.environ["P4CLIENT"]

    def test_set_environment_no_values(self):
        os.environ["P4PORT"] = "test_port"
        os.environ["P4USER"] = "test_user"
        os.environ["P4CLIENT"] = "test_client"
        local_p4 = P4Utils()
        local_p4.set_environment()
        assert "test_port" == os.environ["P4PORT"]
        assert "test_user" == os.environ["P4USER"]
        assert "test_client" == os.environ["P4CLIENT"]

    def test_shelve_full(self):
        self.mock__p4.return_value = [{"Shelved change 1337 deleted"}]
        self.p4.shelve(pending_changelist="1337", discard=True, force=True)
        self.mock__p4.assert_called_with(["shelve", "-d", "-f", "-c", "1337"])

    def test_shelve_dont_discard(self):
        self.mock__p4.return_value = [{"Shelved change 1337 deleted"}]
        self.p4.shelve(pending_changelist="1337", discard=False, force=True)
        self.mock__p4.assert_called_with(["shelve", "-f", "-c", "1337"])

    def test_shelve_dont_force(self):
        self.mock__p4.return_value = [{"Shelved change 1337 deleted"}]
        self.p4.shelve(pending_changelist="1337", discard=True, force=False)
        self.mock__p4.assert_called_with(["shelve", "-d", "-c", "1337"])

    def test_change_full(self):
        self.mock__p4.return_value = [{"Change 1337 deleted"}]
        self.p4.change(pending_changelist="1337", discard=True)
        self.mock__p4.assert_called_with(["change", "-d", "1337"])

    def test_change_dont_discard(self):
        self.mock__p4.return_value = [{"Change 1337 deleted"}]
        assert self.p4.change(pending_changelist="1337", discard=False) == [{"Change 1337 deleted"}]
        self.mock__p4.assert_called_with(["change", "1337"])

    def test_reshelve(self):
        self.mock__p4.return_value = [{"1337"}]
        self.p4.reshelve(shelved_changelist="1337")
        self.mock__p4.assert_called_with(
            ["changelists", "-m", "1", "-s", "pending", "-c", "random-client"]
        )

    def test_reshelve_with_target(self):
        self.mock__p4.return_value = [{"1337"}]
        self.p4.reshelve(shelved_changelist="1337", target_changelist="1338")

    def test_reshelve_with_all(self):
        self.mock__p4.return_value = [{"1337"}]
        self.p4.reshelve(
            shelved_changelist="1337", target_changelist="1338", force=True, promote=True
        )

    def test_reshelve_no_pending_changelist(self):
        self.mock__p4.side_effect = [[{"1337"}], []]
        assert self.p4.reshelve(shelved_changelist="1337") is None
        self.mock__p4.assert_called_with(
            ["changelists", "-m", "1", "-s", "pending", "-c", "random-client"]
        )

    def test_reshelve_pending_changelist(self):
        self.mock__p4.side_effect = [[{"1337"}], [{b"change": b"1234"}]]
        assert self.p4.reshelve(shelved_changelist="1337") == "1234"

    def test_unshelve(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"//awesome/file/path/file.txt#1 - unshelved, opened for edit",
                b"level": 0,
            }
        ]
        self.p4.unshelve(pending_changelist="123")
        self.mock__p4.assert_called_once_with(["unshelve", "-f", "-s", "123"])

    def test_unshelve_no_force(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"//awesome/file/path/file.txt#1 - unshelved, opened for edit",
                b"level": 0,
            }
        ]
        self.p4.unshelve(pending_changelist="123", force=False)
        self.mock__p4.assert_called_once_with(["unshelve", "-s", "123"])

    def test_unshelve_exception(self):
        self.mock__p4.return_value = [
            {b"generic": 2, b"code": b"error", b"data": b"Change 123 unknown.\n", b"severity": 3}
        ]
        with pytest.raises(ELIPYException, match="Failed to unshelve changelist"):
            self.p4.unshelve(pending_changelist="123")

    def test_unshelve_success(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile": b"//path.blox",
                b"changeType": b"public",
                b"action": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        self.p4.unshelve(pending_changelist="123")
        self.mock__p4.assert_called_once_with(["unshelve", "-f", "-s", "123"])

    def test_unshelve_none(self):
        self.mock__p4.return_value = [
            {
                b"code": b"fewo",
                b"none": b"//awesome/file/path/file.txt#1 - unshelved, opened for edit",
                b"level": 0,
            }
        ]
        self.p4.unshelve(pending_changelist="123")
        self.mock__p4.assert_called_once_with(["unshelve", "-f", "-s", "123"])

    def test_unshelve_override_default_cl(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"//awesome/file/path/file.txt#1 - unshelved, opened for edit",
                b"level": 0,
            }
        ]
        self.p4.unshelve(pending_changelist="123", overide_default_cl=True)
        self.mock__p4.assert_called_once_with(["unshelve", "-f", "-c", "123", "-s", "123"])

    @patch("elipy2.LOGGER.warning")
    @patch("elipy2.LOGGER.info")
    def test_unshelve_code_missing_in_response_item(self, mock_info, mock_warning):
        self.mock__p4.return_value = [
            {
                b"not_code": b"info",
                b"data": b"//awesome/file/path/file.txt#1 - unshelved, opened for edit",
                b"level": 0,
            },
            {
                b"code": b"info",
                b"data": b"//awesome/file/path/file.txt#1 - unshelved, opened for edit",
                b"level": 0,
            },
        ]
        self.p4.unshelve(pending_changelist="123")
        self.mock__p4.assert_called_once_with(["unshelve", "-f", "-s", "123"])
        assert mock_info.call_count == 1
        assert mock_warning.call_count == 2

    def test_get_description_no_access(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"data": b"Access for user 'x' has not been enabled by 'p4 protect'..\n",
                b"severity": 3,
            }
        ]
        assert self.p4.get_description(changelist="123") == ""

    @patch("elipy2.LOGGER.info")
    def test_get_description_empty_p4_response(self, mock_info):
        self.mock__p4.return_value = []
        assert self.p4.get_description(changelist="123") is None
        assert mock_info.call_count == 0

    def test_get_description_no_valid(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"dta": b"Access for user 'x' has not been enabled by 'p4 protect'..\n",
                b"severity": 3,
            }
        ]
        assert self.p4.get_description(changelist="123") == ""

    def test_get_description_cl(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile0": b"//whitesharkraw/Walrus/Content/Data/Raw/Animations/WhitesharkAnimations/Assets/Animations/9b445d09a24c31fb.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        assert self.p4.get_description(changelist="1234") == "ANT:\n- description\n"

    def test_get_description_multiple_cl(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            },
            {
                b"code": b"stat",
                b"change": b"1234",
                b"desc": b"ANT:\n- extra description\n",
            },
        ]
        assert self.p4.get_description(changelist="1234") == "ANT:\n- description\n"

    def test_get_description_no_cl(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"data": b"123 - no such changelist.",
                b"severity": 3,
            }
        ]
        assert self.p4.get_description(changelist="123") == ""

    def test_get_description_no_access_2(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"""Change 3510666 by DICE\rgulliksson@casablanca-content-dice-rgullikss1 on 2018/03/02 11:42:48 *pending*

        description

Affected files ...

... //Casablanca/Code/TnT/Build/ELIPY/render_clean.py#1 edit""",
                b"level": 0,
            }
        ]
        assert self.p4.get_description(changelist="123") == "description"

    def test_get_description_other_data(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"stat",
                b"data": b"some other data",
                b"severity": 3,
            }
        ]
        assert self.p4.get_description(changelist="123") is None

    @patch("elipy2.LOGGER.info")
    def test_get_description_missing_desc_and_data(self, mock_info):
        self.mock__p4.return_value = [
            {
                b"generic": 0,
            }
        ]
        assert self.p4.get_description(changelist="123") == ""
        assert mock_info.call_count == 1

    def test_interchanges_no_valid(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"dta": b"No data field in the response\n",
                b"severity": 3,
            }
        ]
        with pytest.raises(ELIPYException):
            self.p4.interchanges("//source/path", "//target/path")

    def test_interchanges_cl(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile0": b"//whitesharkraw/Walrus/Content/Data/Raw/Animations/WhitesharkAnimations/Assets/Animations/9b445d09a24c31fb.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        self.assertEqual(
            self.p4.interchanges("//source/path/...", "//target/path/...", use_file_paths=True),
            [("1234", "DICE\\aburstrom", "ANT:\n- description\n")],
        )

    def test_interchanges_no_change(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"data": b" All revision(s) already integrated.",
                b"severity": 3,
            }
        ]
        self.assertEqual(self.p4.interchanges("//source/path", "//target/path", "1234,1234"), [])

    def test_check_for_tags(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile": b"//whitesharkraw.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        assert self.p4.check_for_tags(changelist="1234", tags=["fake"]) is True

    def test_check_for_tags_byte_input(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile": b"//whitesharkraw.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        assert self.p4.check_for_tags(changelist=b"1234", tags=["fake"]) is True

    def test_check_for_tags_no_match(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile": b"//whitesharkraw.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        assert self.p4.check_for_tags(changelist="14", tags=["fake"]) is False

    @patch("elipy2.LOGGER.error")
    def test_check_for_tags_multiple_changelists(self, mock_error):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"some info",
            },
            {
                b"code": b"stat",
                b"depotFile": b"//whitesharkraw.blox",
                b"data": b"some data",
                b"change": b"1122",
                b"desc": b"ANT:\n- description\n",
            },
            {
                b"code": b"error",
                b"data": b"something went wrong",
            },
            {
                b"code": b"stat",
                b"depotFile": b"//whitesharkraw.blox",
                b"data": b"some data",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            },
        ]
        assert self.p4.check_for_tags(changelist="1234", tags=["fake"]) is True
        assert mock_error.call_count == 1

    def test_check_for_tags_false(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"data": b" All revision(s) already integrated.",
                b"severity": 3,
            }
        ]
        assert self.p4.check_for_tags(changelist="FAKE", tags=["fake"]) is False

    def test_check_for_tags_missing_tags(self):
        assert self.p4.check_for_tags(changelist="FAKE", tags=[]) is False

    def test_check_for_tags_empty_p4_response(self):
        self.mock__p4.return_value = []
        assert self.p4.check_for_tags(changelist="FAKE", tags=["fake"]) is False

    def test_get_cl_by_tags(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile": b"//whitesharkraw.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        assert self.p4.get_cl_by_tags(tags=["fake"]) == [b"1234"]

    def test_get_cl_by_tags_false(self):
        self.mock__p4.return_value = [
            {
                b"generic": 2,
                b"code": b"error",
                b"data": b" All revision(s) already integrated.",
                b"severity": 3,
            }
        ]
        assert self.p4.get_cl_by_tags(tags=["fake"]) == []

    def test_get_cl_by_tags_empty_p4_response(self):
        self.mock__p4.return_value = []
        assert self.p4.get_cl_by_tags(tags=["fake"]) == []

    def test_get_cl_by_tags_missing_tags(self):
        assert self.p4.get_cl_by_tags(tags=[]) == []

    @patch("elipy2.LOGGER.error")
    def test_get_cl_by_tags_multiple_changelists(self, mock_error):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"data": b"some data",
                b"depotFile": b"//whitesharkraw.blox",
                b"change": b"1234",
                b"desc": b"ANT:\n- description\n",
            },
            {
                b"code": b"error",
                b"data": b"something went wrong",
            },
            {
                b"code": b"info",
                b"data": b"some info",
            },
        ]
        assert self.p4.get_cl_by_tags(tags=["fake"]) == [b"1234"]
        assert mock_error.call_count == 1

    def test_submit(self):
        ret = self.p4.submit(message="Random message")
        assert ret == self.mock__p4.return_value

    def test_submit_empty(self):
        self.mock__p4.return_value = []
        with pytest.raises(ELIPYException):
            self.p4.submit(message="Random message")

    def test_submit_nofiles(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b""""no files to submit from the default changelist""",
                b"level": 0,
            }
        ]
        ret = self.p4.submit(message="Random message")
        assert ret == self.mock__p4.return_value

    def test_submit_nofiles_v2(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b""""No files to submit.\n""",
                b"level": 0,
            }
        ]
        ret = self.p4.submit(message="Random message")
        assert ret == self.mock__p4.return_value

    def test_submit_exception(self):
        self.assertRaises(TypeError, self.p4.submit)

    def test_submit_call(self):
        ret = self.p4.submit(message="Random message")
        self.mock__p4.assert_called_once_with(["submit", "-d", "Random message"])

    def test_submit_call_revert_unchanged(self):
        ret = self.p4.submit(message="Random message", revert_unchanged_files=True)
        self.mock__p4.assert_called_once_with(
            ["submit", "-d", "Random message", "-f", "revertunchanged"]
        )

    def test_integrate(self):
        ret = self.p4.integrate("Random_branch_mapping")
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(["integrate", "-h", "-F", "-b", "Random_branch_mapping"])

    def test_integrate_no_mapping(self):
        with pytest.raises(TypeError):
            self.p4.integrate()

    def test_integrate_no_data(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"da": b""""no files to submit from the default changelist""",
                b"level": 0,
            }
        ]
        ret = self.p4.integrate(mapping="Random_branch_mapping", quiet=True)
        assert ret == self.mock__p4.return_value

    def test_integrate_datavalid(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b""""no files to submit from the default changelist""",
                b"level": 0,
            }
        ]
        ret = self.p4.integrate("Random_branch_mapping", quiet=True, to_revision="123", stream=True)
        assert ret == []
        self.mock__p4.assert_called_with(["integrate", "-h", "-S", "Random_branch_mapping", "@123"])

    def test_integrate_stream_with_parent(self):
        self.p4.integrate("a_mapping", to_revision="123", parent="a_parent", stream=True)
        self.mock__p4.assert_called_with(
            ["integrate", "-h", "-S", "a_mapping", "-P", "a_parent", "@123"]
        )

    def test_integrate_exe_too_many_rows_scanned(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"""Too many rows scanned (over 5000000); see 'p4 help maxscanrows'.""",
                b"level": 0,
            }
        ]
        with pytest.raises(ELIPYException) as exc_info:
            self.p4.integrate(mapping="Random_branch_mapping")

        assert "P4: Too many scanned rows" in str(exc_info.value)

    def test_integrate_exe(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""in both client and branch view""", b"level": 0}
        ]
        with pytest.raises(ELIPYException):
            self.p4.integrate("Random_branch_mapping")

    def test_integrate_bad_resolve_mode(self):
        self.mock__p4.return_value = []
        with self.assertRaises(ELIPYException):
            self.p4.integrate("//stream/path", resolve_mode="X")

    def test_integrate_exception_wrong_arguments(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""Missing/wrong number of arguments.""", b"level": 0}
        ]
        with pytest.raises(ELIPYException):
            self.p4.integrate("Random_branch_mapping")

    def test_integrate_exception_not_in_client_view(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""File(s) not in client view.""", b"level": 0}
        ]
        with pytest.raises(ELIPYException):
            self.p4.integrate("Random_branch_mapping")

    def test_integrate_exception_usage(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""Usage: integrate [ -c changelist# ]""", b"level": 0}
        ]
        with pytest.raises(ELIPYException):
            self.p4.integrate("Random_branch_mapping")

    def test_integrate_exception_invalid_option(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""Invalid option: -F""", b"level": 0}
        ]
        with pytest.raises(ELIPYException):
            self.p4.integrate("Random_branch_mapping")

    def test_integrate_resolve_mode_str(self):
        self.mock__p4.return_value = []
        self.p4.integrate("//stream/path", stream=True, resolve_mode="db")
        self.mock__p4.assert_called_with(["integrate", "-h", "-Rdb", "-S", "//stream/path"])

    def test_integrate_resolve_mode_lst(self):
        self.mock__p4.return_value = []
        self.p4.integrate("//stream/path", stream=True, resolve_mode=["d", "b", "s"])
        self.mock__p4.assert_called_with(["integrate", "-h", "-Rdbs", "-S", "//stream/path"])

    def test_integrate_reverse(self):
        self.p4.integrate(mapping="Random_branch_mapping", reverse=True)
        self.mock__p4.assert_called_once_with(
            ["integrate", "-h", "-r", "-F", "-b", "Random_branch_mapping"]
        )

    def test_integrate_ignore_source_history(self):
        self.p4.integrate("Random_branch_mapping", ignore_source_history=True)
        self.mock__p4.assert_called_with(
            ["integrate", "-h", "-Di", "-F", "-b", "Random_branch_mapping"]
        )

    def test_integrate_use_file_paths(self):
        self.p4.integrate("a_mapping", parent="a_parent", use_file_paths=True)
        self.mock__p4.assert_called_with(["integrate", "-h", "-F", "a_mapping", "a_parent"])

    def test_integrate_use_file_paths_with_revision(self):
        self.p4.integrate("a_mapping", to_revision="1234", parent="a_parent", use_file_paths=True)
        self.mock__p4.assert_called_with(["integrate", "-h", "-F", "a_mapping@1234", "a_parent"])

    def test_delete_workspace(self):
        self.p4.delete_workspace("jenkins-somevm-codeclient")
        self.mock__p4.assert_called_once_with(["client", "-d", "jenkins-somevm-codeclient"])

    def test_merge(self):
        ret = self.p4.merge(mapping="Random_branch_mapping")
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(["merge", "-r", "-S", "Random_branch_mapping"])

    def test_merge_reverse(self):
        ret = self.p4.merge(mapping="Random_branch_mapping", reverse=True)
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(["merge", "-r", "-S", "Random_branch_mapping"])

    def test_merge_no_data(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"dta": b""""no files to submit from the default changelist""",
                b"level": 0,
            }
        ]
        ret = self.p4.merge(
            mapping="Random_branch_mapping",
            reverse=False,
            quiet=True,
            parent="pa",
            to_revision="123",
        )
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(
            ["merge", "-S", "Random_branch_mapping", "-P", "pa", "@123"]
        )

    def test_merge_both_exe(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b""""in both client and branch views""", b"level": 0}
        ]

        with pytest.raises(ELIPYException):
            self.p4.merge(mapping="Random_branch_mapping")
        self.mock__p4.assert_called_with(["merge", "-r", "-S", "Random_branch_mapping"])

    def test_merge_both_data_correct(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b""""in boh views""", b"level": 0}
        ]
        assert [] == self.p4.merge(mapping="Random_branch_mapping")
        self.mock__p4.assert_called_with(["merge", "-r", "-S", "Random_branch_mapping"])

    def test_resolve(self):
        ret = self.p4.resolve()
        assert ret == (True, self.mock__p4.return_value)

    def test_resolve_mode(self):
        ret = self.p4.resolve(mode="m", changelist="123", quiet=True)
        assert ret == (True, self.mock__p4.return_value)
        self.mock__p4.assert_called_once_with(["resolve", "-am", "-c", "123"])

    def test_resolve_nodata(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"da": b"""resolve skipped dfgd""", b"level": 0}
        ]
        ret = self.p4.resolve(mode="m", changelist="123")
        assert ret == (True, self.mock__p4.return_value)
        self.mock__p4.assert_called_once_with(["resolve", "-am", "-c", "123"])

    def test_resolve_mode_invalid(self):
        with pytest.raises(ELIPYException):
            self.p4.resolve(mode="x")

    def test_resolve_one(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""resolve skipped dfgd""", b"level": 0}
        ]
        ret = self.p4.resolve()
        assert ret == (True, [])

    def test_resolve_path(self):
        self.p4.resolve(path="//perforce/path/...")
        self.mock__p4.assert_called_once_with(
            ["resolve", "-as", "-c", "default", "//perforce/path/..."]
        )

    def test_resolve_failed(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""resolve skipped dfgd""", b"level": 0},
            {b"code": b"info", b"data": b""""in boh views""", b"level": 0},
        ]
        ret = self.p4.resolve()
        assert ret == (False, self.mock__p4.return_value)

    def test_resolve_failed2(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""must undo move first dfgd""", b"level": 0},
            {b"code": b"info", b"data": b""""in boh views""", b"level": 0},
        ]
        ret = self.p4.resolve()
        assert ret == (False, self.mock__p4.return_value)

    def test_revert(self):
        self.p4.revert("123456")
        self.mock__p4.assert_called_once_with(["revert", "-c", "123456", "-w", "//..."])

    def test_revert_with_wipe_and_only_changed_true(self):
        with self.assertRaises(ELIPYException) as exc:
            self.p4.revert("123456", wipe=True, only_unchanged=True)
        assert "Invalid parameter combination: 'wipe' and 'only_unchanged'" in str(exc.exception)

    def test_revert_quiet(self):
        self.p4.revert(quiet=True, only_unchanged=False)
        self.mock__p4.assert_called_once_with(["revert", "-c", "default", "-w", "//..."])

    def test_revert_return_data(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile0": b"//whitesharkraw/Walrus/Content/Data/Raw/Animations/WhitesharkAnimations/Assets/Animations/9b445d09a24c31fb.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"data": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        self.assertEqual(self.p4.revert(wipe=False), [])
        self.mock__p4.assert_called_once_with(["revert", "-c", "default", "//..."])

    def test_revert_no_data_in_return(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"data-missing": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        assert self.p4.revert(wipe=False) == [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"data-missing": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        self.mock__p4.assert_called_once_with(["revert", "-c", "default", "//..."])

    @patch("elipy2.p4.P4Utils.revert")
    @patch("elipy2.p4.P4Utils.shelve")
    @patch("elipy2.p4.P4Utils.change")
    def test_wipe_client(self, mock_change, mock_shelve, mock_revert):
        changelist = "1234"
        self.mock__p4.return_value = [{b"change": changelist.encode()}]
        self.p4.wipe_client()
        mock_revert.assert_called_once()
        mock_shelve.assert_called_once_with(changelist, discard=True)
        mock_change.assert_called_once_with(pending_changelist=changelist, discard=True)

    def test_reopen(self):
        self.p4.reopen()
        self.mock__p4.assert_called_once_with(["reopen", "-c", "default", "//..."])

    def test_sync(self):
        self.p4.sync("path")
        self.mock__p4.assert_called_once_with(["sync", "path"])

    def test_sync_to_revision(self):
        self.mock__p4.return_value = [{b"code": b"all-ok"}]
        self.p4.sync("path", to_revision="12")
        self.mock__p4.assert_called_once_with(["sync", "path@12"])

    @patch("json.dumps")
    @patch("elipy2.p4.open", new_callable=mock_open())
    @patch("os.getenv")
    def test_sync_write_to_logfile(self, mock_getenv, mock_open, mock_dumps):
        mock_getenv.return_value = "workspace_path"
        self.mock__p4.return_value = [
            {b"byteK1": b"byteV1"},
            {b"byteK2": "stringV1"},
            {"stringK1": b"byteV2"},
            {"stringK2": "stringV2"},
        ]
        mock_dumps.side_effect = [
            {"json": "response1"},
            {"json": "response2"},
            {"json": "response3"},
            {"json": "response4"},
        ]
        self.p4.sync("path")
        mock_open.assert_has_calls(
            [
                call("workspace_path\\logs\\p4_sync.log", "a"),
            ],
            any_order=True,
        )
        mock_dumps.assert_has_calls(
            [
                call({"byteK1": "byteV1"}, indent=4),
                call({"byteK2": "stringV1"}, indent=4),
                call({"stringK1": "byteV2"}, indent=4),
                call({"stringK2": "stringV2"}, indent=4),
            ]
        )
        mock_open.return_value.__enter__().write.assert_has_calls(
            [
                call({"json": "response1"}),
                call({"json": "response2"}),
                call({"json": "response3"}),
                call({"json": "response4"}),
            ]
        )

    def test_sync_error_ignore(self):
        self.mock__p4.return_value = [{b"code": b"error", b"data": b"up-to-date"}]
        self.p4.sync("path")
        self.mock__p4.assert_called_once_with(["sync", "path"])

    def test_sync_error_failure(self):
        self.mock__p4.return_value = [{b"code": b"error", b"data": b"some-other-data"}]
        with pytest.raises(ELIPYException):
            self.p4.sync("path")

    def test_edit(self):
        self.p4.edit("target_file")
        self.mock__p4.assert_called_once_with(["edit", "target_file"])

    def test_edit_failure(self):
        self.mock__p4.return_value = [
            {
                b"code": b"error",
                b"data": b"Client 'test_workspace' unknown - use 'client' command to create it.\n",
                b"severity": 3,
                b"generic": 2,
            }
        ]
        with pytest.raises(ELIPYException):
            self.p4.edit("target_file")
        self.mock__p4.assert_called_once_with(["edit", "target_file"])

    @patch("elipy2.p4.P4Utils.edit")
    def test_edit_integrated_files(self, mock_edit):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"clientFile": b"stat",
                b"depotFile0": b"//whitesharkraw/Walrus/Content/Data/Raw/Animations/WhitesharkAnimations/Assets/Animations/9b445d09a24c31fb.blox",
                b"changeType": b"public",
                b"action": b"integrate",
            }
        ]
        self.p4.edit_integrated_files()
        self.mock__p4.assert_called_once_with(["opened"])
        mock_edit.assert_called_once_with("stat")

    @patch("elipy2.p4.P4Utils.edit")
    def test_edit_integrated_files_none_found(self, mock_edit):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"clientFile": b"stat",
                b"depotFile0": b"//whitesharkraw/Walrus/Content/Data/Raw/Animations/WhitesharkAnimations/Assets/Animations/9b445d09a24c31fb.blox",
                b"changeType": b"public",
                b"action": b"add",
            }
        ]
        self.p4.edit_integrated_files()
        self.mock__p4.assert_called_once_with(["opened"])
        assert mock_edit.call_count == 0

    def test_clean(self):
        self.p4.clean()
        self.mock__p4.assert_called_once_with(["clean"])

    def test_clean_quiet(self):
        self.p4.clean(quiet=True)
        self.mock__p4.assert_called_once_with(["clean"])

    def test_clean_no_data(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"daa": b"""resolve skipped dfgd""", b"level": 0}
        ]
        self.p4.clean(quiet=True)
        self.mock__p4.assert_called_once_with(["clean"])

    def test_clean_folder(self):
        self.mock__p4.return_value = [
            {
                b"status": b"pending",
                b"code": b"stat",
                b"depotFile0": b"//whitesharkraw/Walrus/Content/Data/Raw/Animations/WhitesharkAnimations/Assets/Animations/9b445d09a24c31fb.blox",
                b"changeType": b"public",
                b"action0": b"edit",
                b"shelved": b"",
                b"client": b"dst-walrus-raw-dice-aburstrom1",
                b"user": b"DICE\\aburstrom",
                b"time": b"1521532874",
                b"rev0": b"10",
                b"type0": b"text",
                b"data": b"1234",
                b"desc": b"ANT:\n- description\n",
            }
        ]
        self.p4.clean(folder="dir")
        self.mock__p4.assert_called_once_with(["clean", "dir"])

    def test_clean_no_keys(self):
        self.mock__p4.return_value = [
            {b"dargeta": b"Random data"},
            {
                b"submittedChange": b"1337",
                b"fromFile": b"//awesome/file/path/file.txt",
                b"depotFile": b"//awesome/file/path/file.txt",
                b"actgreion": b"test",
                b"oldAction": b"test",
                b"toFile": b"//awesome/file/path/file.txt",
                b"how": b"test",
            },
            {b"nomatch": b"onThisProperty"},
        ]
        self.p4.clean()
        self.mock__p4.assert_called_once_with(["clean"])

    def test_copy_mapping(self):
        ret = self.p4.copy_mapping(mapping="Random_branch_mapping", reverse=True)
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(["copy", "-b", "Random_branch_mapping", "-r", "-F"])

    def test_copy_mapping_args(self):
        ret = self.p4.copy_mapping(
            mapping="Random_branch_mapping",
            reverse=False,
            quiet=True,
            to_revision="123",
            force=True,
            stream=True,
        )
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(
            ["copy", "-S", "Random_branch_mapping", "-f", "-F", "@123"]
        )

    def test_copy_mapping_nodata(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"ta": b""""in both nch views""", b"level": 0}
        ]
        ret = self.p4.copy_mapping(mapping="Random_branch_mapping", reverse=True)
        assert ret == self.mock__p4.return_value
        self.mock__p4.assert_called_with(["copy", "-b", "Random_branch_mapping", "-r", "-F"])

    def test_copy_mapping_data_valid(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b""""in both nch views""", b"level": 0}
        ]
        ret = self.p4.copy_mapping(mapping="Random_branch_mapping", reverse=True)
        assert ret == []
        self.mock__p4.assert_called_with(["copy", "-b", "Random_branch_mapping", "-r", "-F"])

    def test_copy_mapping_data_exec(self):
        self.mock__p4.return_value = [
            {b"code": b"info", b"data": b"""""in both client and branch view""", b"level": 0}
        ]
        with pytest.raises(ELIPYException):
            self.p4.copy_mapping(mapping="Random_branch_mapping", reverse=True)
        self.mock__p4.assert_called_with(["copy", "-b", "Random_branch_mapping", "-r", "-F"])

    def test_copy_mapping_data_not_merges(self):
        self.mock__p4.return_value = [
            {
                b"code": b"info",
                b"data": b"""""stream test cannot 'copy' over outstanding 'merge'""",
                b"level": 0,
            }
        ]
        with pytest.raises(ELIPYException):
            self.p4.copy_mapping(mapping="Random_branch_mapping", reverse=True)
        self.mock__p4.assert_called_with(["copy", "-b", "Random_branch_mapping", "-r", "-F"])

    def test_copy_mapping_must_sync(self):
        self.mock__p4.return_value = [
            {
                b"fromFile": b"//data/dev/Source/DevFile.dbx",
                b"depotFile": b"//data/stage/Source/StageFile.dbx",
                b"action": b"integrate",
            },
            {
                b"data": b"//jenkins-kr2-587886/kindata/Source/WaterTank_04.dbx - must sync before integrating."
            },
        ]
        with pytest.raises(ELIPYException) as exc_info:
            self.p4.copy_mapping(mapping="Random_branch_mapping", reverse=True)

        assert "must sync before integrating" in str(exc_info.value)

    def test_copy(self):
        self.p4.copy("//stream/path")
        self.mock__p4.assert_called_with(["copy", "-f", "-S", "//stream/path"])

    def test_copy_args(self):
        self.p4.copy("Random_branch_mapping", "123")
        self.mock__p4.assert_called_with(["copy", "-f", "-S", "Random_branch_mapping", "@123"])

    @patch("elipy2.frostbite_core.get_game_root")
    @patch("os.getcwd")
    @patch("os.chdir")
    def test_clean_perforce(self, mock_chdir, mock_cwd, mock_get_game_root):
        mock_cwd.return_value = "here"
        mock_get_game_root.return_value = "tnt_root"
        self.mock__p4.return_value = 0
        self.p4.clean_perforce(cleanpath="path")
        calls = [call(["info", "-s"]), call(["clean"])]
        self.mock__p4.assert_has_calls(calls, any_order=True)

        calls = [
            call(os.path.join(elipy2.frostbite_core.get_game_root(), "path").encode()),
            call("here"),
        ]
        mock_chdir(calls, any_order=True)

        self.mock__p4.return_value = 1
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.p4.clean_perforce(cleanpath="path")

    def test_reconcile_success1(self):
        path = "some\\path\\..."
        self.p4.reconcile(path)
        self.mock__p4.assert_called_with(["reconcile", "-f", "-c", "default", path])

    def test_reconcile_success2(self):
        path = "some\\path\\..."
        self.p4.reconcile(path, changelist="123", options=["a", "e", "d"])
        self.mock__p4.assert_called_with(["reconcile", "-a", "-e", "-d", "-c", "123", path])

    def test_reconcile_fail(self):
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.p4.reconcile("some\\path\\...", options=["invalid"])

    @patch("elipy2.LOGGER.info")
    def test_reconcile_post_result(self, mock_info):
        self.mock__p4.return_value = [{b"reconcile": b"response"}]
        path = "some\\path\\..."
        self.p4.reconcile(path)
        assert mock_info.call_count == 1

    @patch("elipy2.LOGGER.info")
    def test_reconcile_quiet(self, mock_info):
        self.mock__p4.return_value = [{b"reconcile": b"response"}]
        path = "some\\path\\..."
        self.p4.reconcile(path, quiet=True)
        assert mock_info.call_count == 0

    def test_remotes_no_data_returned(self):
        self.mock__p4.return_value = []
        spec_pattern = "spec_pattern"
        assert self.p4.remotes(spec_pattern) == []
        self.mock__p4.assert_called_with(["remotes", "-m", "1", "-E", spec_pattern])

    def test_remotes_with_data(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-frostbite",
                b"Address": b"oh-p4edge-fb.eu.ad.ea.com:2001",
                b"Description": b"       Created by EU\\kcropper.\n",
                b"Owner": b"EU\\kcropper",
                b"RemoteUser": b"",
                b"Options": b"unlocked nocompress copyrcs",
                b"Update": b"1605871676",
                b"Access": b"0",
                b"LastFetch": b"0",
                b"LastPush": b"0",
            },
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-roboto",
                b"Address": b"oh-p4proxy-perforce-ghost.eu.ad.ea.com:2031",
                b"Description": b"Created by EU\\kcropper.\n",
                b"Owner": b"EU\\kcropper",
                b"RemoteUser": b"",
                b"Options": b"unlocked nocompress copyrcs",
                b"Update": b"1569596650",
                b"Access": b"1569865516",
                b"LastFetch": b"3865134",
                b"LastPush": b"0",
            },
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-test-sandbox",
                b"Address": b"eucr-perforce.eu.ad.ea.com:1666",
                b"Description": b"Created by EU\\mburas.\n",
                b"Owner": b"EU\\mburas",
                b"RemoteUser": b"",
                b"Options": b"unlocked nocompress copyrcs",
                b"Update": b"1605006295",
                b"Access": b"0",
                b"LastFetch": b"0",
                b"LastPush": b"0",
            },
        ]
        spec_pattern = "spec_pattern"
        data = self.p4.remotes(spec_pattern)
        assert len(data) == len(self.mock__p4.return_value)
        assert "excalibur-frostbite" in data
        assert "excalibur-roboto" in data
        assert "excalibur-test-sandbox" in data
        self.mock__p4.assert_called_with(["remotes", "-m", "1", "-E", spec_pattern])

    @patch("elipy2.LOGGER.info")
    def test_remotes_log(self, mock_logger_info):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-frostbite",
            },
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-roboto",
            },
        ]
        spec_pattern = "spec_pattern"
        self.p4.remotes(spec_pattern)
        assert mock_logger_info.call_count == 2

    @patch("elipy2.LOGGER.info")
    def test_remotes_quiet(self, mock_logger_info):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-frostbite",
            },
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-roboto",
            },
        ]
        spec_pattern = "spec_pattern"
        self.p4.remotes(spec_pattern, quiet=True)
        assert mock_logger_info.call_count == 0

    @patch("elipy2.LOGGER.info")
    def test_remotes_not_force_case_insensitive(self, mock_logger_info):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-frostbite",
            },
            {
                b"code": b"stat",
                b"RemoteID": b"excalibur-roboto",
            },
        ]
        spec_pattern = "spec_pattern"
        self.p4.remotes(spec_pattern, force_case_insensitive=False)
        self.mock__p4.assert_called_with(["remotes", "-m", "1", "-e", spec_pattern])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_push_basic(self, mock_dvcs_errors):
        self.mock__p4.return_value = [
            {b"code": b"stat", b"change": b"117926", b"renamedChange": b"-1"},
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""},
            {b"code": b"error", b"data": b"Some other error."},
        ]
        remote_spec = "spec_pattern"
        assert self.p4.push(remote_spec) == [
            {b"code": b"stat", b"change": b"117926", b"renamedChange": b"-1"},
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""},
            {b"code": b"error", b"data": b"Some other error."},
        ]
        self.mock__p4.assert_called_once_with(["push", "-r", remote_spec])
        mock_dvcs_errors.assert_called_once_with(
            "push",
            remote_spec,
            [
                {b"code": b"stat", b"change": b"117926", b"renamedChange": b"-1"},
                {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""},
                {b"code": b"error", b"data": b"Some other error."},
            ],
            False,
        )

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_push_quiet(self, mock_dvcs_errors):
        self.mock__p4.return_value = [
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}
        ]
        remote_spec = "spec_pattern"
        assert self.p4.push(remote_spec, quiet=True) == [
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}
        ]
        mock_dvcs_errors.assert_called_once_with(
            "push",
            remote_spec,
            [{b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}],
            True,
        )

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_push_dry_run(self, mock_dvcs_errors):
        remote_spec = "spec_pattern"
        self.p4.push(remote_spec, dry_run=True)
        self.mock__p4.assert_called_with(["push", "-n", "-r", remote_spec])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_push_verbose(self, mock_dvcs_errors):
        remote_spec = "spec_pattern"
        self.p4.push(remote_spec, verbose=True)
        self.mock__p4.assert_called_with(["push", "-v", "-r", remote_spec])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_push_extra_args(self, mock_dvcs_errors):
        remote_spec = "spec_pattern"
        self.p4.push(remote_spec, extra_args=["-Ocfi"])
        self.mock__p4.assert_called_with(["push", "-r", remote_spec, "-Ocfi"])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_push_dvcs_errors(self, mock_dvcs_errors):
        mock_dvcs_errors.side_effect = ELIPYException()
        remote_spec = "spec_pattern"
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.p4.push(remote_spec)

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_fetch_basic(self, mock_dvcs_errors):
        self.mock__p4.return_value = [
            {b"code": b"stat", b"change": b"117926", b"renamedChange": b"-1"},
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""},
            {b"code": b"error", b"data": b"Some other error."},
        ]
        remote_spec = "spec_pattern"
        assert self.p4.fetch(remote_spec) == [
            {b"code": b"stat", b"change": b"117926", b"renamedChange": b"-1"},
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""},
            {b"code": b"error", b"data": b"Some other error."},
        ]
        self.mock__p4.assert_called_once_with(["fetch", "-r", remote_spec, "-k"])
        mock_dvcs_errors.assert_called_once_with(
            "fetch",
            remote_spec,
            [
                {b"code": b"stat", b"change": b"117926", b"renamedChange": b"-1"},
                {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""},
                {b"code": b"error", b"data": b"Some other error."},
            ],
            False,
        )

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_fetch_quiet(self, mock_dvcs_errors):
        self.mock__p4.return_value = [
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}
        ]
        remote_spec = "spec_pattern"
        assert self.p4.fetch(remote_spec, quiet=True) == [
            {b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}
        ]
        mock_dvcs_errors.assert_called_once_with(
            "fetch",
            remote_spec,
            [{b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}],
            True,
        )

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_fetch_dry_run(self, mock_dvcs_errors):
        remote_spec = "spec_pattern"
        self.p4.fetch(remote_spec, dry_run=True)
        self.mock__p4.assert_called_with(["fetch", "-n", "-r", remote_spec, "-k"])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_fetch_verbose(self, mock_dvcs_errors):
        remote_spec = "spec_pattern"
        self.p4.fetch(remote_spec, verbose=True)
        self.mock__p4.assert_called_with(["fetch", "-v", "-r", remote_spec, "-k"])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_fetch_extra_args(self, mock_dvcs_errors):
        remote_spec = "spec_pattern"
        self.p4.fetch(remote_spec, extra_args=["-Ocfi"])
        self.mock__p4.assert_called_with(["fetch", "-r", remote_spec, "-k", "-Ocfi"])

    @patch("elipy2.p4.P4Utils._dvcs_errors")
    def test_fetch_dvcs_errors(self, mock_dvcs_errors):
        mock_dvcs_errors.side_effect = ELIPYException()
        remote_spec = "spec_pattern"
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.p4.fetch(remote_spec)

    @patch("elipy2.LOGGER.info")
    def test_dvcs_errors_push(self, mock_logger_info):
        p4_result = [{b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}]
        remote_spec = "spec_pattern"
        self.p4._dvcs_errors("push", remote_spec, p4_result)
        assert mock_logger_info.call_count == 1

    @patch("elipy2.LOGGER.info")
    def test_dvcs_errors_quiet(self, mock_logger_info):
        p4_result = [{b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}]
        remote_spec = "spec_pattern"
        self.p4._dvcs_errors("push", remote_spec, p4_result, quiet=True)
        assert mock_logger_info.call_count == 0

    def test_dvcs_errors_fetch(self):
        p4_result = [{b"code": b"stat", b"change": b"118645", b"alreadyPresent": b""}]
        remote_spec = "spec_pattern"
        self.p4._dvcs_errors("fetch", remote_spec, p4_result)

    @patch("elipy2.LOGGER.info")
    def test_dvcs_errors_non_failing_error(self, mock_logger_info):
        p4_result = [
            {
                b"code": b"error",
                b"data": b"1234 - no revision(s) above those at that changelist number.\n",
                b"severity": 2,
                b"generic": 17,
            }
        ]
        remote_spec = "spec_pattern"
        self.p4._dvcs_errors("push", remote_spec, p4_result)
        assert mock_logger_info.call_count == 1

    @patch("elipy2.LOGGER.info")
    def test_dvcs_errors_non_failing_error_quiet(self, mock_logger_info):
        p4_result = [
            {
                b"code": b"error",
                b"data": b"1234 - no revision(s) above those at that changelist number.\n",
                b"severity": 2,
                b"generic": 17,
            }
        ]
        remote_spec = "spec_pattern"
        self.p4._dvcs_errors("push", remote_spec, p4_result, quiet=True)
        assert mock_logger_info.call_count == 0

    def test_dvcs_errors_remote_spec_failure(self):
        p4_result = [{b"code": b"error", b"data": b"spec_pattern doesn't exist"}]
        remote_spec = "spec_pattern"
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.p4._dvcs_errors("push", remote_spec, p4_result)

    def test_dvcs_errors_generic_failure(self):
        p4_result = [{b"code": b"error", b"data": b"Some other error"}]
        remote_spec = "spec_pattern"
        with pytest.raises(elipy2.exceptions.ELIPYException):
            self.p4._dvcs_errors("push", remote_spec, p4_result)

    @patch("elipy2.LOGGER.warning")
    def test_dvcs_errors_no_code(self, mock_logger_warning):
        p4_result = [{b"other_key": b"value"}]
        remote_spec = "spec_pattern"
        self.p4._dvcs_errors("push", remote_spec, p4_result)
        assert mock_logger_warning.call_count == 1

    def test_changes_range_with_end(self):
        self.mock__p4.return_value = [
            {b"change": b"200"},
            {b"change": b"175"},
            {b"change": b"150"},
            {b"change": b"125"},
            {b"change": b"100"},
        ]
        path = "//some/path"
        cl_start = "100"
        cl_end = "200"
        assert self.p4.changes_range(path, cl_start, changelist_end=cl_end) == [125, 150, 175, 200]
        self.mock__p4.assert_called_with(
            ["changes", "//some/path" + "/...@" + cl_start + "," + cl_end]
        )

    def test_changes_range_with_head(self):
        self.mock__p4.return_value = [
            {b"change": b"200"},
            {b"change": b"175"},
            {b"change": b"150"},
            {b"change": b"125"},
            {b"change": b"100"},
        ]
        path = "//some/path"
        cl_start = "100"
        assert self.p4.changes_range(path, cl_start) == [125, 150, 175, 200]
        self.mock__p4.assert_called_with(["changes", "//some/path" + "/...@" + cl_start + ",#head"])

    def test_changes_range_start_not_in_range(self):
        self.mock__p4.return_value = [
            {b"change": b"200"},
            {b"change": b"175"},
            {b"change": b"150"},
            {b"change": b"125"},
        ]
        path = "//some/path"
        cl_start = "100"
        cl_end = "200"
        assert self.p4.changes_range(path, cl_start, changelist_end=cl_end) == [125, 150, 175, 200]
        self.mock__p4.assert_called_with(
            ["changes", "//some/path" + "/...@" + cl_start + "," + cl_end]
        )

    def test_changes_range_keep_start(self):
        self.mock__p4.return_value = [
            {b"change": b"200"},
            {b"change": b"175"},
            {b"change": b"150"},
            {b"change": b"125"},
            {b"change": b"100"},
        ]
        path = "//some/path"
        cl_start = "100"
        cl_end = "200"
        assert self.p4.changes_range(path, cl_start, changelist_end=cl_end, remove_start=False) == [
            100,
            125,
            150,
            175,
            200,
        ]
        self.mock__p4.assert_called_with(
            ["changes", "//some/path" + "/...@" + cl_start + "," + cl_end]
        )

    @patch("elipy2.LOGGER.info")
    def test_unresolved_without_files(self, mock_info):
        self.mock__p4.return_value = [
            {
                b"code": b"error",
                b"data": b"No file(s) to resolve.\n",
                b"severity": 2,
                b"generic": 17,
            }
        ]
        self.assertEqual(self.p4.unresolved(), [])
        assert mock_info.call_count == 1

    @patch("elipy2.LOGGER.info")
    def test_unresolved_without_files_error_data_missing(self, mock_info):
        self.mock__p4.return_value = [
            {
                b"code": b"error",
                b"data-missing": b"No file(s) to resolve.\n",
                b"severity": 2,
                b"generic": 17,
            }
        ]
        self.assertEqual(self.p4.unresolved(), [])
        assert mock_info.call_count == 0

    def test_unresolved_with_files(self):
        local_path_prefix = "E:\\dev\\awesome\\file\\path\\"
        depot_path_prefix = "//awesome/file/path/"
        filenames = ("File.dbx", "AnotherFile.dbx", "TextFile.txt")
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"clientFile": f"{local_path_prefix}{filename}".encode(),
                b"fromFile": f"{depot_path_prefix}{filename}".encode(),
                b"startFromRev": b"124",
                b"endFromRev": b"125",
                b"resolveType": b"content",
                b"resolveFlag": b"c",
                b"contentResolveType": b"3waytext",
            }
            for filename in filenames
        ]

        dbx_files = self.p4.unresolved("*.dbx", resolve_type="content")

        self.assertEqual(len(dbx_files), len([f for f in filenames if f.endswith(".dbx")]))
        for filename, dbx_file in zip(filenames, dbx_files):
            self.assertEqual(f"{local_path_prefix}{filename}", dbx_file.local_path)
            self.assertEqual(f"{depot_path_prefix}{filename}", dbx_file.depot_path)

    def test_unresolved_with_files_some_missing_local(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\File.dbx",
                b"fromFile": b"//perforce/path/File.dbx",
                b"resolveType": b"content",
            },
            {
                b"code": b"stat",
                b"fromFile": b"//perforce/path/AnotherFile.dbx",
                b"resolveType": b"content",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\TextFile.txt",
                b"fromFile": b"//perforce/path/TextFile.txt",
                b"resolveType": b"content",
            },
        ]
        dbx_files = self.p4.unresolved("*.dbx", resolve_type="content")
        assert len(dbx_files) == 1

    def test_unresolved_with_files_other_resolve_type(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\File.dbx",
                b"fromFile": b"//perforce/path/File.dbx",
                b"resolveType": b"content",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\AnotherFile.dbx",
                b"fromFile": b"//perforce/path/AnotherFile.dbx",
                b"resolveType": b"other-type",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\TextFile.txt",
                b"fromFile": b"//perforce/path/TextFile.txt",
                b"resolveType": b"content",
            },
        ]
        dbx_files = self.p4.unresolved("*.dbx", resolve_type="content")
        assert len(dbx_files) == 1

    def test_unresolved_with_files_no_resolve_type_for_file(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\File.dbx",
                b"fromFile": b"//perforce/path/File.dbx",
                b"resolveType": b"content",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\AnotherFile.dbx",
                b"fromFile": b"//perforce/path/AnotherFile.dbx",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\TextFile.txt",
                b"fromFile": b"//perforce/path/TextFile.txt",
                b"resolveType": b"content",
            },
        ]
        dbx_files = self.p4.unresolved("*.dbx", resolve_type="content")
        assert len(dbx_files) == 1

    def test_unresolved_with_files_no_resolve_type_in_input(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\File.dbx",
                b"fromFile": b"//perforce/path/File.dbx",
                b"resolveType": b"content",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\AnotherFile.dbx",
                b"fromFile": b"//perforce/path/AnotherFile.dbx",
            },
            {
                b"code": b"stat",
                b"clientFile": b"C:\\local\\path\\TextFile.txt",
                b"fromFile": b"//perforce/path/TextFile.txt",
                b"resolveType": b"content",
            },
        ]
        dbx_files = self.p4.unresolved("*.dbx")
        assert len(dbx_files) == 2

    def test_stream_info_doesnt_exist(self):
        self.mock__p4.return_value = [
            {
                b"code": b"error",
                b"data": b"Depot 'stream' doesn't exist.\n",
                b"severity": 3,
                b"generic": 2,
            }
        ]
        with self.assertRaises(ELIPYException):
            self.p4.stream_info("//stream/path")

    def test_stream_info(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"Stream": b"//data/kin/future/future-dev-content",
                b"Name": b"future-dev-content",
                b"Parent": b"//data/kin/dev/kin-dev",
                b"Type": b"development",
                b"Options": b"allsubmit unlocked notoparent fromparent mergedown",
            }
        ]

        self.assertEqual(
            self.p4.stream_info(),
            StreamInfo(
                name="future-dev-content",
                stream="//data/kin/future/future-dev-content",
                parent="//data/kin/dev/kin-dev",
                type="development",
                options=tuple("allsubmit unlocked notoparent fromparent mergedown".split()),
            ),
        )

    def test_opened(self):
        self.mock__p4.return_value = [
            {
                b"code": b"stat",
                b"depotFile": b"//perforce/depot/path/file_1.txt",
                b"clientFile": b"//client-name/path/file_1.txt",
                b"rev": b"4",
                b"action": b"integrate",
            },
            {
                b"code": b"stat",
                b"depotFile": b"//perforce/depot/path/file_2.txt",
                b"clientFile": b"//client-name/path/file_2.txt",
                b"rev": b"4",
                b"action": b"integrate",
            },
        ]
        assert self.p4.opened(path="//perforce/depot/path/...") == [
            "//perforce/depot/path/file_1.txt",
            "//perforce/depot/path/file_2.txt",
        ]

    def test_opened_empty_response(self):
        self.mock__p4.return_value = []
        assert self.p4.opened(path="//perforce/depot/path/...") == []

    def test_opened_unexpected_response(self):
        self.mock__p4.return_value = "some string"
        with pytest.raises(ELIPYException):
            self.p4.opened(path="//perforce/depot/path/...")

    def test_setcounter(self):
        self.mock__p4.return_value = [
            {
                b"value": b"counter_value",
            },
        ]
        self.p4.setcounter("counter_name", "counter_value")

    def test_setcounter_no_value(self):
        with pytest.raises(ELIPYException):
            self.p4.setcounter("counter_name", None)

    def test_setcounter_wrong_return_value(self):
        self.mock__p4.return_value = [
            {
                b"value": b"wrong_value",
            },
        ]
        with pytest.raises(ELIPYException):
            self.p4.setcounter("counter_name", "counter_value")

    @patch("elipy2.LOGGER.info")
    def test_getcounter(self, mock_info):
        self.mock__p4.return_value = [
            {
                b"value": b"1234",
            },
        ]
        assert self.p4.getcounter("counter_name") == "1234"
        assert mock_info.call_count == 2

    @patch("elipy2.LOGGER.info")
    def test_getcounter_none_found(self, mock_info):
        self.mock__p4.return_value = []
        self.p4.getcounter("counter_name")
        assert mock_info.call_count == 1

    @patch("elipy2.LOGGER.info")
    def test_p4set(self, mock_info):
        self.mock__p4.return_value = [
            "P4CLIENT = client_name (config 'C:\\path\\to\\.p4config')",
            "P4CONFIG =.p4config (config C:\\path\\to\\.p4config' )",
            "P4PORT = perforce-port:1234 (config 'C:\\path\\to\\.p4config')",
            "P4USER = DICE\\bvaksdal (C:\\path\\to\\.p4config')",
            "P4_perforce-port:1234_CHARSET = none(set)",
            "filesys.windows.lfn: 10",
        ]
        self.p4.p4set()
        assert mock_info.call_count == 6

    @patch("elipy2.LOGGER.info")
    def test_ignores(self, mock_info):
        self.mock__p4.return_value = [
            "... /.p4root / ...",
            "... /.p4root",
            "... /.p4config",
        ]
        self.p4.ignores()
        assert mock_info.call_count == 3

    @patch("os.remove", MagicMock())
    @patch("marshal.dump")
    @patch("elipy2.p4.open", new_callable=mock_open)
    def test_set_description(self, mock_open, mock_dump):
        self.mock__p4.side_effect = [
            [
                {
                    b"Change": b"1234",
                    b"Description": b"old_description",
                }
            ],
            [],
        ]
        self.p4.set_description("1234", "new_description")
        mock_dump.assert_called_once_with(
            {
                b"Change": b"1234",
                b"Description": b"new_description",
            },
            mock_open.return_value,
            0,
        )

    @patch("os.remove", MagicMock())
    @patch("marshal.dump")
    @patch("elipy2.p4.open", new_callable=mock_open)
    def test_set_description_no_description_returned(self, mock_open, mock_dump):
        self.mock__p4.side_effect = [
            [
                {
                    b"Change": b"1234",
                    b"Description_missing": b"missing",
                }
            ],
            [],
        ]
        self.p4.set_description("1234", "new_description")
        mock_dump.assert_called_once_with(
            {
                b"Change": b"1234",
                b"Description_missing": b"missing",
            },
            mock_open.return_value,
            0,
        )

    @patch("os.remove", MagicMock())
    @patch("elipy2.LOGGER.error")
    @patch("marshal.dump", MagicMock())
    @patch("elipy2.p4.open", new_callable=mock_open)
    def test_set_description_code_error(self, mock_open, mock_error):
        self.mock__p4.side_effect = [
            [
                {
                    b"Change": b"1234",
                    b"Description": b"old_description",
                }
            ],
            [{b"code": b"error", b"data": b"error_info"}],
        ]
        self.p4.set_description("1234", "new_description")
        assert mock_error.call_count == 1

    @patch("os.remove", MagicMock())
    @patch("elipy2.LOGGER.info")
    @patch("marshal.dump", MagicMock())
    @patch("elipy2.p4.open", new_callable=mock_open)
    def test_set_description_code_info(self, mock_open, mock_info):
        self.mock__p4.side_effect = [
            [
                {
                    b"Change": b"1234",
                    b"Description": b"old_description",
                }
            ],
            [{b"code": b"info", b"data": b"general_info"}],
        ]
        self.p4.set_description("1234", "new_description")
        assert mock_info.call_count == 1

    @patch("os.remove", MagicMock())
    @patch("elipy2.LOGGER.info")
    @patch("marshal.dump", MagicMock())
    @patch("elipy2.p4.open", new_callable=mock_open)
    def test_set_description_code_stat(self, mock_open, mock_info):
        self.mock__p4.side_effect = [
            [
                {
                    b"Change": b"1234",
                    b"Description": b"old_description",
                }
            ],
            [{b"code": b"stat", b"data": b"stat returned"}],
        ]
        self.p4.set_description("1234", "new_description")
        assert mock_info.call_count == 0

    @patch("os.remove", MagicMock())
    @patch("elipy2.LOGGER.info")
    @patch("marshal.dump", MagicMock())
    @patch("elipy2.p4.open", new_callable=mock_open)
    def test_set_description_no_code(self, mock_open, mock_info):
        self.mock__p4.side_effect = [
            [
                {
                    b"Change": b"1234",
                    b"Description": b"old_description",
                }
            ],
            [
                {
                    b"other": b"response",
                }
            ],
        ]
        self.p4.set_description("1234", "new_description")
        assert mock_info.call_count == 0


@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestP4Other:
    @patch("elipy2.p4.P4Utils._p4")
    def test_describe(self, mock__p4):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        mock__p4.return_value = [
            {
                b"code": b"stat",
                b"change": b"13389716",
                b"user": b"random-user",
                b"client": b"random-client",
                b"time": b"1642523563",
                b"desc": b"test\n",
                b"status": b"pending",
                b"changeType": b"public",
                b"shelved": b"",
                b"IsPromoted": b"1",
                b"depotFile0": b"//fbstream/dev-na/.p4ignore",
                b"action0": b"edit",
                b"type0": b"text",
                b"rev0": b"65",
                b"depotfile1": b"//fbstream/dev-na/Frostbite.diceconfig",
                b"action1": b"edit1",
                b"type1": b"text",
                b"rev1": b"130",
            }
        ]
        result = p4.describe("13389716")
        assert result[0][b"rev1"] == b"130"

    @patch("elipy2.p4.P4Utils._p4")
    def test_describe_no_changelist(self, mock__p4):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        mock__p4.return_value = [
            {
                b"code": b"error",
                b"data": b"133897161 - no such changelist.\n",
                b"severity": 2,
                b"generic": 17,
            }
        ]
        result = p4.describe("13389716")
        assert result[0][b"code"] == b"error"

    @patch("elipy2.p4.P4Utils._p4")
    def test_describe_shelved(self, mock__p4):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        mock__p4.return_value = [
            {
                b"code": b"stat",
                b"change": b"13389716",
                b"user": b"random-user",
                b"client": b"random-client",
                b"time": b"1642523563",
                b"desc": b"test\n",
                b"status": b"pending",
                b"changeType": b"public",
                b"shelved": b"",
                b"IsPromoted": b"1",
                b"depotFile0": b"//fbstream/dev-na/.p4ignore",
                b"action0": b"edit",
                b"type0": b"text",
                b"rev0": b"65",
                b"fileSize0": b"1931",
                b"digest0": b"FFB26FE348EC5AAD5E98957F331D9FB7",
                b"depotFile1": b"//fbstream/dev-na/Frostbite.diceconfig",
                b"action1": b"edit",
                b"type1": b"text",
                b"rev1": b"130",
                b"fileSize1": b"13942",
                b"digest1": b"42D199E7E9B955D497078D6109631FE2",
            }
        ]
        result = p4.describe("13389716")
        assert result[0][b"digest1"] == b"42D199E7E9B955D497078D6109631FE2"

    @patch("elipy2.p4.P4Utils._p4")
    def test_describe_show_shelved_files(self, mock__p4):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        p4.describe("13389716", show_shelved_files=True)
        mock__p4.assert_called_once_with(["describe", "-S", "13389716"])

    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("elipy2.p4.LOGGER.isEnabledFor", MagicMock(return_value=False))
    @patch("marshal.load", MagicMock(side_effect=EOFError))
    @patch("subprocess.Popen")
    @patch("subprocess.PIPE", -1)
    @patch("subprocess.STDOUT", -2)
    def test_p4_executable_exists(self, mock_popen):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        output = p4._p4(["test"])
        mock_popen.assert_called_once_with(
            [
                "C:\\Program Files\\Perforce\\p4.exe",
                "-G",
                "-r3",
                "-vnet.maxwait=60",
                "-p",
                "random-perforce.server",
                "-u",
                "random-user",
                "-c",
                "random-client",
                "test",
            ],
            stdin=None,
            stdout=-1,
            stderr=-2,
        )
        assert output == []

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.p4.LOGGER.isEnabledFor", MagicMock(return_value=False))
    @patch("marshal.load", MagicMock(side_effect=EOFError))
    @patch("subprocess.Popen")
    @patch("subprocess.PIPE", -1)
    @patch("subprocess.STDOUT", -2)
    def test_p4_executable_does_not_exist(self, mock_popen):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        output = p4._p4(["test"])
        mock_popen.assert_called_once_with(
            [
                "p4.exe",
                "-G",
                "-r3",
                "-vnet.maxwait=60",
                "-p",
                "random-perforce.server",
                "-u",
                "random-user",
                "-c",
                "random-client",
                "test",
            ],
            stdin=None,
            stdout=-1,
            stderr=-2,
        )
        assert output == []

    @patch("elipy2.LOGGER.error")
    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.p4.LOGGER.isEnabledFor", MagicMock(return_value=False))
    @patch("marshal.load", MagicMock(side_effect=ValueError))
    @patch("subprocess.Popen", MagicMock())
    @patch("subprocess.PIPE", -1)
    @patch("subprocess.STDOUT", -2)
    def test_p4_valueerror(self, mock_logger_error):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        with pytest.raises(ValueError):
            p4._p4(["test"])
        assert mock_logger_error.call_count == 2

    @patch("elipy2.LOGGER.error")
    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("elipy2.p4.LOGGER.isEnabledFor", MagicMock(return_value=False))
    @patch("marshal.load", MagicMock(side_effect=FileNotFoundError))
    @patch("subprocess.Popen", MagicMock())
    @patch("subprocess.PIPE", -1)
    @patch("subprocess.STDOUT", -2)
    def test_p4_filenotfounderror(self, mock_logger_error):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        with pytest.raises(FileNotFoundError):
            p4._p4(["test"])
        assert mock_logger_error.call_count == 2

    @patch("elipy2.p4.P4Utils._p4")
    def test_get_client(self, mock__p4):
        p4 = P4Utils("random-perforce.server", user="random-user", client="random-client")
        mock__p4.return_value = [
            {
                b"code": b"stat",
                b"Client": b"random-client",
                b"Update": b"2022/01/14 11:42:55",
                b"Access": b"2022/01/20 16:21:28",
                b"Owner": b"random-user",
                b"Host": b"random-host",
                b"Description": b"Created by random-user.\n",
                b"Root": b"d:\\P4\\kingston",
                b"Options": b"noallwrite noclobber nocompress unlocked nomodtime rmdir",
                b"SubmitOptions": b"submitunchanged",
                b"LineEnd": b"local",
                b"Stream": b"//dicestudio/kin/stage/kin-stage",
                b"ServerID": b"oh-p4edge-fb",
                b"View0": b"//dicestudio/kin/stage/kin-stage/... //random-client/...",
                b"Type": b"writeable",
                b"Backup": b"enable",
            }
        ]
        result = p4.get_client("random-client")
        assert result[0][b"Stream"] == b"//dicestudio/kin/stage/kin-stage"
