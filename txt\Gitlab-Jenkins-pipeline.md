Branch is hvu-cobra-6811-improving-deleter-tr
<PERSON> job is https://dice-build-jenkins.cobra.dre.ea.com/job/hvu-utility.build-deleter.Fb1-tr

Monitor this Gitlab URL https://gitlab.ea.com/dre-cobra/elipy/elipy2/-/commits/$branch until the latest pipeline is green
If the pipeline failed then re-iterate with : investigate -> fix -> commit+push (with auto-generate message, tried to have minimal length) -> monitor
When its green then navigate to the pipeline deploy-pre-release-to-prod-repo and looking for this log
Submitting /builds/dre-cobra/elipy/elipy2/dist/elipy2-version_xxxx-py3-none-any.whl to https://artifactory.eu.ea.com/artifactory/api/pypi/dreeu-pypi-local
Save the version_xxxx from that line
Navigate to this URL $Jenkins_job/configure
Looking for this line (similar not exact text since the version can be different) in the configuration
tnt\bin\fbcli\cli.bat x64 && C:\dev\ci\install-elipy.bat elipy_fb1.yml "elipy_script_version" "elipy_version">> D:\dev\logs\install-elipy.log 2>&1
Replace the elipy_version with version_xxxx then save the config
Navigate to $Jenkins_job/lastCompletedBuild/rebuild/parameterized and click Rebuild
Monitor the latest result from $Jenkins_job, if it's running then try to set the Description of it to the version_xxxx
When it finished check the result and re-iterate with investigate -> fix if there is error failure