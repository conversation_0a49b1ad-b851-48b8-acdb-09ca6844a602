image: registry.gitlab.ea.com/dre-cobra/container-images/python-dre-cobra:latest_37

default:
  id_tokens:
    VAULT_ID_TOKEN:
      aud: https://ess.ea.com
    VAULT_JWT_TOKEN:
      aud: https://gitlab.ea.com

stages:
 - test
 - deploy

variables:
  ARTIFACTORY_URL: https://artifactory.eu.ea.com/artifactory/api/pypi/dreeu-pypi-virtual/simple
  PYTHON_INDEX_REPO: "https://artifactory.eu.ea.com/artifactory/api/pypi/dreeu-pypi-local"
  VAULT_ADDR: "https://ess.ea.com"
  VAULT_AUTH_PATH: "auth/jwt/gitlab/login"
  VAULT_AUTH_ROLE: "gl-dre-cobra-elipy"
  VAULT_NAMESPACE: "cds-dre-prod"
  VAULT_RATE_LIMIT: 50
  PYTEST_TIMEOUT: 2

.artifactory2_auth: &artifactory2_auth
  variables:
    VAULT_AUTH_PATH: "jwt/gitlab"
    ARTIFACTORY_URL: https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated
    PYTHON_INDEX_REPO: https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated
    VAULT_SERVER_URL: "https://ess.ea.com"
  secrets:
    PYTHON_USER:
      vault: artifacts/automation/dre-pypi-federated/rw/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    PYTHON_PASSWORD:
      vault: artifacts/automation/dre-pypi-federated/rw/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false

elipy2-pylint-py3:
  stage: test
  script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - python -m pylint elipy2
  tags:
    - glaas-shared-k8s

elipy2-tox-pytest-py3_7:
  image: registry.gitlab.ea.com/dre-cobra/container-images/python-dre-cobra:latest_37
  stage: test
  script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - tox -e py37
  tags:
    - glaas-shared-k8s

elipy2-tox-pytest-py3_8:
  image: registry.gitlab.ea.com/dre-cobra/container-images/python-dre-cobra:latest_38
  stage: test
  script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - tox -e py38
  tags:
    - glaas-shared-k8s

elipy2-tox-pytest-py3_11:
  image: registry.gitlab.ea.com/dre-cobra/container-images/python-dre-cobra:latest_311
  stage: test
  script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - tox -e py311
  tags:
    - glaas-shared-k8s

elipy-scripts-pylint-py3:
  stage: test
  script:
    - export PYTHONPATH="$PWD:$PYTHONPATH"
    - git clone https://gitlab-ci-token:$<EMAIL>/dre-cobra/elipy/elipy-scripts.git
    - cd elipy-scripts
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - python3 -m pylint dice_elipy_scripts -r y
  tags:
    - glaas-shared-k8s

elipy2-test-py3:
  stage: test
  script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - python3 setup.py test
  coverage: /(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/
  tags:
    - glaas-shared-k8s

black:
  stage: test
  before_script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
  script:
    - python -m black -l 100 --diff .
    - python -m black -l 100 --check -q .
  tags:
    - glaas-shared-k8s

editorconfig-validation:
  stage: test
  before_script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
  script:
    - ec
  tags:
    - glaas-shared-k8s

test docs:
  stage: test
  script:
  - pip3 install -U -r requirements.txt
  - sphinx-apidoc -f -o docs/source/ elipy2 */tests
  - sphinx-build -b html ./docs/source/ public
  only:
  - branches
  except:
  - master

pages:
  stage: deploy
  script:
  - pip3 install -U -r requirements.txt
  - sphinx-apidoc -f -o docs/source/ elipy2 */tests
  - sphinx-build -b html ./docs/source/ public
  artifacts:
    paths:
    - public
  only:
  - master


.deploy_template:
  stage: deploy
  tags:
    - glaas-shared-k8s
  before_script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
    - source $CI_PROJECT_DIR/scripts/get_all_secrets.sh
  script:
    - |+
      echo "[distutils]
      index-servers = ${PYTHON_INDEX_REPO}
      [${PYTHON_INDEX_REPO}]
      repository = ${PYTHON_INDEX_REPO}
      username = ${PYPI_ARTIFACTORY_USER}
      password = ${PYPI_ARTIFACTORY_PASSWORD}" >> ~/.pypirc
    - python3 setup.py bdist_wheel upload -r ${PYTHON_INDEX_REPO}

.deploy_af2_template:
  <<: *artifactory2_auth
  stage: deploy
  tags:
    - glaas-shared-k8s
  before_script:
    - pip3 install -i $ARTIFACTORY_URL -r requirements.txt
  script:
    - |+
      echo "[distutils]
      index-servers = ${PYTHON_INDEX_REPO}
      [${PYTHON_INDEX_REPO}]
      repository = ${PYTHON_INDEX_REPO}
      username = ${PYTHON_USER}
      password = ${PYTHON_PASSWORD}" >> ~/.pypirc
    - python3 setup.py bdist_wheel upload -r ${PYTHON_INDEX_REPO}

deploy-pre-release-to-prod-repo:
  extends: .deploy_template
  except:
    - master
  variables:
    IS_PRERELEASE: "true"

deploy-release-to-prod-repo:
  extends: .deploy_template
  only:
    - master
  variables:
    IS_PRERELEASE: "false"

deploy-pre-release-to-af2-prod-repo:
  extends: .deploy_af2_template
  except:
    - master
  variables:
    IS_PRERELEASE: "true"

deploy-release-to-af2-prod-repo:
  extends: .deploy_af2_template
  only:
    - master
  variables:
    IS_PRERELEASE: "false"
