# Mandatory
default:
  name: "frostbite"
  script_path:
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "frostbite"
  studio_location: "frostbite"
  avalanche_symbol_server: ""
  symbol_stores_share: ""
  symbol_stores_suffix: ""
  vault_symstore: "false"
  vault_destination: ""
  vault_verification_config_path: ""
  md5_exf_path: ""
  ant_local_dir: ""
  build_share: ""
  enable_retry: false
  enable_executed_command_log: true
  enable_threshold_clean: false

  p4_package_server: "ssl:1318.p4one.ea.com:1999"
  hailstorm_server: "eac-fb-hstorm02.eac.ad.ea.com"

  gamescripts_script_path: ""
  skip_increment_client_version: true

  bilbo_api_version: 2
  bilbo_url: ""
  shift_tool_url: "https://artifacts.at.ea.com/artifactory/list/dreeu-generic-local/shiftsubmission/4.0.1"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://dice-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  overlord_url: "https://overlord.apps.prod.frosting.ea.com"
  filefusion_url: "https://filefusion-file-fusion.apps.prod.frosting.ea.com"

  game_binaries: ['ExampleGameTrail.exe', 'ExampleGame.exe']

  elsa_patch: "false"
  use_onefs_api: "false"
  skip_frosty_game_config_flags: "true"
  shift_config_file: "shift_config_exc.yml"
  shift_retention: 100
  release_candidate_retention: 2 # 2 CLs
  shift_submission_path: "\\\\eamobile.ad.ea.com\\sports\\DREBUILDS\\fc\\devg5\\skybuild\\Shift\\submit"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'nant.exe'
    - 'devenv.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'cl.exe'
    - 'animationapp.exe'
    - 'prospero-clang.exe'
    - 'eapm.exe'

  secrets:
    # Get Roboto server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/excalibur/certificates'
          key: 'NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'

  path_retention:
    - \\eamobile.ad.ea.com\sports\DREBUILDS\fc\devg5\skybuild\Shift\submit: 40

na:
  build_share: "\\\\eac-fs201\\qdsbuilds\\ado\\code\\1"
  hailstorm_server: "eac-fb-hstorm02.eac.ad.ea.com"
  avalanche_symbol_server: ********** #ensemble
  avalanche_state_host:
    win64: **********
    ps4: **********
    ps5: **********
    xb1: **********
    xbsx: **********
    linux64: **********
    nx: **********
    android: **********
    ios: **********
    win64game: **********
    win64server: **********
    server: **********
  overlord_url: "https://overlord.apps.prod.frosting.ea.com"
  filefusion_url: "https://filefusion-file-fusion.apps.prod.frosting.ea.com"

EAV:
  build_share: "\\\\eac-fs201\\qdsbuilds\\ado\\code\\1"
  hailstorm_server: "eac-fb-hstorm02.eac.ad.ea.com"
  avalanche_symbol_server: ********** #ensemble
  avalanche_state_host:
    win64: **********
    ps4: **********
    ps5: **********
    xb1: **********
    xbsx: **********
    linux64: **********
    nx: **********
    android: **********
    ios: **********
    win64game: **********
    win64server: **********
    server: **********
  overlord_url: "https://overlord.apps.prod.frosting.ea.com"
  filefusion_url: "https://filefusion-file-fusion.apps.prod.frosting.ea.com"

eu:
  build_share: "" # to override later
  hailstorm_server: "fb-hailstorm.dice.ad.ea.com"
  avalanche_symbol_server: ************  #ensemble
  avalanche_state_host:
    win64: ************
    ps5: ************
    ps4: ************
    xbsx: ************
    xb1: ************
    nx: ************
    linux64: ************
    android: ************
    ios: ************
    win64game: ************
    win64server: ************
    server: ************
  overlord_url: ""
  filefusion_url: ""

EAS:
  build_share: "" # to override later
  hailstorm_server: "fb-hailstorm.dice.ad.ea.com"
  avalanche_symbol_server: ************  #ensemble
  avalanche_state_host:
    win64: ************
    ps5: ************
    ps4: ************
    xbsx: ************
    xb1: ************
    nx: ************
    linux64: ************
    android: ************
    ios: ************
    win64game: ************
    win64server: ************
    server: ************
  overlord_url: ""
  filefusion_url: ""
