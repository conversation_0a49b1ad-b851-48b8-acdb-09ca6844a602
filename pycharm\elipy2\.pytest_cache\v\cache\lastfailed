{"elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_list_value": true, "elipy2/tests/test_expire.py::TestExpire::test_keep_n_at_path_dry_run": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_dict_format": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_string_format": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_empty_list_value": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_invalid_count_values": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_invalid_string_format": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_mixed_formats": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_case_insensitive": true, "elipy2/tests/test_expire.py::TestExpire::test_get_retention_count_for_path_exception_handling": true, "elipy2/tests/test_expire.py::TestExpire::test_save_tagged_builds": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_delete_build": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_all": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_call": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_call_failure": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes_NoneAttributes": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_dump_attributes_retry": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_with_query": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearch::test_get_aggregations": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearchMetadataProvider::test_dump_attributes": true, "elipy2/tests/test_bilbo.py::TestBilboElasticSearchMetadataProvider::test_dump_attributes_NoneAttributes": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_code_build": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles_no_path": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_bundles_non_default_bundle_type": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_set_attribute": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_as_used": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_no_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_no_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_build_ant": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_ant_build_no_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_tnt_local_ant_build_no_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_avalanche_state": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_state": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_web_export": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols_build_no_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_symbols_build_no_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked_data": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_code_build_as_smoked_fail": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_build_as_release_candidate": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_drone_build": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_with_combine_params": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_with_partial_combine_params": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_data_smoke": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_verified_data": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_no_matching_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_method": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_path": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_tag_data_only_as_smoked_missing_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test0]": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test1]": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test2]": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test3]": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_without_required_parameters_raises_ELIPYException[args_under_test4]": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_baseline_build_calls__register_build_when_parameters_meet_reqs": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_data_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_code_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_data_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_code_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_dataset": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_platform": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_config": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_frosty_build_no_pkgtype": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_run_first_time": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_update_test_status": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_category_exists_but_test_has_not_been_created": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_register_new_test_on_existing_test": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_fail": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_autotest_build_handle_race_condition": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_set_autotest_category_status": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_not_set_verified_data_block_no_update_bilbo": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_not_set_branch_changelist_no_update_bilbo": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_set_autotest_category_status_category_not_found": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_first_time": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_update_is_use_until": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_do_not_update": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_mark_build_as_in_use_until_handle_race_condition": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_data_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_data_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_code_cl": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_code_branch": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_platform": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_clone_host": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_register_clone_db_no_dest_db": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds_query_string_success": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_all_builds_query_string_not_found_handled": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_aggregations_success": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_aggregations_not_found": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_builds_with_query_success": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_build_by_id_success": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_builds_matching": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_last_successful": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_last_successful_no": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_get_build_ids": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path_empth": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_filter_by_path_one": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_dump_attributes_NoneAttributes": true, "elipy2/tests/test_bilbo.py::TestBilbo::test_dump_attributes_lower_case": true, "elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_code_build_exception": true, "elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_drone_build_data_changelist_exception": true, "elipy2/tests/test_bilbo.py::TestBilboWithConfig::test_register_drone_build_code_changelist_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_no_changelist_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_code_build_no_branch_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_data_changelist_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_code_changelist_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_data_branch_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_code_branch_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_drone_build_dataset_exception": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_data_branch": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_code_branch": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_data_cl": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_code_cl": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_bundles_fail_platform": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_retry_update_new_build": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_build_failures[register_func0]": true, "elipy2/tests/test_bilbo.py::TestBilboExceptions::test_register_build_failures[register_func1]": true}