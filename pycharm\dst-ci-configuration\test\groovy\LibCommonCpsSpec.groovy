import com.ea.exceptions.CobraException
import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.LibJobDsl
import javaposse.jobdsl.dsl.jobs.FreeStyleJob
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import support.Steps
import support.TestParent
import support.TestPipeLineContext

class LibCommonCpsSpec extends Specification {
    @Shared String CODE_CHANGELIST = '1234'
    @Shared String DATA_CHANGELIST = '5678'
    @Shared Steps steps

    static class ProjectClassWithP4Prefix {
        static String p4_countername_prefix = 'project_settings_p4_prefix'
    }

    static class ProjectClassWithoutP4Prefix {
        static String unused_variable
    }

    class BranchFilePlusP4PrefixPlusProjectP4Prefix {
        static Class project = ProjectClassWithP4Prefix
        static Map standard_jobs_settings = ['p4_countername_prefix': 'branch_p4_prefix']
    }

    class BranchFileMinusP4PrefixPlusProjectP4Prefix {
        static Class project = ProjectClassWithP4Prefix
        static Map standard_jobs_settings = [:]
    }

    class BranchFilePlusP4PrefixMinusProjectP4Prefix {
        static Class project = ProjectClassWithoutP4Prefix
        static Map standard_jobs_settings = ['p4_countername_prefix': 'branch_p4_prefix']
    }

    class BranchFileMinusP4PrefixMinusProjectP4Prefix {
        static Class project = ProjectClassWithoutP4Prefix
        static Map standard_jobs_settings = [:]
    }

    static class ProjectClassGetAdoTriggerCurlCmd {
        static String unused_variable
    }

    class BranchFileGetAdoTriggerCurlCmd {
        static Class project = ProjectClassGetAdoTriggerCurlCmd
        static Map preflight_settings = [
            'ado_organization'              : 'my-ado-organization',
            'ado_project'                   : 'my-ado-project',
            'ado_code_preflight_pipeline_id': '123',
            'ado_data_preflight_pipeline_id': '124',
        ]
    }

    void setupSpec() {
        Steps.metaClass.build = {
            return [
                buildVariables: [
                    code_changelist: CODE_CHANGELIST,
                    data_changelist: DATA_CHANGELIST,
                ]
            ]
        }
        Steps.metaClass.echo = {}
        Steps.metaClass.string = { Map args -> return args }
    }

    void setup() {
        GroovySpy(Steps, global: true)
        steps = GroovyMock(Steps)
    }

    void "getDataPreflightPlatformAssets: test we get the right assets"(String platform, List<String> expectedAssets) {
        given:
        def matrix = [
            [name: 'win64', platform: 'win64', assets: ['asset_1'], extra_label: ''],
            [name: 'server', platform: 'server', assets: ['asset_2'], extra_label: ''],
            [name: 'xb1', platform: 'xb1', assets: ['asset_3'], extra_label: ''],
            [name: 'ps4', platform: 'ps4', assets: ['asset_4'], extra_label: ''],
            [name: 'xbsx', platform: 'xbsx', assets: ['asset_5'], extra_label: ''],
            [name: 'ps5', platform: 'ps5', assets: ['asset_6'], extra_label: ''],
        ]

        expect:
        LibCommonCps.getDataPreflightPlatformAssets(matrix, platform) == expectedAssets

        where:
        platform || expectedAssets
        'win64'  || ['asset_1']
        'server' || ['asset_2']
        'xb1'    || ['asset_3']
        'ps4'    || ['asset_4']
        'xbsx'   || ['asset_5']
        'ps5'    || ['asset_6']
    }

    @Unroll
    void "triggerDownstreamJobs: to call or not to call using #message"() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def context = new TestPipeLineContext(run, steps)
        def branchFile = BranchFileMinusP4PrefixMinusProjectP4Prefix

        when:
        LibCommonCps.triggerDownstreamJobs(context, downstreamMatrix, 'code', 'testBranch', branchFile, CODE_CHANGELIST, DATA_CHANGELIST)

        then:
        calls * steps.build(
            job: downstreamJobName,
            wait: false,
            parameters: downstreamJobArgs,
            propagate: false
        )

        where:
        downstreamMatrix || calls | downstreamJobName | downstreamJobArgs | message
        []               || 0     | ''                | []                | 'empty job list'
        [[name: 'job1']] || 1     | 'job1'            | []                | 'list with one job'
    }

    @Unroll
    void "triggerDownstreamJobs: basic functionality using #downstreamMatrix"() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def context = new TestPipeLineContext(run, steps)
        def branchFile = BranchFileMinusP4PrefixMinusProjectP4Prefix

        when:
        LibCommonCps.triggerDownstreamJobs(context, downstreamMatrix, 'code', 'testBranch', branchFile, CODE_CHANGELIST, DATA_CHANGELIST)

        then:
        1 * steps.build(
            job: downstreamJobName,
            wait: false,
            parameters: downstreamJobArgs,
            propagate: false
        )

        where:
        downstreamMatrix           || downstreamJobName | downstreamJobArgs
        [[name: 'job1']]           || 'job1'            | []
        [[name: 'job1']]           || 'job1'            | []
        [[name: '.job1']]          || 'testBranch.job1' | []
        [[name: 'job1', args: []]] || 'job1'            | []
    }

    @Unroll
    void 'triggerDownstreamJobs: different types of args for #jobType and #downstreamMatrix'() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def context = new TestPipeLineContext(run, steps)
        // Ensure branchFile p4_countername_prefix defaults to 'dice.'
        def branchFile = BranchFileMinusP4PrefixMinusProjectP4Prefix

        when:
        LibCommonCps.triggerDownstreamJobs(context, downstreamMatrix, jobType, 'testBranch', branchFile, CODE_CHANGELIST, DATA_CHANGELIST)

        then:
        codeClCalls * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'code_changelist', value: CODE_CHANGELIST)],
            propagate: false
        )
        codeCounterCalls * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'code_countername', value: 'dice.testBranch.lkg.' + jobType + '.code')],
            propagate: false
        )
        dataClCalls * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'data_changelist', value: DATA_CHANGELIST)],
            propagate: false
        )
        dataCounterCalls * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'data_countername', value: 'dice.testBranch.lkg.' + jobType + '.data')],
            propagate: false
        )
        regionCalls * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'region', value: 'ww')],
            propagate: false
        )
        mirrorCalls * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'data_changelist', value: CODE_CHANGELIST)],
            propagate: false
        )

        where:
        downstreamMatrix                              | jobType  || codeClCalls | codeCounterCalls | dataClCalls | dataCounterCalls | regionCalls | mirrorCalls
        [[name: 'job1', args: ['code_changelist']]]   | 'code'   || 1           | 0                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['code_countername']]]  | 'code'   || 0           | 1                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['data_changelist']]]   | 'code'   || 0           | 0                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['data_countername']]]  | 'code'   || 0           | 0                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['region']]]            | 'code'   || 0           | 0                | 0           | 0                | 1           | 0
        [[name: 'job1', args: ['mirror_changelist']]] | 'code'   || 0           | 0                | 0           | 0                | 0           | 1
        [[name: 'job1', args: ['code_changelist']]]   | 'data'   || 1           | 0                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['code_countername']]]  | 'data'   || 0           | 1                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['data_changelist']]]   | 'data'   || 0           | 0                | 1           | 0                | 0           | 0
        [[name: 'job1', args: ['data_countername']]]  | 'data'   || 0           | 0                | 0           | 1                | 0           | 0
        [[name: 'job1', args: ['region']]]            | 'data'   || 0           | 0                | 0           | 0                | 1           | 0
        [[name: 'job1', args: ['code_changelist']]]   | 'frosty' || 1           | 0                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['code_countername']]]  | 'frosty' || 0           | 1                | 0           | 0                | 0           | 0
        [[name: 'job1', args: ['data_changelist']]]   | 'frosty' || 0           | 0                | 1           | 0                | 0           | 0
        [[name: 'job1', args: ['data_countername']]]  | 'frosty' || 0           | 0                | 0           | 1                | 0           | 0
        [[name: 'job1', args: ['region']]]            | 'frosty' || 0           | 0                | 0           | 0                | 1           | 0
    }

    @Unroll
    void "triggerDownstreamJobs: region args for different cases"() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def context = new TestPipeLineContext(run, steps)
        def branchFile = BranchFileMinusP4PrefixMinusProjectP4Prefix

        when:
        LibCommonCps.triggerDownstreamJobs(context, downstreamMatrix, 'code', 'testBranch', branchFile, CODE_CHANGELIST, DATA_CHANGELIST)

        then:
        1 * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'region', value: region)],
            propagate: false
        )

        where:
        downstreamMatrix                                  || region
        [[name: 'job1', args: ['region']]]                || 'ww'
        [[name: 'job1', args: ['region'], region: 'IRT']] || 'IRT'
    }

    void "triggerDownstreamJobs: multiple downstream jobs"() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def context = new TestPipeLineContext(run, steps)
        def branchFile = BranchFileMinusP4PrefixMinusProjectP4Prefix

        List downstreamMatrix = [
            [name: 'job1', args: ['code_changelist']],
            [name: 'job2', args: ['data_changelist']],
        ]

        when:
        LibCommonCps.triggerDownstreamJobs(context, downstreamMatrix, 'data', 'testBranch', branchFile, CODE_CHANGELIST, DATA_CHANGELIST)

        then:
        1 * steps.build(
            job: 'job1',
            wait: false,
            parameters: [steps.string(name: 'code_changelist', value: CODE_CHANGELIST)],
            propagate: false
        )
        1 * steps.build(
            job: 'job2',
            wait: false,
            parameters: [steps.string(name: 'data_changelist', value: DATA_CHANGELIST)],
            propagate: false
        )
    }

    @Unroll
    void 'triggerDownstreamJobs: test patchdata job type'() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def context = new TestPipeLineContext(run, steps)
        def branchFile = BranchFileMinusP4PrefixMinusProjectP4Prefix

        List downstreamMatrix = [
            [name: 'job1', args: ['code_changelist', 'data_changelist']],
            [name: 'job2', args: ['code_countername', 'data_countername']],
        ]

        when:
        LibCommonCps.triggerDownstreamJobs(context, downstreamMatrix, 'patchdata', 'testBranch', branchFile, CODE_CHANGELIST, DATA_CHANGELIST)

        then:
        1 * steps.build(
            job: 'job1',
            wait: false,
            parameters: [
                steps.string(name: 'code_changelist', value: CODE_CHANGELIST),
                steps.string(name: 'data_changelist', value: DATA_CHANGELIST)
            ],
            propagate: false
        )
        1 * steps.build(
            job: 'job2',
            wait: false,
            parameters: [
                steps.string(name: 'code_countername', value: 'dice.testBranch.lkg.patchdata.code'),
                steps.string(name: 'data_countername', value: 'dice.testBranch.lkg.patchdata.data')
            ],
            propagate: false
        )
    }

    @Unroll
    void 'GetP4Countername: different args for #targetBranch, #jobType, #counterType, #branchFile and #expectedResult'() {
        given:

        when:
        String result = LibCommonCps.getp4Countername(targetBranch, jobType, branchFile, counterType)

        then:

        result == expectedResult

        where:
        targetBranch   | jobType            | counterType | branchFile                                  | expectedResult
        'targetBranch' | 'lkg.clean.code'   | 'code'      | BranchFilePlusP4PrefixPlusProjectP4Prefix   | 'branch_p4_prefix.targetBranch.lkg.clean.code.code'
        'targetBranch' | 'lkg.clean.data'   | 'data'      | BranchFileMinusP4PrefixPlusProjectP4Prefix  | 'project_settings_p4_prefix.targetBranch.lkg.clean.data.data'
        'targetBranch' | '.lkg.clean.code.' | 'code'      | BranchFilePlusP4PrefixMinusProjectP4Prefix  | 'branch_p4_prefix.targetBranch.lkg.clean.code.code'
        'targetBranch' | '.lkg.clean.data.' | 'data'      | BranchFileMinusP4PrefixMinusProjectP4Prefix | 'dice.targetBranch.lkg.clean.data.data'
        'testBranch'   | 'lkg.clean.data'   | 'data'      | BranchFilePlusP4PrefixPlusProjectP4Prefix   | 'branch_p4_prefix.testBranch.lkg.clean.data.data'
        'testBranch'   | 'lkg.clean.data'   | 'data'      | BranchFileMinusP4PrefixPlusProjectP4Prefix  | 'project_settings_p4_prefix.testBranch.lkg.clean.data.data'
        'testBranch'   | 'lkg.clean.data'   | 'data'      | BranchFilePlusP4PrefixMinusProjectP4Prefix  | 'branch_p4_prefix.testBranch.lkg.clean.data.data'
        'testBranch'   | '.lkg.clean.code.' | 'code'      | BranchFileMinusP4PrefixMinusProjectP4Prefix | 'dice.testBranch.lkg.clean.code.code'
        'targetBranch' | 'lkg.clean.code'   | '.code.'    | BranchFilePlusP4PrefixPlusProjectP4Prefix   | 'branch_p4_prefix.targetBranch.lkg.clean.code.code'
        'targetBranch' | 'lkg.clean.data'   | '.data.'    | BranchFileMinusP4PrefixPlusProjectP4Prefix  | 'project_settings_p4_prefix.targetBranch.lkg.clean.data.data'
        'targetBranch' | '.lkg.clean.code.' | '.code.'    | BranchFilePlusP4PrefixMinusProjectP4Prefix  | 'branch_p4_prefix.targetBranch.lkg.clean.code.code'
        'targetBranch' | '.lkg.clean.data.' | '.data.'    | BranchFileMinusP4PrefixMinusProjectP4Prefix | 'dice.targetBranch.lkg.clean.data.data'
        'testBranch'   | 'lkg.clean.data'   | '.data.'    | BranchFilePlusP4PrefixPlusProjectP4Prefix   | 'branch_p4_prefix.testBranch.lkg.clean.data.data'
        'testBranch'   | 'lkg.clean.data'   | '.data.'    | BranchFileMinusP4PrefixPlusProjectP4Prefix  | 'project_settings_p4_prefix.testBranch.lkg.clean.data.data'
        'testBranch'   | 'lkg.clean.data'   | '.data.'    | BranchFilePlusP4PrefixMinusProjectP4Prefix  | 'branch_p4_prefix.testBranch.lkg.clean.data.data'
        'testBranch'   | '.lkg.clean.code.' | '.code.'    | BranchFileMinusP4PrefixMinusProjectP4Prefix | 'dice.testBranch.lkg.clean.code.code'
    }

    void "triggerAzureUploads: build_type not supported"() {
        given:
        def job = Mock(FreeStyleJob)
        def branch_name = 'testBranch'
        def platform = 'testPlatform'
        def config = 'testConfig'
        def azure_uploads_matrix = []

        when:
        LibCommonCps.triggerAzureUploads(job, 'unsupportedBuildType', branch_name, platform, config, azure_uploads_matrix)

        then:
        thrown(CobraException)
    }

    void "triggerAzureUploads: job not an instance of FreeStyleJob"() {
        given:
        def job = 'notAJobInstance'
        def build_type = 'code'
        def branch_name = 'testBranch'
        def platform = 'testPlatform'
        def config = 'testConfig'
        def azure_uploads_matrix = []

        when:
        LibCommonCps.triggerAzureUploads(job, build_type, branch_name, platform, config, azure_uploads_matrix)

        then:
        thrown(CobraException)
    }

    void "triggerAzureUploads: azure_uploads_matrix is empty"() {
        given:
        def job = Mock(FreeStyleJob)
        def build_type = 'code'
        def branch_name = 'testBranch'
        def platform = 'testPlatform'
        def config = 'testConfig'
        def azure_uploads_matrix = []

        when:
        LibCommonCps.triggerAzureUploads(job, build_type, branch_name, platform, config, azure_uploads_matrix)

        then:
        // Verify that addAnyDownstreamJobToFreestyleJob was not called
        0 * LibJobDsl.addAnyDownstreamJobToFreestyleJob(_)
    }

    void "triggerAzureUploads: azure_upload matches build_type, platform, and config"() {
        given:
        GroovyMock(LibJobDsl, global: true)
        LibJobDsl.addAnyDownstreamJobToFreestyleJob(_, _) >> null
        def job = Mock(FreeStyleJob)
        def build_type = 'code'
        def branch_name = 'testBranch'
        def platform = 'testPlatform'
        def config = 'testConfig'
        def azure_uploads_matrix = [[
                                        build_type: build_type,
                                        platforms : [platform],
                                        configs   : [config]
                                    ]]
        when:
        LibCommonCps.triggerAzureUploads(job, build_type, branch_name, platform, config, azure_uploads_matrix)

        then:
        // Verify that addAnyDownstreamJobToFreestyleJob was called with expected arguments
        1 * LibJobDsl.addAnyDownstreamJobToFreestyleJob(job, "${branch_name}.${build_type}.${platform}.${config}.copy-build-to-azure")
    }

    void "get_ado_code_trigger_curl_cmd: test output"() {
        given:
        def buildUrl = 'https://my-jenkins.cobra.dre.ea.com/job/job_name/1'
        def jenkinsUrl = 'https://my-jenkins.cobra.ea.com'
        def buildId = '1'
        steps.env >> [
            BUILD_URL  : buildUrl,
            JENKINS_URL: jenkinsUrl,
            BUILD_ID   : buildId,
        ]
        def preflighter = 'test_user'
        def baseChangelist = '1234'
        def shelfChangelist = '1235'
        def adoToken = 'base64_encoded_string'

        def branchFile = BranchFileGetAdoTriggerCurlCmd
        def adoPipelineId = branchFile.preflight_settings.ado_code_preflight_pipeline_id
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            "curl -X POST -H 'Content-Type: application/json'",
            "-H 'Authorization: Basic ${adoToken}'",
            String.format(
                '-d \'{"templateParameters":{"BuildLink":"%s","JenkinsUrl":"%s","BuildId":"%s","Source":"%s","Preflighter":"%s","BaseChangelist":"%s","ShelfChangelist":"%s"}}\'',
                buildUrl, jenkinsUrl, buildId, 'Jenkins', preflighter, baseChangelist, shelfChangelist
            ),
            "https://dev.azure.com/my-ado-organization/my-ado-project/_apis/pipelines/${adoPipelineId}/runs?api-version=7.2-preview.1",
        ].join(' ')

        when:
        def result = LibCommonCps.get_ado_code_trigger_curl_cmd(steps, branchFile, adoToken, preflighter, shelfChangelist, baseChangelist)

        then:
        result == EXPECTED_RESULT
    }

    void "get_ado_data_trigger_curl_cmd: test output"() {
        given:
        def buildUrl = 'https://my-jenkins.cobra.dre.ea.com/job/job_name/1'
        def jenkinsUrl = 'https://my-jenkins.cobra.ea.com'
        def buildId = '1'
        steps.env >> [
            BUILD_URL  : buildUrl,
            JENKINS_URL: jenkinsUrl,
            BUILD_ID   : buildId,
        ]
        def preflighter = 'test_user'
        def baseChangelist = '1234'
        def codeChangelist = '1235'
        def shelfChangelist = '1236'
        def adoToken = 'base64_encoded_string'

        def branchFile = BranchFileGetAdoTriggerCurlCmd
        def adoPipelineId = branchFile.preflight_settings.ado_data_preflight_pipeline_id
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            "curl -X POST -H 'Content-Type: application/json'",
            "-H 'Authorization: Basic ${adoToken}'",
            String.format(
                '-d \'{"templateParameters":{"BuildLink":"%s","JenkinsUrl":"%s","BuildId":"%s","Source":"%s","Preflighter":"%s","BaseChangelist":"%s","PipelineChangelist":"%s","ShelfChangelist":"%s"}}\'',
                buildUrl, jenkinsUrl, buildId, 'Jenkins', preflighter, baseChangelist, codeChangelist, shelfChangelist
            ),
            "https://dev.azure.com/my-ado-organization/my-ado-project/_apis/pipelines/${adoPipelineId}/runs?api-version=7.2-preview.1",
        ].join(' ')

        when:
        def result = LibCommonCps.get_ado_data_trigger_curl_cmd(steps, branchFile, adoToken, preflighter, shelfChangelist, baseChangelist, codeChangelist)

        then:
        result == EXPECTED_RESULT
    }

    void "get_ado_curl_cmd: test output"() {
        given:
        def buildUrl = 'https://my-jenkins.cobra.dre.ea.com/job/job_name/1'
        def jenkinsUrl = 'https://my-jenkins.cobra.ea.com'
        def buildId = '1'
        steps.env >> [
            BUILD_URL  : buildUrl,
            JENKINS_URL: jenkinsUrl,
            BUILD_ID   : buildId,
        ]
        def adoToken = 'base64_encoded_string'
        def parametersMap = [:]

        def branchFile = BranchFileGetAdoTriggerCurlCmd
        def adoPipelineId = branchFile.preflight_settings.ado_code_preflight_pipeline_id
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            "curl -X POST -H 'Content-Type: application/json'",
            "-H 'Authorization: Basic ${adoToken}'",
            String.format(
                '-d \'{"templateParameters":{"BuildLink":"%s","JenkinsUrl":"%s","BuildId":"%s","Source":"%s"}}\'',
                buildUrl, jenkinsUrl, buildId, 'Jenkins'
            ),
            "https://dev.azure.com/my-ado-organization/my-ado-project/_apis/pipelines/${adoPipelineId}/runs?api-version=7.2-preview.1",
        ].join(' ')

        when:
        def result = LibCommonCps.get_ado_curl_cmd(steps, branchFile, adoToken, adoPipelineId, parametersMap)

        then:
        result == EXPECTED_RESULT
    }

    void "triggerAdoCodePreflights: test function call"() {
        given:
        GroovySpy(LibCommonCps, global: true)
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def env = [
            adoToken: 'my_ado_token'
        ]
        def context = new TestPipeLineContext(run, steps, env)//, env)
        def branchFile = BranchFileGetAdoTriggerCurlCmd

        steps.echo(_ as String) >> null
        steps.withCredentials(_ as List, _ as Closure) >> { args -> args[1]() }
        steps.wrap(_ as Map, _ as Closure) >> { args -> args[1]() }
        def encodedAdoPAT = ":${env.adoToken}".bytes.encodeBase64().toString()

        def curlCmd = 'curl_cmd'
        LibCommonCps.get_ado_code_trigger_curl_cmd(
            _ as Object, _ as Object, _ as String,
            _ as String, _ as String, _ as String
        ) >> curlCmd

        def preflighter = 'preflighter'
        def unshelveChangelist = '123'
        def baseChangelist = '124'

        when:
        LibCommonCps.triggerAdoCodePreflights(context, branchFile, preflighter, unshelveChangelist, baseChangelist)

        then:
        1 * LibCommonCps.get_ado_code_trigger_curl_cmd(
            context, branchFile, encodedAdoPAT,
            preflighter, unshelveChangelist, baseChangelist
        ) >> curlCmd
        1 * steps.sh(script: curlCmd, returnStdout: true)
    }

    void "triggerAdoDataPreflights: test function call"() {
        given:
        GroovySpy(LibCommonCps, global: true)
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        def env = [
            adoToken: 'my_ado_token'
        ]
        def context = new TestPipeLineContext(run, steps, env)//, env)
        def branchFile = BranchFileGetAdoTriggerCurlCmd

        steps.echo(_ as String) >> null
        steps.withCredentials(_ as List, _ as Closure) >> { args -> args[1]() }
        steps.wrap(_ as Map, _ as Closure) >> { args -> args[1]() }
        def encodedAdoPAT = ":${env.adoToken}".bytes.encodeBase64().toString()

        def curlCmd = 'curl_cmd'
        LibCommonCps.get_ado_data_trigger_curl_cmd(
            _ as Object, _ as Object, _ as String, _ as String, _ as String, _ as String, _ as String
        ) >> curlCmd

        def preflighter = 'preflighter'
        def unshelveChangelist = '123'
        def baseChangelist = '124'
        def codeChangelist = '124'

        when:
        LibCommonCps.triggerAdoDataPreflights(context, branchFile, preflighter, unshelveChangelist, baseChangelist, codeChangelist)

        then:
        1 * LibCommonCps.get_ado_data_trigger_curl_cmd(
            context, branchFile, encodedAdoPAT, preflighter, unshelveChangelist, baseChangelist, codeChangelist
        ) >> curlCmd
        1 * steps.sh(script: curlCmd, returnStdout: true)
    }

    void "configure_steam_upload_job: test return value - job_name"() {
        given:
        GroovyMock(LibJenkins, global: true)
        LibJenkins.getLastStableCodeChangelist(_) >> CODE_CHANGELIST
        LibJenkins.getLastStableDataChangelist(_) >> DATA_CHANGELIST

        when:
        String job_name = (LibCommonCps.configure_steam_upload_job(currentBranch, platform, branchInfo, variant, isPatch)).job_name

        then:
        job_name == jobName

        where:
        currentBranch  | platform | isPatch | branchInfo                                                    | variant                                                        | jobName
        'targetBranch' | 'ps5'    | true    | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'retail', 'region': 'ww', 'format': 'steam']        | 'targetBranch.ps5.upload_to_steam.patch.ww.retail'
        'targetBranch' | 'ps5'    | true    | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'final', 'region': 'eu', 'format': 'steam_combine'] | 'targetBranch.ps5.upload_to_steam.patch.combine.eu.final'
        'testBranch'   | 'win64'  | true    | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'retail', 'region': 'ww', 'format': 'steam']        | 'testBranch.win64.upload_to_steam.patch.ww.retail'
        'testBranch'   | 'win64'  | true    | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'final', 'region': 'eu', 'format': 'steam_combine'] | 'testBranch.win64.upload_to_steam.patch.combine.eu.final'
        'targetBranch' | 'xbsx'   | false   | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'retail', 'region': 'eu', 'format': 'steam']        | 'targetBranch.xbsx.upload_to_steam.eu.retail'
        'targetBranch' | 'xbsx'   | false   | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'final', 'region': 'ww', 'format': 'steam_combine'] | 'targetBranch.xbsx.upload_to_steam.combine.ww.final'
        'testBranch'   | 'server' | false   | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'retail', 'region': 'eu', 'format': 'steam']        | 'testBranch.server.upload_to_steam.eu.retail'
        'testBranch'   | 'server' | false   | [combine_bundles: [combine_reference_job: 'test.job.start'],] | ['config': 'final', 'region': 'ww', 'format': 'steam_combine'] | 'testBranch.server.upload_to_steam.combine.ww.final'
    }

    void "configure_steam_upload_job: test return value - steam_branch_info"() {
        given:
        GroovyMock(LibJenkins, global: true)
        LibJenkins.getLastStableCodeChangelist(_) >> CODE_CHANGELIST
        LibJenkins.getLastStableDataChangelist(_) >> DATA_CHANGELIST

        when:
        Object stream_branch_info = (LibCommonCps.configure_steam_upload_job(currentBranch, platform, branchInfo, variant, isPatch)).steam_branch_info

        then:
        stream_branch_info.combine_args.contains(' --upload-patch') == containsUploadPatch
        !stream_branch_info.combine_args.contains('None') == containsCombineArgs

        where:
        isPatch | variant                                                         | containsUploadPatch | containsCombineArgs
        true    | ['config': 'retail', 'region': 'ww', 'format': 'steam']         | true                | false
        true    | ['config': 'retail', 'region': 'ww', 'format': 'steam']         | true                | false
        false   | ['config': 'retail', 'region': 'ww', 'format': 'steam']         | false               | false
        false   | ['config': 'retail', 'region': 'ww', 'format': 'steam']         | false               | false
        true    | ['config': 'retail', 'region': 'ww', 'format': 'steam_combine'] | true                | true
        true    | ['config': 'retail', 'region': 'ww', 'format': 'steam_combine'] | true                | true
        false   | ['config': 'retail', 'region': 'ww', 'format': 'steam_combine'] | false               | true
        false   | ['config': 'retail', 'region': 'ww', 'format': 'steam_combine'] | false               | true

        platform = 'ps5'
        currentBranch = 'testBranch'
        branchInfo = [
            combine_bundles: [
                combine_reference_job: 'test.job.start',
            ],
        ]
    }
}
